import copy
import os

from django.utils.translation import gettext_lazy as _

from country_config.enums import Country
from env_secrets.v1.secrets_manager import SecretsManager
from env_secrets.v2.base_core_secret_provider_factory import get_base_secrets_provider
from lib.enums import SMSPaid<PERSON>num, SMSTypeEnum
from lib.utils import str_to_bool
from webapps.notification.enums import NotificationService, <PERSON><PERSON><PERSON>


def update_dict(instance, **kwargs):
    result = instance.copy()
    result.update(kwargs)
    return result


USE_NEW_SECRETS_MANAGER = str_to_bool(os.environ.get('USE_NEW_SECRETS_MANAGER', 'False'))
SecretsProvider = (
    get_base_secrets_provider() if USE_NEW_SECRETS_MANAGER else SecretsManager().provider
)


EVOXSMS_SETTINGS = {
    'service': NotificationService.EVOX,
    'api_login': SecretsProvider.read_secret('EVOXSMS_API_LOGIN'),
    'api_password': SecretsProvider.read_secret('EVOXSMS_API_PASSWORD'),
    'api_url': 'https://apic.evox.mobi/v2/sms',
    'from': 'BOOKSY',
}
EVOXSMS_SETTINGS_US = update_dict(EVOXSMS_SETTINGS, route_type='LONGCODE')

TWILIO_SETTINGS = {
    'service': NotificationService.TWILIO,
    'account_sid': SecretsProvider.read_secret('TWILIO_ACCOUNT_SID'),
    'auth_token': SecretsProvider.read_secret('TWILIO_AUTH_TOKEN'),
    'messaging_service_sid': SecretsProvider.read_secret('TWILIO_MESSAGING_SERVICE_SID'),
    'messaging_services': {
        'default': SecretsProvider.read_secret('TWILIO_MESSAGING_SERVICE_DEFAULT'),
        'invitations': SecretsProvider.read_secret('TWILIO_MESSAGING_SERVICE_INVITATIONS'),
        'registration_codes': SecretsProvider.read_secret(
            'TWILIO_MESSAGING_SERVICE_REGISTRATION_CODE'
        ),
    },
}
TWILIO_FROM_NUMBER_US = '+***********'
TWILIO_SETTINGS_US = update_dict(TWILIO_SETTINGS, from_number=TWILIO_FROM_NUMBER_US)

# For economic reasons, we decided to reduce
# usage of phone numbers and use those from the us profile
USE_TELNYX_US_PROFILES_IN_CA = True


TELNYX_SETTINGS_US = {
    'service': NotificationService.TELNYX,
    'api_key': SecretsProvider.read_secret('TELNYX_API_KEY'),
    'webhook_pubkey': SecretsProvider.read_secret('TELNYX_WEBHOOK_PUBKEY'),
}
# US doesn't allow (with minor exceptions) alphanumeric sender ID
TELNYX_SETTINGS = update_dict(TELNYX_SETTINGS_US, from_name='Booksy')

TELNYX_ROW_COUNTRIES_ID = '40017882-0191-426d-872e-ed54835994cd'
TELNYX_ROW_COUNTRIES_NAME = 'ROW countries'

TELNYX_SYSTEMS_ID = '40017589-1819-4a82-b789-863d81469c86'
TELNYX_SYSTEMS_NAME = 'System'

TELNYX_OTHER_ID = '40017589-18a5-4cd3-b24a-aa83eb88f2ea'
TELNYX_OTHER_NAME = 'Other'

TELNYX_PROFILES_PER_COUNTRY = {
    'ar': {
        'marketing': {
            'id': '400184c2-7f54-4631-9f36-91a1c350fb2b',
            'name': 'AR Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-7fc4-4ad0-aa09-760ff51f2655',
            'name': 'AR OTP Messages',
        },
        'system': {'id': '400184c2-8044-4bb7-b1e7-d9b7418edb30', 'name': 'AR System Messages'},
        'invitation': {
            'id': '400184c2-80bf-46ac-9bbf-49f9e5aea620',
            'name': 'AR Invitation Messages',
        },
    },
    'au': {
        'marketing': {
            'id': '400184c2-8128-4d8e-a168-e048c30ac7f6',
            'name': 'AU Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8185-457f-8a9b-dee158c8a4a9',
            'name': 'AU OTP Messages',
        },
        'system': {'id': '400184c2-8211-40df-b4ab-edc66219ae41', 'name': 'AU System Messages'},
        'invitation': {
            'id': '400184c2-827d-416c-ab93-7ab64f20b556',
            'name': 'AU Invitation Messages',
        },
    },
    'br': {
        'marketing': {
            'id': '400184c2-82e7-460e-a203-d391edce72b7',
            'name': 'BR Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8365-4d31-9024-0a679308c6ea',
            'name': 'BR OTP Messages',
        },
        'system': {'id': '400184c2-83d2-446f-a41c-9deeb05f1b02', 'name': 'BR System Messages'},
        'invitation': {
            'id': '400184c2-8438-4468-b02e-d544b5fe6963',
            'name': 'BR Invitation Messages',
        },
    },
    'ca': {
        'marketing': {
            'id': '400184c2-84b0-4910-b65c-31470af986da',
            'name': 'CA Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-850e-4b2e-98d7-8657e62323ac',
            'name': 'CA OTP Messages',
        },
        'system': {'id': '400184c2-8571-4450-bfc2-8cc9ea819701', 'name': 'CA System Messages'},
        'invitation': {
            'id': '400184c2-85c9-4c35-a99f-d65242a40b1e',
            'name': 'CA Invitation Messages',
        },
    },
    'cl': {
        'marketing': {
            'id': '400184c2-8627-4a75-bebf-0cf3be0cc151',
            'name': 'CL Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-867f-46ac-a01a-0ce91bfe8c37',
            'name': 'CL OTP Messages',
        },
        'system': {'id': '400184c2-86d7-4268-a82f-700639173b83', 'name': 'CL System Messages'},
        'invitation': {
            'id': '400184c2-8733-4e44-9233-dfc0692cf705',
            'name': 'CL Invitation Messages',
        },
    },
    'co': {
        'marketing': {
            'id': '400184c2-879a-429d-b06d-bf8739dc8720',
            'name': 'CO Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-87f8-4050-9ad7-94f9e633ed74',
            'name': 'CO OTP Messages',
        },
        'system': {'id': '400184c2-8853-4d1e-903c-a5886f08e81f', 'name': 'CO System Messages'},
        'invitation': {
            'id': '400184c2-88ac-4f6a-b5c5-26c191f993ff',
            'name': 'CO Invitation Messages',
        },
    },
    'es': {
        'marketing': {
            'id': '400184c2-8916-4c8c-a9a2-66a58b0e0e2a',
            'name': 'ES Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8977-4f66-a929-74486afb5e3f',
            'name': 'ES OTP Messages',
        },
        'system': {'id': '400184c2-89d1-4d66-bea6-05c57d06f177', 'name': 'ES System Messages'},
        'invitation': {
            'id': '400184c2-8a2c-4d8d-934b-52892313b17d',
            'name': 'ES Invitation Messages',
        },
    },
    'fr': {
        'marketing': {
            'id': '400184c2-8a9f-4b09-8ead-b2515581a472',
            'name': 'FR Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8aff-4187-93f7-786b60301c84',
            'name': 'FR OTP Messages',
        },
        'system': {'id': '400184c2-8b50-4370-a46f-eb768aeca629', 'name': 'FR System Messages'},
        'invitation': {
            'id': '400184c2-8bad-4c8e-bc9b-3ff84969d02d',
            'name': 'FR Invitation Messages',
        },
    },
    'gb': {
        'marketing': {
            'id': '400184c2-8c11-4e35-8431-d03935db1aa9',
            'name': 'GB Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8c98-48d2-899e-79429b64f348',
            'name': 'GB OTP Messages',
        },
        'system': {'id': '400184c2-8d03-4fea-ae19-d63b6b1a4b7d', 'name': 'GB System Messages'},
        'invitation': {
            'id': '400184c2-8d6c-4f34-846d-92abe4f684f2',
            'name': 'GB Invitation Messages',
        },
    },
    'ie': {
        'marketing': {
            'id': '400184c2-8dcd-4879-92dc-359c10b3b881',
            'name': 'IE Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8e27-4065-bb28-a5c8d2ee77a4',
            'name': 'IE OTP Messages',
        },
        'system': {'id': '400184c2-8e92-41a5-97a3-b34cdba56a20', 'name': 'IE System Messages'},
        'invitation': {
            'id': '400184c2-8ee5-48f9-99bd-4ea729d50d98',
            'name': 'IE Invitation Messages',
        },
    },
    'it': {
        'marketing': {
            'id': '400184c2-8f47-42aa-87fb-29e384e7eea6',
            'name': 'IT Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-8fa7-47f0-bdd8-4d633a6d58b8',
            'name': 'IT OTP Messages',
        },
        'system': {'id': '400184c2-8ff4-4da8-992a-7242d78243ef', 'name': 'IT System Messages'},
        'invitation': {
            'id': '400184c2-9049-43a1-b57a-4cfa965cfff2',
            'name': 'IT Invitation Messages',
        },
    },
    'mx': {
        'marketing': {
            'id': '400184c2-90b1-4773-9deb-d7d6b948cfbb',
            'name': 'MX Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-9105-41e7-8c93-ef679bddb708',
            'name': 'MX OTP Messages',
        },
        'system': {'id': '400184c2-915c-4997-8332-ca42cc126f42', 'name': 'MX System Messages'},
        'invitation': {
            'id': '400184c2-91b1-4b66-a686-3f50c9fd0981',
            'name': 'MX Invitation Messages',
        },
    },
    'pl': {
        'marketing': {
            'id': '400184c2-7a51-4698-aaf9-723785beb559',
            'name': 'PL Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-79ea-4b2a-b935-c2c66974f71f',
            'name': 'PL OTP Messages',
        },
        'system': {'id': '40018413-8d10-4725-b53c-b68ac8b41c63', 'name': 'PL System Messages'},
        'invitation': {
            'id': '400184c2-7b20-4f1e-9146-88b618bc7e39',
            'name': 'PL Invitation Messages',
        },
    },
    'pt': {
        'marketing': {
            'id': '400184c2-920d-4545-9302-dce7ea6d894c',
            'name': 'PT Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-9263-45e4-8b77-5223489d1dce',
            'name': 'PT OTP Messages',
        },
        'system': {'id': '400184c2-9331-4e0f-a5ff-819ecdee914a', 'name': 'PT System Messages'},
        'invitation': {
            'id': '400184c2-9395-4c6d-b17c-97dd5d6ed1cd',
            'name': 'PT Invitation Messages',
        },
    },
    'us': {
        'marketing': {
            'id': '400184c2-9434-4fab-ab72-f725fd2163ba',
            'name': 'US Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-9496-4af3-8820-e775cdaed7c0',
            'name': 'US OTP Messages',
        },
        'system': {'id': '400184c2-94fd-4c88-8400-8878469184f8', 'name': 'US System Messages'},
        'invitation': {
            'id': '400184c2-9569-4399-8f27-216f6e4e5564',
            'name': 'US Invitation Messages',
        },
    },
    'za': {
        'marketing': {
            'id': '400184c2-95d3-4e6c-857c-eddda0818060',
            'name': 'ZA Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-962f-498b-8daf-ff03cbd5845d',
            'name': 'ZA OTP Messages',
        },
        'system': {'id': '400184c2-9689-428c-85fc-bf02a42a5987', 'name': 'ZA System Messages'},
        'invitation': {
            'id': '400184c2-96f7-48c7-b35e-6d05a6027806',
            'name': 'ZA Invitation Messages',
        },
    },
    'default': {
        'marketing': {
            'id': '400184c2-976a-4b72-85cb-bdc46f70d4b8',
            'name': 'Default Marketing Messages',
        },
        'registration_code': {
            'id': '400184c2-97db-4d56-a173-c8fc72dd8d7e',
            'name': 'Default OTP Messages',
        },
        'system': {'id': '400184c2-9845-4ab3-8208-d5249472318d', 'name': 'Default System Messages'},
        'invitation': {
            'id': '400184c2-98b0-41c6-8276-7c25622a03cf',
            'name': 'Default Invitation Messages',
        },
    },
}

if USE_TELNYX_US_PROFILES_IN_CA:
    TELNYX_PROFILES_PER_COUNTRY['ca'] = copy.deepcopy(TELNYX_PROFILES_PER_COUNTRY['us'])


REGULATED_ALPHANUMERIC_SENDER_COUNTRIES = [Country.CA, Country.US]


TELNYX_COUNTRY_PROFILE_USAGE = {
    ProfileKey.MARKETING.value: [
        Country.AR,
        Country.AU,
        Country.BR,
        Country.CL,
        Country.CO,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.IE,
        Country.IT,
        Country.MX,
        Country.NL,
        Country.PL,
        Country.PT,
        Country.ZA,
    ],
    ProfileKey.INVITATION.value: [
        Country.AR,
        Country.AU,
        Country.BR,
        Country.CA,
        Country.CL,
        Country.CO,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.IE,
        Country.IT,
        Country.MX,
        Country.NL,
        Country.PL,
        Country.PT,
        Country.US,
        Country.ZA,
    ],
    ProfileKey.SYSTEM.value: [
        Country.AR,
        Country.AU,
        Country.BR,
        Country.CL,
        Country.CO,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.IE,
        Country.IT,
        Country.MX,
        Country.NL,
        Country.PL,
        Country.PT,
        Country.ZA,
    ],
    ProfileKey.REGISTRATION_CODE.value: [
        Country.AR,
        Country.AU,
        Country.BR,
        Country.CA,
        Country.CL,
        Country.CO,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.IE,
        Country.IT,
        Country.MX,
        Country.NL,
        Country.PL,
        Country.PT,
        Country.US,
        Country.ZA,
    ],
}


def _get_vonage_optional_rotating_credentials(secret_prefix: str) -> list[dict[str, str]]:
    rotating_credentials = []
    for key_no in range(1, 11):
        api_key = SecretsProvider.read_secret(f'{secret_prefix}VONAGE_API_KEY_ROTATING_{key_no}')
        api_secret = SecretsProvider.read_secret(
            f'{secret_prefix}VONAGE_API_SECRET_ROTATING_{key_no}'
        )

        if api_key and api_secret:
            rotating_credentials.append({'api_key': api_key, 'api_secret': api_secret})
    return rotating_credentials


def _get_country_vonage_profile_secrets(profile) -> dict:
    profile = profile.upper()

    if (country_api_key := SecretsProvider.read_secret(f'{profile}_VONAGE_API_KEY')) and (
        country_api_secret := SecretsProvider.read_secret(f'{profile}_VONAGE_API_SECRET')
    ):
        return {
            'api_key': country_api_key,
            'api_secret': country_api_secret,
            'rotating_credentials': [{'api_key': country_api_key, 'api_secret': country_api_secret}]
            + _get_vonage_optional_rotating_credentials(f'{profile}_'),
            'messaging_profile_name': f'Country {profile.capitalize()} Messages',
        }

    default_api_key = SecretsProvider.read_secret(f'DEFAULT_{profile}_VONAGE_API_KEY')
    default_api_secret = SecretsProvider.read_secret(f'DEFAULT_{profile}_VONAGE_API_SECRET')

    return {
        'api_key': default_api_key,
        'api_secret': default_api_secret,
        'rotating_credentials': [{'api_key': default_api_key, 'api_secret': default_api_secret}]
        + _get_vonage_optional_rotating_credentials(f'DEFAULT_{profile}_'),
        'messaging_profile_name': f'Default {profile.capitalize()} Messages',
    }


default_vonage_api_key = SecretsProvider.read_secret('VONAGE_API_KEY')
default_vonage_api_secret = SecretsProvider.read_secret('VONAGE_API_SECRET')

VONAGE_SETTINGS_US = {
    'service': NotificationService.VONAGE,
    'api_key': default_vonage_api_key,
    'api_secret': default_vonage_api_secret,
    'rotating_credentials': [
        {'api_key': default_vonage_api_key, 'api_secret': default_vonage_api_secret}
    ]
    + _get_vonage_optional_rotating_credentials(''),
}

VONAGE_COUNTRY_SECRETS = {
    ProfileKey.MARKETING: _get_country_vonage_profile_secrets('marketing'),
    ProfileKey.REGISTRATION_CODE: _get_country_vonage_profile_secrets('otp'),
    ProfileKey.SYSTEM: _get_country_vonage_profile_secrets('system'),
    ProfileKey.INVITATION: _get_country_vonage_profile_secrets('invitation'),
}
VONAGE_SETTINGS_US.update(country_secrets=VONAGE_COUNTRY_SECRETS)
VONAGE_SETTINGS = update_dict(VONAGE_SETTINGS_US, from_name='Booksy')

# Define which countries use rotating keys for each profile
VONAGE_ROTATING_CREDENTIALS_COUNTRIES = {
    ProfileKey.MARKETING.value: [
        Country.BR,
        Country.ES,
        Country.FR,
        Country.PL,
        Country.ZA,
    ],
    ProfileKey.INVITATION.value: [],
    ProfileKey.SYSTEM.value: [
        Country.BR,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.PL,
        Country.ZA,
    ],
    ProfileKey.REGISTRATION_CODE.value: [],
}


VONAGE_COUNTRY_EXLUDED_FROM_PROFILE_USAGE = [
    Country.CO,
    Country.US,
]
# If True then extended characters will be replaced by basic ones.
# Basic in this case mean supported by GSM 03.38,
# so polish "ą" is extended but german "ß" or greek capitals are basic.
# If text contain characters which cannot be replaced by basic characters
# (for example russian or asian) then original text is send.
# This is useful to reduce amount of sent SMSes, because one SMs can contain
# either 160 basic characters or 70 extended (unicode) ones.
SMS_DEDIACRIT = True


SMS_COST_PER_COUNTRY = {
    Country.US: (0.005, 'USD'),
    Country.PL: (0.1, 'PLN'),
    Country.GB: (0.03, 'GBP'),
    Country.IE: (0.04, 'EUR'),
    Country.BR: (0.12, 'BRL'),
    Country.ES: (0.04, 'EUR'),
    Country.ZA: (0.4, 'ZAR'),
    Country.AR: (5.0, 'ARS'),
    Country.MX: (1.0, 'MXN'),
    Country.CL: (35.0, 'CLP'),
    Country.CO: (149.0, 'COP'),
    Country.AU: (0.04, 'USD'),
    Country.PT: (0.04, 'EUR'),
    Country.FR: (0.07, 'EUR'),
}

DEFAULT_SMS_COST = (0.1, 'USD')

# Used in service.purchase.SubscriptionListingHandler.
# Price is set as text, because SMS policy per country
# may differ a lot and the safest way is to hardcode
# the text, instead of composing it from tuples like in SMS_COSTS.
SMS_COSTS_TEXT = {
    'us': _('%(price)s per message, 500 text messages for free'),
    'pl': _('Bespoke text messages plans, 50 text messages for free'),
}

# Countries in which businesses are able to use default monthly limits
COUNTRY__SMS_LIMITS_APPLY = ('us', 'ca', 'pl', 'gb', 'ie', 'br', 'za', 'es', 'fr')

# ACHTUNG: settings below are overwritten in local.py for PL, BR, ES, RoW
# RoW == all countries except 'us', 'ca', 'pl', 'gb', 'ie', 'br', 'za', 'es', 'fr'
SMS_DEMO_ACCOUNT_PREPAID_COUNT = 50
SMS_PAID_ACCOUNT_PREPAID_COUNT = 500
SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
    '2015-01': 50,
    '2015-11': SMS_PAID_ACCOUNT_PREPAID_COUNT,
}

# All sms limits can be set both for "trial"/"non-trial" merchants
# so we also divide settings for T ("trial", SMSPaidEnum.TRIAL)
# and P ("non-trial", SMSPaidEnum.NON_TRIAL)
# first n free sms counts per merchant per phone number

# ACHTUNG: In terms of first_n_sms_limits and total_m_sms_limits None always
# means "unlimited", so you should always take 0 as default!
# If you put None for both total_m_sms_limits and first_n_sms_limits then
# no limits will be taken into account (also the ones set in subscription!)

# In some countries SMS limits are moved to subscription, in some not (lol).
# Countries which don't have SMS limits moved to subscription, use values from
# settings both for trial and non-trial merchants. For those in which we have
# SMS limits moved to subscription, values from settings are treated as
# defaults for non-trial merchants.
COUNTRY__SMS_LIMITS_IN_SUBSCRIPTION = Country.supported() - {Country.US, Country.CA}


DEFAULT_FIRST_N_SMS = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: 6,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 0,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: 6,
        # ACHTUNG: There's 99,9% chance you want to keep that value as 0
        # even for US & CA. Setting it to None along with total_m_sms_limits
        # set to None means unlimited amount of blasts etc even if merchant has
        # bought a subscription with sms limit 50! Setting both
        # first_n_sms_limits and total_m_sms_limits to None allows to skip ALL
        # limits for given sms type.
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 0,
    },
}

N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: 6,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 0,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: 6,
        SMSTypeEnum.MARKETING: 0,
        # No limits at all (even from subscription)
        SMSTypeEnum.SYSTEM: None,
    },
}

TOTAL_M_SMS_50_INVITES_FOR_NEVER_PAID = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: 50,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    },
}

TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: 500,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    },
}

DEFAULT_TOTAL_M_SMS = TOTAL_M_SMS_50_INVITES_FOR_NEVER_PAID

# US & CA
ALL_N_INVITES_FOR_FREE_AND_UNLIMITED = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 0,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: None,
    },
}

# US & CA
ALL_M_UNLIMITED = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    },
}

# MX
N_SYSTEM_UNLIMITED_FOR_TRIAL_500_INVITES = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: 500,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 1000,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: 6,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: None,
    },
}

ALL_ZERO_LIMIT = {
    SMSPaidEnum.TRIAL: {
        SMSTypeEnum.INVITATION: 0,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 0,
    },
    SMSPaidEnum.NON_TRIAL: {
        SMSTypeEnum.INVITATION: 0,
        SMSTypeEnum.MARKETING: 0,
        SMSTypeEnum.SYSTEM: 0,
    },
}

WHITELIST_SMS = {
    '+1': [Country.US, Country.CA],
    '+27': [Country.ZA],
    '+34': [Country.ES],
    '+44': [Country.GB],
    '+48': [Country.PL],
    '+49': [Country.DE],
    '+52': [Country.MX],
    '+54': [Country.AR],
    '+55': [Country.BR],
    '+56': [Country.CL],
    '+57': [Country.CO],
    '+61': [Country.AU],
    '+351': [Country.PT],
    '+353': [Country.IE],
    '+33': [Country.FR],
    '+31': [Country.NL],
    # DOM-TOM countries
    '+596': ['mq'],
    '+590': ['gp', 'bl', 'mf'],
    '+594': ['gf'],
    '+262': ['re', 'yt'],
    # FR boarder prefixes
    '+376': ['ad'],  # Andora
    '+41': ['ch'],  # Switzerland
    '+32': ['be'],  # Belgium
    '+352': ['lu'],  # Luxembourg
}
WHITELIST_SMS_FOR_REGISTRATION_CODES = WHITELIST_SMS | {
    '+54': [Country.AR],  # Argentina Republic
    '+39': [Country.IT],  # Italy
    '+7': [Country.RU],  # Russia
    '+46': [Country.SE],  # Sweden
    '+65': [Country.SG],  # Singapore
    '+47': ['no'],  # Norway
    '+45': ['dk'],  # Denmark
    '+356': ['mt'],  # Malta
    '+357': ['cy'],  # Cyprus
    '+43': ['at'],  # Austria
    '+420': ['cz'],  # Czech Rep.
    '+40': ['ro'],  # Romania
    '+30': ['gr'],  # Greece
    '+380': ['ua'],  # Ukraine
    '+421': ['sk'],  # Slovakia
    '+36': ['hu'],  # Hungary
    '+370': ['lt'],  # Lithuania
    '+358': ['fi'],  # Finland
    '+354': ['is'],  # Iceland
    '+971': ['ae'],  # United Arab Emirates
    '+372': ['ee'],  # Estonia
    '+371': ['lv'],  # Latvia
    '+972': ['il'],  # Israel
    '+974': ['qa'],  # Qatar
    '+385': ['hr'],  # Croatia
    '+386': ['si'],  # Slovenia
    '+966': ['sa'],  # Saudi Arabia
    '+82': ['kr'],  # Korea S, Republic of
    '+81': ['jp'],  # Japan
    '+51': ['pe'],  # Peru
    '+56': [Country.CL],  # Chile
    '+91': [Country.IN],  # India
    '+60': [Country.MY],  # Malaysia
    '+234': [Country.NG],  # Nigeria
}

PREFIX_CODE_TO_COUNTRY_CODE = {}

for prefix_code, country_codes in WHITELIST_SMS_FOR_REGISTRATION_CODES.items():
    PREFIX_CODE_TO_COUNTRY_CODE[prefix_code] = {
        'countries': country_codes.copy(),
        'regions': country_codes.copy(),
    }

# DOM-TOM countries - have the same flag
PREFIX_CODE_TO_COUNTRY_CODE['+262']['regions'] = [Country.FR]
PREFIX_CODE_TO_COUNTRY_CODE['+590']['regions'] = [Country.FR]


SMS_SERVICES = {
    NotificationService.TELNYX: TELNYX_SETTINGS,
    NotificationService.EVOX: EVOXSMS_SETTINGS,
    NotificationService.VONAGE: VONAGE_SETTINGS,
}

SMS_SERVICES_US = {
    NotificationService.TELNYX: TELNYX_SETTINGS_US,
    NotificationService.EVOX: EVOXSMS_SETTINGS_US,
    NotificationService.VONAGE: VONAGE_SETTINGS_US,
}

SMS_SETTINGS_PER_COUNTRY = {
    'pl': {
        'country': 'Poland',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        # 50876
        'notification_sms_deduction': False,
        'first_n_sms_limits': DEFAULT_FIRST_N_SMS,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
    },
    'us': {
        'country': 'United States',
        'sms_services': SMS_SERVICES_US,
        'fallback_sms_service': NotificationService.TELNYX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': ALL_N_INVITES_FOR_FREE_AND_UNLIMITED,
        'total_m_sms_limits': ALL_M_UNLIMITED,
        'sms_prefix': 'Booksy: ',
        'marketing_night_adjust': {
            'night_start': '20:00',
            'night_end': '09:00',
        },
    },
    'gb': {
        'country': 'Great Britain',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': DEFAULT_FIRST_N_SMS,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
    },
    'ca': {
        'country': 'Canada',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.TELNYX,
        'sms_prefix': 'Booksy: ',
        'notification_sms_deduction': True,
        'first_n_sms_limits': ALL_N_INVITES_FOR_FREE_AND_UNLIMITED,
        'total_m_sms_limits': ALL_M_UNLIMITED,
    },
    'au': {
        'country': 'Australia',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'mx': {
        'country': 'Mexico',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_TRIAL_500_INVITES,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
    },
    'co': {
        'country': 'Colombia',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'cl': {
        'country': 'Chile',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'de': {
        'country': 'Germany',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'ar': {
        'country': 'Argentina',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'br': {
        'country': 'Brazil',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'pt': {
        'country': 'Portugal',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': DEFAULT_TOTAL_M_SMS,
    },
    'za': {
        'country': 'Republic of South Africa',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': DEFAULT_FIRST_N_SMS,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
    },
    'ie': {
        'country': 'Ireland',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': DEFAULT_FIRST_N_SMS,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
    },
    'es': {
        'country': 'Spain',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
    },
    'fr': {
        'country': 'France',
        'sms_services': SMS_SERVICES,
        'fallback_sms_service': NotificationService.EVOX,
        'notification_sms_deduction': True,
        'first_n_sms_limits': N_SYSTEM_UNLIMITED_FOR_NON_TRIAL_6_INVITES,
        'total_m_sms_limits': TOTAL_M_SMS_500_INVITES_FOR_NEVER_PAID,
        'marketing_night_adjust': {
            'night_start': '22:00',
            'night_end': '09:00',
        },
        # List of days which are excluded from marketing SMSs.
        # e.g. [0] - Monday, ..., [6] - Sunday
        'marketing_days_exclude': [6],
        'marketing_public_holidays_adjust': True,
    },
}

for country_code_, sms_settings_ in list(SMS_SETTINGS_PER_COUNTRY.items()):
    first_n_for_trial = sms_settings_.get('first_n_sms_limits', {}).get(SMSPaidEnum.TRIAL, {})
    first_n_for_non_trial = sms_settings_.get('first_n_sms_limits', {}).get(
        SMSPaidEnum.NON_TRIAL, {}
    )
    assert first_n_for_trial.get(SMSTypeEnum.MARKETING) is not None
    assert first_n_for_non_trial.get(SMSTypeEnum.MARKETING) is not None
    assert first_n_for_trial.get(SMSTypeEnum.SYSTEM) is not None

    total_m_for_non_trial = sms_settings_.get('total_m_sms_limits', {}).get(
        SMSPaidEnum.NON_TRIAL, {}
    )
    # No such feature for PAID_AT_LEAST_ONCE businesses (no info when to reset
    # and for which tz)
    assert total_m_for_non_trial.get(SMSTypeEnum.INVITATION) is None
    assert total_m_for_non_trial.get(SMSTypeEnum.MARKETING) is None
    assert total_m_for_non_trial.get(SMSTypeEnum.SYSTEM) is None

# Ticket 24446-sms-countries
# We want try sending registration SMSes to everybody,
# even if there is chance of failing
# If this will be set to None, then feature will be disabled
SMS_SETTINGS_DEFAULT = {
    'sms_services': SMS_SERVICES,
    'fallback_sms_service': NotificationService.TELNYX,
    'sms_prefix': 'Booksy: ',
    'marketing_night_adjust': {
        'night_start': '21:30',
        'night_end': '08:30',
    },
    # Set False or mark days in list that are excluded from marketing SMSs.
    # [0] - Monday, ..., [6] - Sunday
    'marketing_days_exclude': False,
    'marketing_public_holidays_adjust': False,
}

SMS_BUSINESS_MONTHLY_LIMITS_CHOICES = [0, 100, 250, 500, 1000, 2000]
SMS_SMSES_PER_PAGE = 100

# Countries in which businesses are NOT able to CHANGE their sms limit
# without CS support
COUNTRY__BIZ__SMS_LIMIT_CHANGE_BLOCKED = {
    'gb',
}

# Countries in which businesses are able to extend their sms limit
# without CS support
COUNTRY__BIZ__SMS_LIMIT_EXTEND = {
    'us',
    'ca',
    'pl',
}.difference(COUNTRY__BIZ__SMS_LIMIT_CHANGE_BLOCKED)

# Countries in which businesses are NOT able to EXTEND (increase)
# their sms limit without CS support
COUNTRY__BIZ__SMS_LIMIT_EXTEND_BLOCKED = Country.supported() - COUNTRY__BIZ__SMS_LIMIT_EXTEND

SMS_OPT_OUT_SUFFIX_COUNTRIES = ['us']

SMS_INVITE_PROFILE_COUNTRIES = ['ca', 'us']
