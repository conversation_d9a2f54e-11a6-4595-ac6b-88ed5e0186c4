{% if basket_status in ["success", "charged", "refunded"] %}
    {% set color='#44B560' %}{% set background='#ECF7EF' %}{# green #}
{% elif basket_status in ["call_for_payment", "authorized", "awaiting"] %}
    {% set color='#FFAB00' %}{% set background='#FFF6E5' %}{# orange #}
{% elif basket_status in ["failed", "chargeback"] %}
    {% set color='#FF1100' %}{% set background='#FFE7E5' %}{# red #}
{% elif basket_status in ["archived", "canceled"] %}
    {% set color='#8C8B88' %}{% set background='#F4F4F3' %}{# gray #}
{% else %}
    {% set color='#FFAB00' %}{% set background='#FFF6E5' %}{# orange #}
{% endif %}

{% set color_epsilon='#605F5D' %}
{% set color_beta='#8c8b88' %}

    <div style="background-color:#f9f9f9; width: 100%;  color: #383734; font-family: 'Proxima Nova', Arial; font-size: 11px; line-height: 18px; font-weight: 400;">
    <center>
        <div style="padding: 15px 15px 0px 15px; border-top: 2px solid #eee; border-left: 2px solid #eee; border-right: 2px solid #eee; background: #fff;">
            <table style="width: 100%; margin-bottom: 10px; color: {{ color_epsilon }};"
                   cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        <div style="">{{ business_details.business_name }}</div>
                        <div style="">{{ business_details.business_address }}</div>
                    </td>
                </tr>
            </table>

            <table style="width: 100%; padding-bottom: 10px;" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="background: #E1E1E1; height: 1px;"></td>
                </tr>
            </table>

            <table  style="width: 100%; color: {{ color_epsilon }};"  cellpadding="0" cellspacing="0">
                <tr>
                    <td style="padding-bottom: 15px; font-size: 11px">
{#                        {% if transaction.customer_data %}#}
{#                            <div style="">{{ transaction.customer_data.replace(', ', '<br/>') }}</div>#}
{#                        {% endif %}#}
                    </td>
                    <td style="vertical-align: top; text-align: right;">
                         <span>{{ created | date("DATE_FORMAT") }}</span>
                        <div>
                            <span style="line-height: 12px; display: inline-block; text-align: right; font-size: 1.1em; font-weight: 600; text-transform: uppercase; color: {{ color }}; border-radius: 2px; padding: 6px 0px 2px 0px; min-width: 100px;">
{#                                {{ short_status_label }}#}
                                TEST STATUS
                            </span>
                        </div>
                        <div>
                            <span style="font-size: 0.9em; color: #FFAB00;">
{#                                {% if short_status_description %}#}
{#                                    {{ short_status_description }}#}
{#                                {% endif %}&nbsp;#}
                            </span>
                        </div>
                    </td>
                </tr>
            </table>

    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <thead >
            <tr>
                <th style="width: 70%; text-align: left; font-weight: 400; font-size: 10px; border-bottom: 1px solid #E1E1E1; color: #c3c1bc; text-transform: uppercase;">
                    {{ _("Item") }}
                </th>
                <th style="width: 10%;  border-bottom: 1px solid #E1E1E1;">&nbsp;

                </th>
                <th style="text-align: right;width: 20%;  font-weight: 400; font-size: 10px; border-bottom: 1px solid #E1E1E1; color: #c3c1bc; text-transform: uppercase;">
                    {{ _("Amount") }}
                </th>
            </tr>
        </thead>
        <tbody>
        {% for item in basket_items %}
            <tr>
                <td style="text-align: left;  border-bottom: 1px solid #E1E1E1; padding: 10px 0; vertical-align: top">
                    {{ item.name_line_1 }}
                    {% if item.name_line_2 %}
                        <br><span style="color: #8c8b88; font-size: 11px;"> {{ item.name_line_2 }}</span>
                    {% endif %}
                </td>
                <td style="text-align: right; padding: 10px;  border-bottom: 1px solid #E1E1E1; vertical-align: top">
                    {% if item.quantity or item.formatted_item_price != item.formatted_total %}
                        {{ item.quantity }}x&nbsp;{{ item.formatted_item_price }}
                    {% endif %}
                </td>
                <td style="text-align: right; padding: 10px 0;  border-bottom: 1px solid #E1E1E1; vertical-align: top;">
                    {{ item.formatted_total }}
                </td>
            </tr>
        {% endfor %}
       </tbody>
    </table>

    {% if show_taxes_info %}
        <table  style="width: 100%; border-top: 1px solid #E1E1E1; margin-bottom: 15px; padding-top: 15px; text-align: right; text-transform: uppercase;"  cellpadding="0" cellspacing="0">
            <tr>
                <td style="color: #8c8b88;">
                </td>
                <td style="color: #8c8b88; width: 19%;">
                  {{ _('Tax rate') }}
                </td>
                <td style="color: #8c8b88; width: 19%; ">
                  {{ _('Net value') }}
                </td>
                <td style="color: #8c8b88; width: 19%;">
                  {{ _('Tax amount') }}
                </td>
                <td style="color: #8c8b88; width: 19%;">
                  {{ _('Gross value') }}
                </td>
            </tr>
            {% for tax in tax_summary %}
                <tr>
                    <td style=""></td>
                    <td>
                        {% if tax.tax_rate == None %}
                          {{ _('Tax free') }}
                        {% else %}
                          {{ tax.tax_rate }}%
                        {% endif %}
                    </td>
                    <td>{{ format_currency(tax.total_net_value)  }}</td>
                    <td>{{ format_currency(tax.total_tax_amount) }}</td>
                    <td><strong>{{ format_currency(tax.total_gross_value) }}</strong></td>
                </tr>
            {% endfor %}
        </table>
    {% endif %}

        <table  style="width: 100%; border-top: 1px solid #E1E1E1; padding: 15px 0 0 0;"  cellpadding="0" cellspacing="0">
            {% for summary in summaries %}
                <tr>
                    <td style="color: #8c8b88; text-align: right; padding: 0 10px 10px 10px; width: 80%; text-transform: uppercase">{{ summary.label }}</td>
                    <td style="text-align: right; padding: 0 0 10px 0; width: 20%;">{{ summary.text }}</td>
                </tr>
            {% endfor %}
        </table>


        <table  style="width: 100%; border-top: 1px solid #E1E1E1; margin-bottom: 15px; padding-top: 15px"  cellpadding="0" cellspacing="0">
            <tr>
                <td style="color: #8c8b88; text-align: right; padding: 0 10px 10px 10px; width: 80%; text-transform: uppercase;">
{#                    {% if latest_receipt_serialized.remaining %}#}
{#                        {{ _("Remaining") }}#}
{#                    {% else %}#}
                        {{ _("Total") }}
{#                    {% endif %}#}
                </td>
                <td style="text-align: right; padding: 0 0 10px 0; width: 20%; font-size: 20px;">
                156
{#                    {% if latest_receipt_serialized.remaining  %}#}
{#                        {{ latest_receipt_serialized.remaining  }}#}
{#                    {% else %}#}
{#                        {{ latest_receipt_serialized.total }}#}
{#                    {% endif %}#}
                </td>
            </tr>
        </table>

        <table style="width: 100%; padding: 15px 0; color: {{ color_beta }}" cellpadding="0" cellspacing="0">
            <tr>
                {% if show_fiscal_disclaimer %}
                <td>
                    {% if receipt.receipt_number %}
                    {{ _("Receipt") }}: {{ receipt.receipt_number }} •
                    {% endif %}
                    {{ created | date("DATETIME_FORMAT") }}
                </td>
                {% else %}
                <td>
                    {{ _("Receipt") }}: {{ receipt.assigned_number }}, ID: trx_id • {{ created | date("DATETIME_FORMAT") }}
                </td>
                {% endif %}
            </tr>
        </table>

        <table  style="width: 100%; padding: 15px 0; color: {{ color_beta }};"  cellpadding="0" cellspacing="0">
            {% for payment in basket_payments %}
                <tr style="margin-bottom: 5px;">
                    <td style="text-align: left; width: 60%; border-top: 1px solid #E1E1E1;">
                        {{ payment.status_formatted }} • {{ payment.label }} • {{ payment.created | date("DATETIME_FORMAT")}}
                    </td>
                    {% if payment.status == 'refunded' %}
                        <td style="text-align: right; width: 20%; border-top: 1px solid #E1E1E1; color: #f10;">
                            -{{ payment.amount_text }}
                        </td>
                    {% else %}
                        <td style="text-align: right; width: 20%; border-top: 1px solid #E1E1E1;">
                            {{ payment.amount_text }}
                        </td>
                    {% endif %}
                </tr>
            {% endfor %}
        </table>

        {% if receipt_footer_line_1 or receipt_footer_line_2  %}
            <table style="width: 100%; padding: 0 0 15px 0;" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="background: #E1E1E1; height: 1px;"></td>
                </tr>
            </table>

            <table style="width: 100%; padding: 5px 0;"
                   cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        {% if receipt_footer_line_1 %}
                            <div style="text-align: center; font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">{{ receipt_footer_line_1 }}</div>
                        {% endif %}
                        {% if receipt_footer_line_2 %}
                            <div style="text-align: center; font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">{{ receipt_footer_line_2 }}</div>
                        {% endif %}
                    </td>
                </tr>
            </table>
        {% endif %}

        <div>
            {% if show_fiscal_disclaimer %}
                <br/>
                <p style="font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">
                    {{ _('The above confirmation of sale is not a fiscal receipt.') }}
                </p>
            {% endif %}
        </div>
    </div><img src="{{ STATIC_FULL_URL }}scenarios/claws.png" style="width: 100%; vertical-align: top;" alt="">
    </center>
    </div>
