# pylint: disable=too-many-ancestors
import json
import logging
import re
from typing import Any, Literal, Optional

import responses
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext as _
from rest_framework.status import HTTP_200_OK, HTTP_401_UNAUTHORIZED

from lib.booksy_sms import parse_phone_number
from lib.db import READ_ONLY_DB, retry_on_sync_error, using_db_for_reads
from lib.feature_flag.feature import (
    AuthPasswordChangeRequiredFlag,
    CustomerQuickSignInUpFlag,
)
from lib.feature_flag.feature.booksy_med import CustomerLoginRestrictionForImport
from lib.feature_flag.feature.business import (
    AccountExistsBusinessMigrationFlag,
    AccountExistsAddProfileTypeFlag,
)
from lib.feature_flag.feature.security import (
    DisableCaptchaForAccountExistEndpointFlag,
    DisableDisposableEmailBlacklistBusinessFlag,
    HCaptchaBusinessFlag,
    HCaptchaBusinessForceFlag,
    HCaptchaFlag,
    HCaptchaForceFlag,
    ReCaptchaCustomerFlag,
)
from lib.feature_flag.killswitch import ReadOnlyDBLoginFlag
from lib.segment_analytics.enums import EventType
from lib.tools import (
    get_meta_data_from_handler,
    get_supported_language,
    quick_assert,
    tznow,
    validated_email,
)
from lib.validators import validate_email_domain_disposable
from service.business.hcaptcha import HCaptchaBusinessRequestValidator
from service.customer.hcaptcha import HCaptchaRequestValidator
from service.customer.recaptcha import RecaptchaRequestValidator
from service.exceptions import ServiceError
from service.mixins.throttling import BooksyScopedRateThrottle, ThrottleScopeEnum
from service.tools import (
    InvalidJWTError,
    JWTTokenType,
    RequestHandler,
    get_language_from_header,
    json_request,
    session,
    validate_user_jwt_token,
)
from webapps.admin_extra.models import SuperuserLoginEntry
from webapps.business.events import business_user_logged_in
from webapps.business.models import Resource
from webapps.notification.models import NotificationSMSCodes
from webapps.segment.consts import UserRoleEnum
from webapps.segment.tasks import (
    analytics_business_user_language_set,
    analytics_contact_preferences_updated_task,
    analytics_customer_registration_completed_task,
    analytics_customer_user_language_set,
)
from webapps.segment.utils import get_user_role_by_id
from webapps.session.booksy_auth import (
    BooksyAuthInvalidLoginException,
    BooksyAuthNonExistingUserException,
    LoginData,
    LoginResponseBooksyAuth,
)
from webapps.session.ports import SessionMediator
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import (
    ChangeEmailRequest,
    ChangePasswordRequest,
    EmailToken,
    UnsubscribedEmail,
    User,
    UserProfile,
)
from webapps.user.serializers import (
    BusinessAccountCreateSerializer,
    BusinessAccountSerializer,
    BusinessFacebookAccountCreateSerializer,
    CustomerAccountSerializer,
    CustomerCreateSerializer,
    CustomerFacebookAccountCreateSerializer,
    UserPasswordSerializer,
)
from webapps.user.tasks.sync import sync_user_booksy_auth
from webapps.versum_migration.user_connection.customer_registration import (
    versum_migration_handle_customer_registration_completed,
)

SAIYAN_EMAIL_RE = re.compile(r'^\+([0-9a-z.-]*?)\+(.*)$')

logger = logging.getLogger('booksy.account')


class ExtendedLogsRequestHandler(RequestHandler):
    @property
    def login_data(self) -> LoginData:
        return LoginData(
            email=self.data.get('email'),
            password=self.data.get('password'),
            fingerprint=self.fingerprint,
            profile_type=self.profile_type,
        )

    def _log_base(self, phrase, extra_phrase=None, extra_kwargs=None):
        if not extra_phrase:
            extra_phrase = ''
        if not extra_kwargs:
            extra_kwargs = {}
        recaptcha_request_validator = getattr(self, 'recaptcha_request_validator', None)
        if recaptcha_request_validator:
            extra_kwargs['recaptcha_valid'] = recaptcha_request_validator.recaptcha_valid
            extra_kwargs['recaptcha_score'] = recaptcha_request_validator.recaptcha_score
            extra_kwargs['x_real_ip'] = self.request.headers.get('X-Real-IP')

        login_data = self.login_data.get_logger_info()
        if cell_phone := self.data.get('cell_phone'):
            login_data += f', PHONE: {cell_phone}'

        logger.warning(
            msg=f'[LOGIN - {phrase}] {extra_phrase}',
            extra={
                'request_url': self.request.full_url(),
                'country_code': settings.API_COUNTRY,
                'metadata': get_meta_data_from_handler(self),
                'login_data': login_data,
                **extra_kwargs,
            },
        )

    def _log_password_change(self):
        self._log_base('PASSWORD CHANGE ATTEMPT')

    def _log_captcha_exception(
        self,
        captcha_provider: Literal[
            HCaptchaRequestValidator.SERVICE_NAME, RecaptchaRequestValidator.SERVICE_NAME
        ],
    ):
        self._log_base(f'{captcha_provider} VALIDATION FAILED')


class AccountRequestHandler(ExtendedLogsRequestHandler):
    """
    Abstract class for handling customer/business accounts.

    NOTE: We use some of ValidationMixin methods, but since
    service.tools.RequestHandler inherits from this class, we
    don't need to inherit from it here anymore.
    """

    def _get_profile_response_key(self, profile):
        """Ernest wanted to have different key for customer and biz owner."""
        if profile.profile_type == UserProfile.Type.CUSTOMER:
            return 'customer'
        return 'account'

    def no_access_token_response(self, profile: UserProfile, user: User, status=HTTP_200_OK):
        """Responds with :swaggerModels: ProfileDetailsResponse."""
        self.set_status(status)
        name = self._get_profile_response_key(profile)

        self.finish(
            json.dumps(
                {
                    name: user.format_account(profile),
                }
            )
        )

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    def with_access_token_response(
        self,
        profile,
        status=HTTP_200_OK,
        access_rights=None,
        password_change_required=None,
        first_login=None,
        connected_with='',
    ):
        """Responds with :swaggerModels: LoginResponse."""
        self.set_status(status)
        self.set_auth_origin_header()
        name = self._get_profile_response_key(profile)
        account = profile.format_account()
        result = {
            'access_token': self.session.session_key if self.session else None,
            'superuser': self.session and self.session.get('superuser'),
            name: account,
            'access_rights': access_rights,
            'password_change_required': password_change_required,
            'connected_with': connected_with,
        }
        if first_login is not None:
            result['first_login'] = first_login

        self.finish(json.dumps(result))

    @retry_on_sync_error
    @transaction.atomic
    def _validate_and_create_user(self, serializer_class, data, process_photo):
        serializer = serializer_class(
            data=data,
            context={
                'booking_source': self.booking_source,
                'process_photo': process_photo,
                'profile_type': self.profile_type,
                'language': self.language,
                'photo': data.get('photo'),
            },
        )
        self.validate_serializer(serializer)
        user = serializer.save()
        return user

    def create_user_from_data(
        self, data, profile_type=UserProfile.Type.CUSTOMER, process_photo=True, facebook=False
    ):
        serializer_class = None
        if profile_type == UserProfile.Type.CUSTOMER:
            if not facebook:
                serializer_class = CustomerCreateSerializer
            else:
                serializer_class = CustomerFacebookAccountCreateSerializer
        elif profile_type == UserProfile.Type.BUSINESS:
            if not facebook:
                serializer_class = BusinessAccountCreateSerializer
            else:
                serializer_class = BusinessFacebookAccountCreateSerializer
        if serializer_class is None:
            self.quick_error(
                ('invalid', None, 'profile_type'),
                _("Profile type %s unknown") % profile_type,
            )

        user = self._validate_and_create_user(
            serializer_class,
            data,
            process_photo,
        )

        return user

    def create_user_profile(self, user, photo=None):
        profile = UserProfile(
            user=user,
            profile_type=self.profile_type,
            language=self.language,
            source=self.booking_source,
        )

        if photo:
            from webapps.photo.models import Photo

            profile.photo = Photo.save_from_base64(photo)
        profile.save()
        if profile.photo_id:
            profile.migrate_photo_to_s3()

        return profile

    def update_user(self, user, data, profile_type=UserProfile.Type.CUSTOMER, access_rights=None):
        """Update user"""
        form_data = dict(data)
        form_data['user'] = user
        # form_data['profile_type'] = profile_type

        serializer_class = None
        if profile_type == UserProfile.Type.CUSTOMER:
            serializer_class = CustomerAccountSerializer
        elif profile_type == UserProfile.Type.BUSINESS:
            serializer_class = BusinessAccountSerializer
        if serializer_class is None:
            self.quick_error(
                ('invalid', None, 'profile_type'),
                _("Profile type %s unknown") % profile_type,
            )

        serializer = serializer_class(
            data=form_data,
            instance=user,
            context={
                'profile_type': profile_type,
                'photo': data.get('photo'),
                'booking_source': self.booking_source,
            },
        )

        self.validate_serializer(serializer)

        user = serializer.save()
        validated_data = serializer.validated_data

        user.save()

        # refresh the data, since Django doesn't do it well sometimes
        profile = user.profiles.get(profile_type=profile_type)

        if data.get('language') is not None:
            language = get_language_from_header(data['language'])
            profile.language = get_supported_language(language)
            profile.save()
            analytics_user_role = get_user_role_by_id(user.id)

            if analytics_user_role == UserRoleEnum.CUSTOMER:
                analytics_customer_user_language_set.delay(
                    profile_id=profile.id,
                    context={'session_user_id': user.id, 'source_id': self.booking_source.id},
                )
            else:
                analytics_business_user_language_set.delay(
                    profile_id=profile.id,
                    context={
                        'session_user_id': user.id,
                        'source_id': self.booking_source.id,
                        'event_type': EventType.USER,
                    },
                )

        if serializer.user_changes_email:
            old_email = user.email
            user.email = validated_data['email'].strip()
            user.initiate_email_change(
                renderer=self.render_string,
                receiver=old_email,
            )
        self.with_access_token_response(profile, access_rights=access_rights)

    @staticmethod
    def is_saiyan(original_email, password):
        """Check and authenticate superuser trying to log in as another user.

        Try to extract saiyan_name from the original_email
        and authenticate as user using saiyan's password.

        :param original_email: email with encoded superuser name in the form:
                               "+<saiyan_name>+<original_email>"
        :param password: password sent by the user
        :return: (
            saiyan: User object of superuser or None,
            saiyan_password_check: is superuser's password correct,
            email: email used for standard authentication,
        )

        """
        match = SAIYAN_EMAIL_RE.match(original_email)
        if not match:
            return None, None, original_email

        saiyan_name, user_email = match.groups()
        saiyan = User.objects.filter(
            email=f'{saiyan_name}@booksy.com',
            superuser=True,
        ).first()
        if saiyan is None:
            return None, None, original_email

        saiyan_password_check = saiyan.check_password(password)
        return saiyan, saiyan_password_check, user_email

    # pylint: disable=too-many-branches, too-many-statements
    def log_user_in(self):
        self._log_login_attempt()
        self.quick_assert(
            self.data.get('email') and self.data.get('password'),
            ('required', 'validation', None),
            _("Provided email or password is empty."),
        )
        if CustomerQuickSignInUpFlag():
            self.data['email'] = validated_email(self.data.get('email'), field='email')

        # TODO booksy_auth delete after migration
        if not self.new_login_turn_on:
            return self.django_log_user_in()
        if CustomerLoginRestrictionForImport():
            self.data['email'] = validated_email(self.data.get('email'), field='email')
        user = None
        try:
            response: LoginResponseBooksyAuth = self.booksy_auth_client.login_user(
                login_data=self.login_data,
            )
            if AuthPasswordChangeRequiredFlag():
                condition = not response.success and not response.password_change_required
            else:
                condition = not response.success
            if condition:
                self._log_login_auth_no_success()
                # fallback to old method
                self.django_log_user_in()
                sync_user_booksy_auth([self.session['user_id']], use_master=True)
                return None
            try:
                user = User.objects.get(id=response.country_user_id)
            except User.DoesNotExist:
                # user exists in auth but not in core fallback some how
                logger.warning('[LOGIN USER] USER_EMAIl: %s', self.data['email'])
        except BooksyAuthInvalidLoginException:
            self._log_login_attempt_bad_login_data()
            self.quick_error(
                ('not_valid', 'validation', None),
                _('Invalid email or password'),
            )
        except BooksyAuthNonExistingUserException:
            self._log_no_account()
            if self.user_exists(self.login_data.email):
                self.django_log_user_in()
                sync_user_booksy_auth([self.session['user_id']], use_master=True)
                return None

            self.quick_error(
                ('not_valid', 'validation', 'password'),
                _("Account doesn't exists"),
            )

        if user is None or not user.is_active:
            self._log_user_inactive()
            self.quick_error(
                ('not_valid', 'validation', 'email'),
                _("Your account has been deactivated"),
            )
        # make sure profile exist before check password
        profile, profile_created = user.profiles.get_or_create(
            profile_type=self.profile_type,
            defaults={
                'language': self.language,
                'source': self.booking_source,
            },
        )
        if profile_created and self.profile_type == UserProfile.Type.CUSTOMER:
            analytics_customer_registration_completed_task.delay(
                user_id=user.id,
                context={
                    'session_user_id': user.id,
                    'source_id': self.booking_source.id,
                },
            )
            versum_migration_handle_customer_registration_completed(user_id=user.id)
        if response.password_change_required:
            # Probably a widget user whose password was set to be empty
            # Send a password reminder
            user.initiate_password_change(
                profile_type=self.profile_type,
                booking_source=self.booking_source,
                language=self.language,
            )
            self._log_password_change()
            self.quick_error(
                ('not_valid', 'validation', 'password'),
                _("Your password was reset: please check your email"),
            )
        self.session = SessionMediator(
            session_key=response.session_key,
            new_login_turned_on=True,
        ).get_session()
        self.session.update(
            {
                'superuser': response.superuser_email or None,
                'user_id': response.country_user_id,
                'session_expire_date': response.expired,
                'origin': response.origin,
            }
        )

        if response.superuser_email:
            SuperuserLoginEntry(
                user_email=user.email,
                superuser_email=response.superuser_email,
            ).save()

        access_level = None
        if self.profile_type == UserProfile.Type.BUSINESS:
            # access_level from front-end doesn't matter:
            # access_level = self.data.get('access_level')
            # only data in db is source of truth
            # for more details on get_resource_
            # with_access_level see RequestHandler
            access_level = self.get_access_level(
                user,
            )

        access_rights = self.get_access_rights(
            self.profile_type,
            access_level,
        )

        first_login = user.last_login is None
        if not ReadOnlyDBLoginFlag():
            user.last_login = tznow()
            user.save(update_fields=['last_login'])

        self._user_logged_in(user_id=user.id)
        self.with_access_token_response(
            profile,
            access_rights=access_rights,
            password_change_required=user.password_change_required,
            first_login=first_login,
        )

    def user_exists(self, email):
        email = self.is_saiyan(original_email=email, password=None)[2]
        email = validated_email(email, field='email')
        return User.objects.filter(email=email).exists()

    def django_log_user_in(self):
        """Log in user"""

        saiyan, saiyan_password_check, email = self.is_saiyan(
            original_email=self.login_data.email,
            password=self.login_data.password,
        )
        email = validated_email(email, field='email')

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            user = None
            profile = None
        else:
            profile, created = user.profiles.get_or_create(
                profile_type=self.profile_type,
                defaults={
                    'language': self.language,
                    'source': self.booking_source,
                },
            )
            if created and profile.profile_type == UserProfile.Type.CUSTOMER:
                analytics_customer_registration_completed_task.delay(
                    user_id=user.id,
                    context={'session_user_id': user.id, 'source_id': self.booking_source.id},
                )
                versum_migration_handle_customer_registration_completed(user_id=user.id)

        if saiyan:
            check_password = saiyan_password_check
        else:
            check_password = user and user.check_password(self.login_data.password)

        if user and user.password == '!' and not check_password:
            # Probably a widget user whose password was set to be empty
            # Send a password reminder
            user.initiate_password_change(
                profile_type=self.profile_type,
                booking_source=self.booking_source,
                language=self.language,
            )

            self._log_password_change()
            self.quick_error(
                ('not_valid', 'validation', 'password'),
                _("Your password was reset: please check your email"),
            )

        if not user or not check_password:
            self._log_login_attempt_bad_login_data()
            self.quick_error(
                ('not_valid', 'validation', None),
                _("Invalid email or password"),
            )

        if not user.is_active:
            self._log_user_inactive()
            self.quick_error(
                ('not_valid', 'validation', 'email'),
                _("Your account has been deactivated"),
            )

        if user and check_password:  # always True
            first_login = user.last_login is None
            if not ReadOnlyDBLoginFlag():
                user.last_login = tznow()
                user.save(update_fields=['last_login'])

            if saiyan:
                SuperuserLoginEntry(
                    user_email=user.email,
                    superuser_email=saiyan.email,
                ).save()
            access_level = None
            if self.profile_type == UserProfile.Type.BUSINESS:
                # access_level from front-end doesn't matter:
                # access_level = self.data.get('access_level')
                # only data in db is source of truth
                # for more details on get_resource_
                # with_access_level see RequestHandler
                access_level = self.get_access_level(
                    user,
                )
            self.session = user.create_session(
                origin=AuthOriginEnum.BOOKSY,
                superuser_name=saiyan.email.split('@')[0] if saiyan else None,
                fingerprint=self.fingerprint,
            )
            access_rights = self.get_access_rights(
                self.profile_type,
                access_level,
            )
            self._user_logged_in(user_id=user.id)
            self.with_access_token_response(
                profile,
                access_rights=access_rights,
                password_change_required=user.password_change_required,
                first_login=first_login,
            )

    def get_access_rights(self, profile_type, access_level):
        access_rights = None
        if profile_type == UserProfile.Type.BUSINESS:
            if access_level in Resource.STAFF_ACCESS_LEVELS_ALL:
                access_rights = {
                    'access_level': access_level,
                }
            else:
                self.quick_error(
                    ('not_valid', 'validation', 'access_level'),
                    _("Invalid access_level"),
                )
            return access_rights

    def consume_sms_code(self):
        sms_code = self.data.get('sms_code')
        if not sms_code:
            return
        cell_phone = parse_phone_number(self.data.get('cell_phone'))
        NotificationSMSCodes.objects.filter(
            phone=cell_phone.db_format,
            sms_code=sms_code,
        ).update(consumed=tznow())

    def _user_logged_in(
        self,
        user_id: int,
        sender: Any = None,
        profile_type: Optional[str] = None,
    ) -> None:
        """
        Method called (for now) after successful login from:

            - /business_api/account/login/?
            - /business_api/account/login/facebook/?
            - /business_api/account/login/apple/?

            - /customer_api/account/login/?
            - /customer_api/account/login/facebook/?
            - /customer_api/account/login/apple/?

        Feel free to add other signals for different profile types,
        but be reasonable, it's a login endpoint after all.
        """
        self._log_success(user_id)
        if (profile_type or self.profile_type) == UserProfile.Type.BUSINESS:
            business_user_logged_in.send(sender or self.__class__, user_id=user_id)

    def _log_login_attempt(self):
        self._log_base('ATTEMPT')

    def _log_login_auth_no_success(self):
        self._log_base('BOOKSY AUTH NO SUCCESS')

    def _log_login_attempt_bad_login_data(self):
        self._log_base('BAD LOGIN DATA')

    def _log_no_account(self):
        self._log_base('NO ACCOUNT')

    def _log_user_inactive(self):
        self._log_base('USER INACTIVE')

    def _log_success(self, user_id):
        self._log_base(
            'LOGIN SUCCESS',
            f'User {user_id} logged in',
            extra_kwargs={'user_id': user_id},
        )


class AccountPasswordResetHandler(ExtendedLogsRequestHandler):
    """
    swagger:
        summary: request password reset
        notes:
            This requests a password reset action,
            which sends email with change password link. <br>
            No content is sent back in success response.
        parameters:
            - name: body
              paramType: body
              type: Email
              description: email of the registered account
        responseMessages:
            - code: 400
              message: Email is required
              responseModel: Errors
            - code: 400
              message: Provided email is not valid email address
              responseModel: Errors
    """

    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING, BooksyTeams.PROVIDER_ONBOARDING)
    throttle_scope = ThrottleScopeEnum.RESET_PASSWORD
    throttle_classes = [BooksyScopedRateThrottle]

    @json_request
    @session()
    def post(self):
        """Initiate password change
        inputs:
            - email: string
        """
        if (
            '/business_api/' in self.request.path
            and HCaptchaBusinessFlag()
            and (self.request.headers.get('x-hcaptcha-token') or HCaptchaBusinessForceFlag())
        ):
            HCaptchaBusinessRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            ).validate()

        if (
            (HCaptchaFlag() and HCaptchaForceFlag())
            or (self.request.headers.get('x-hcaptcha-token') and HCaptchaFlag())
        ) and '/customer_api/' in self.request.uri:
            self.hcaptcha_request_validator = HCaptchaRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            )

            try:
                self.hcaptcha_request_validator.validate()
            except ServiceError as exc:
                self._log_captcha_exception(captcha_provider=HCaptchaRequestValidator.SERVICE_NAME)
                raise exc
        elif ReCaptchaCustomerFlag() and '/customer_api/' in self.request.uri:
            self.recaptcha_request_validator = RecaptchaRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            )
            try:
                self.recaptcha_request_validator.validate()
            except ServiceError as exc:
                self._log_captcha_exception(captcha_provider=RecaptchaRequestValidator.SERVICE_NAME)
                raise exc

        self._log_password_change()

        email = validated_email(self.data.get('email'), field='email')
        user = User.objects.filter(email=email).first()

        # Do not signalize that email is invalid
        # https://redmine.booksy.pm/issues/62586
        if not user:
            self.set_status(HTTP_200_OK)
            self.finish({})
            return
        # ensure user profile exists
        user.profiles.get_or_create(
            profile_type=self.profile_type,
            defaults={
                'language': self.language,
                'source': self.booking_source,
            },
        )

        user.initiate_password_change(
            profile_type=self.profile_type,
            booking_source=self.booking_source,
            language=self.language,
        )
        self.set_status(HTTP_200_OK)
        self.finish({})


class JWTTokenValidationHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.VALIDATE_TOKEN
    throttle_classes = [BooksyScopedRateThrottle]

    @json_request
    @session(login_required=False)
    def post(self):
        token_type = self.data.get('token_type')

        try:
            validate_user_jwt_token(self.data.get('token'), token_type=token_type)
            self.finish_with_json(HTTP_200_OK, {})
        except InvalidJWTError:
            self.finish_with_json(HTTP_401_UNAUTHORIZED, {'token_error': 'Wrong token'})


class JWTAccountEmailResetHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.RESET_EMAIL
    throttle_classes = [BooksyScopedRateThrottle]

    @json_request
    @session(login_required=True)
    def post(self):
        request_data = {
            'user_agent': self.request.headers.get('User-Agent'),
            'fingerprint': self.request.headers.get('X-Fingerprint'),
            'ip': self.forwarded_ip,
        }

        self.user.initiate_jwt_email_change(
            profile_type=self.profile_type,
            request_data=request_data,
            language=self.language,
        )

        self.finish_with_json(HTTP_200_OK, {})


class JWTAccountEmailChangeHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @json_request
    @session(login_required=False)
    def put(self):
        try:
            decoded_token = validate_user_jwt_token(
                self.data.get('token'),
                token_type=JWTTokenType.EMAIL.value,
            )
        except InvalidJWTError:
            self.finish_with_json(HTTP_401_UNAUTHORIZED, {'token_error': 'Wrong token'})

        user = User.objects.get(email=decoded_token['iss'])

        self.quick_assert(
            not user.is_staff,
            ('invalid', 'validation', 'new_email'),
            _("Booksy Staff cannot change email"),
        )
        new_email = validated_email(
            self.data.get('new_email'),
            field='new_email',
            description_required=_('New email is required'),
        )
        self.quick_assert(
            not User.objects.filter(email=new_email).exists(),
            ('entity_exists', 'database', 'email'),
            _("Email already registered"),
        )

        with transaction.atomic():
            user.email = new_email
            user.save(update_fields=['email'])

            # ensure that the token will be used only once - set completed to True
            ChangeEmailRequest.objects.filter(nonce=decoded_token['nonce']).update(completed=True)

        self.finish_with_json(HTTP_200_OK, {})


class JWTAccountPasswordResetHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.RESET_PASSWORD
    throttle_classes = [BooksyScopedRateThrottle]

    @json_request
    @session(login_required=False)
    def post(self):
        if (
            ReCaptchaCustomerFlag() and '/customer_api/' in self.request.uri
        ):  # temporary ugly bypass
            RecaptchaRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            ).validate()

        email = validated_email(self.data.get('email'), field='email')
        user = User.objects.filter(email=email).first()

        if user:
            request_data = {
                'user_agent': self.request.headers.get('User-Agent'),
                'fingerprint': self.request.headers.get('X-Fingerprint'),
                'ip': self.forwarded_ip,
            }

            user.initiate_password_change(
                profile_type=self.profile_type,
                booking_source=self.booking_source,
                language=self.language,
                request_data=request_data,
                use_jwt=True,
            )

        self.finish_with_json(HTTP_200_OK, {})


class JWTAccountPasswordChangeHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @json_request
    @session(login_required=False)
    def put(self):
        try:
            decoded_token = validate_user_jwt_token(
                self.data.get('token'),
                token_type=JWTTokenType.PASSWORD.value,
            )
        except InvalidJWTError:
            self.finish_with_json(HTTP_401_UNAUTHORIZED, {'token_error': 'Wrong token'})

        password = self.data.get('new_password')

        serializer = UserPasswordSerializer(instance=self.user, data={'password': password})
        self.validate_serializer(serializer)

        with transaction.atomic():
            serializer.save()

            # ensure that the token will be used only once - set completed to True
            password_request = ChangePasswordRequest.objects.get(nonce=decoded_token['nonce'])
            password_request.completed = True
            password_request.save()

        self.finish_with_json(HTTP_200_OK, {})


class AccountPasswordChangeBaseHandler(RequestHandler):
    """
    swagger:
        summary: Changes current user password
        parameters:
            - name: body
              paramType: body
              type: PasswordChangeRequest
              description: email of the registered account
    """

    @json_request
    @session(login_required=True)
    def put(self):
        password = self.data.get('password')

        serializer = UserPasswordSerializer(instance=self.user, data={'password': password})
        self.validate_serializer(serializer)
        serializer.save()

        self.set_status(HTTP_200_OK)
        self.finish({})


class AccountPasswordChangeBusinessHandler(AccountPasswordChangeBaseHandler):
    __doc__ = AccountPasswordChangeBaseHandler.__doc__
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)


class AccountPasswordChangeCustomerHandler(AccountPasswordChangeBaseHandler):
    __doc__ = AccountPasswordChangeBaseHandler.__doc__
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)


class LogoutBaseHandler(RequestHandler):
    """Logout User.

    swagger:
        summary: Logout User
        notes: Request to this url (GET, POST, PUT, DELETE) logs user out. <br>
               Returns with 200 and empty json response.

    """

    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING, BooksyTeams.PROVIDER_ONBOARDING)
    SESSION_BUMP_DISABLED_FOR = [responses.GET, responses.POST, responses.PUT, responses.DELETE]

    def _delete_session(self):
        self.session.flush()

    @session(login_required=True)
    def get(self):
        self._delete_session()
        self.finish_with_json(HTTP_200_OK, {})

    @session(login_required=True)
    def post(self):
        self._delete_session()
        self.finish_with_json(HTTP_200_OK, {})

    @session(login_required=True)
    def put(self):
        self._delete_session()
        self.finish_with_json(HTTP_200_OK, {})

    @session(login_required=True)
    def delete(self):
        self._delete_session()
        self.finish_with_json(HTTP_200_OK, {})


class LogoutBusinessHandler(LogoutBaseHandler):
    __doc__ = LogoutBaseHandler.__doc__
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)


class LogoutCustomerHandler(LogoutBaseHandler):
    __doc__ = LogoutBaseHandler.__doc__
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)


class UnsubscribeHandler(RequestHandler):
    """Unsubscribe User from mailing lists.

    swagger:
        summary: Unsubscribe User
        notes: >
            Request to this url unsubscribes user from the mailing list. <br>
            No content is sent back in success response.
        parameters:
         - name: body
           paramType: body
           type: UnsubscribeRequest
           description: Generated token for request
    :swagger

    swaggerModels:
        UnsubscribeRequest:
            id: UnsubscribeRequest
            required:
              - token
            properties:
                token:
                    type: string
    :swaggerModels
    """

    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @session()
    @json_request
    def post(self):
        token = self.data.get('token')

        email_token = EmailToken.objects.filter(
            token=token,
            expiry_date__gt=tznow(),
        ).first()
        if email_token is None:
            self.quick_error(
                ('required', 'validation', 'token'),
                _("Token is invalid"),
            )

        # create UnsubscribedEmail
        UnsubscribedEmail.objects.get_or_create(email=email_token.email)

        user = User.objects.filter(email=email_token.email).values('id').first()
        if user:
            analytics_contact_preferences_updated_task.delay(
                user_id=user.get('id'),
                context={
                    'session_user_id': user.get('id'),
                    'source_id': self.booking_source.id,
                },
            )
        self.finish_with_json(201, None)


class CheckAccountExistsBaseHandler(RequestHandler):
    throttle_scope = ThrottleScopeEnum.CHECK_ACCOUNT_EXISTS
    throttle_classes = [BooksyScopedRateThrottle]

    @session(optional_login=True)
    def get(self, include_profile_type=False):
        """
        swagger:
            summary: Checks if account with given email exist
            type: AccountExistsResponse
            parameters:
                - name: email
                  description: Email address
                  type: string
                  paramType: query
        :swagger
        swaggerModels:
            AccountExistsResponse:
                id: AccountExistsResponse
                required:
                    - account_exists
                properties:
                    account_exists:
                        type: boolean
                    profile_type:
                        type: string
        """
        email = validated_email(
            self.get_argument('email', ''),
            field='email',
        )
        match = SAIYAN_EMAIL_RE.match(email)
        if match:
            _, email = match.groups()
        with using_db_for_reads(READ_ONLY_DB):
            user_qs = User.objects.filter(email=email)
            result = {'account_exists': user_qs.exists()}
            if AccountExistsAddProfileTypeFlag() and include_profile_type:
                result['profile_type'] = list(
                    user_qs.filter(profiles__profile_type__isnull=False).values_list(
                        'profiles__profile_type', flat=True
                    )
                )
            self.finish_with_json(HTTP_200_OK, result)


class CheckAccountExistsBusinessHandler(CheckAccountExistsBaseHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    __tornado_migration_feature_flag__ = AccountExistsBusinessMigrationFlag

    @session(optional_login=True)
    def get(self):
        if HCaptchaBusinessFlag() and (
            self.request.headers.get('x-hcaptcha-token') or HCaptchaBusinessForceFlag()
        ):
            HCaptchaBusinessRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            ).validate()

        email = self.get_argument('email', '')
        if DisableDisposableEmailBlacklistBusinessFlag() and validate_email_domain_disposable(
            email
        ):
            logger.warning('Email usage attempt from disposable domain: %s', email)
            quick_assert(
                False,
                ('invalid', 'validation', 'email'),
                _('Email not accepted. Please use a different email address.'),
                None,
            )

        super().get(include_profile_type=True)


class CheckAccountExistsCustomerHandler(CheckAccountExistsBaseHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @session(optional_login=True)
    def get(self):
        if not DisableCaptchaForAccountExistEndpointFlag():
            if (HCaptchaFlag() and HCaptchaForceFlag()) or (
                self.request.headers.get('x-hcaptcha-token') and HCaptchaFlag()
            ):
                HCaptchaRequestValidator(
                    booking_source_name=self.booking_source.name,
                    request=self.request,
                ).validate()
            elif ReCaptchaCustomerFlag() and '/customer_api/' in self.request.uri:
                RecaptchaRequestValidator(
                    booking_source_name=self.booking_source.name,
                    request=self.request,
                ).validate()

        super().get()
