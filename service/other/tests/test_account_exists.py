import json
from urllib.parse import urlencode

import pytest
from django.conf import settings
from mock import patch
from tornado.testing import AsyncHTTPTestCase

from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.booksy_med import CustomerLoginRestrictionForImport
from lib.feature_flag.feature.business import AccountExistsAddProfileTypeFlag
from lib.feature_flag.feature.security import (
    DisableCaptchaForAccountExistEndpointFlag,
    DisableDisposableEmailBlacklistBusinessFlag,
    ReCaptchaCustomerFlag,
    HCaptchaBusinessFlag,
    HCaptchaBusinessForceFlag,
    HCaptchaBusinessFrontdeskFlag,
)
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from service import run
from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import BookingSources
from webapps.consts import FRONTDESK
from webapps.user.models import User, UserProfile


class BaseSessionOnlyHTTPTestCase(AsyncHTTPTestCase):

    business_api_key = 'biz_key'
    customer_api_key = 'customer_key'
    content_type = 'application/json; charset=UTF-8'

    def setUp(self):
        self.business_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key=self.business_api_key,
        )
        self.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP, api_key=self.customer_api_key
        )
        super().setUp()

    @property
    def business_headers(self) -> dict:
        return {
            'Content-Type': self.content_type,
            'X-API-KEY': self.business_api_key,
        }

    @property
    def customer_headers(self) -> dict:
        return {
            'Content-Type': self.content_type,
            'X-API-KEY': self.customer_api_key,
        }

    def get_app(self):
        return run.tornado_app


@pytest.mark.django_db
class TestAccountExistsCustomerEndpoint(BaseSessionOnlyHTTPTestCase):

    url = f'/api/{settings.API_COUNTRY.lower()}/2/customer_api/account/exists/?'

    existing_email = '<EMAIL>'
    non_existing_email = '<EMAIL>'

    def setUp(self):
        baker.make_recipe('webapps.user.user_recipe', email=self.existing_email)
        super().setUp()

    def tearDown(self):
        User.objects.all().delete()

    def test_account_exists(self):
        params = {'email': self.existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.customer_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']

    @override_feature_flag({ReCaptchaCustomerFlag.flag_name: True})
    @override_feature_flag({DisableCaptchaForAccountExistEndpointFlag.flag_name: True})
    def test_captcha_validation_disabled(self):
        params = {'email': self.existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.customer_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']

    def test_account_does_not_exists(self):
        params = {'email': self.non_existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.customer_headers)

        assert response.code == status.HTTP_200_OK
        assert not json.loads(response.body)['account_exists']


@pytest.mark.django_db
class TestAccountExistsBooksyMedCustomerEndpoint(BaseSessionOnlyHTTPTestCase):

    url = f'/api/{settings.API_COUNTRY.lower()}/2/customer_api/account/exists/?'
    existing_email = f'{settings.BM_CUSTOMER_IMPORT_PREFIX}@booksy.com'

    def setUp(self):
        baker.make_recipe('webapps.user.user_recipe', email=self.existing_email)
        super().setUp()

    def test_account_exists(self):
        params = {'email': self.existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.customer_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']

    @override_eppo_feature_flag({CustomerLoginRestrictionForImport.flag_name: True})
    def test_does_not_exists(self):
        params = {'email': self.existing_email}
        url = ''.join([self.url, urlencode(params)])

        expected_error = {
            'code': 'invalid',
            'description': 'Login is restricted to this email address.',
            'field': 'email',
            'type': 'validation',
        }

        response = self.fetch(url, headers=self.customer_headers)

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert json.loads(response.body)['errors'][0] == expected_error


@pytest.mark.django_db
class TestAccountExistsBusinessEndpoint(BaseSessionOnlyHTTPTestCase):

    url = f'/api/{settings.API_COUNTRY.lower()}/2/business_api/account/exists/?'

    existing_email = '<EMAIL>'
    non_existing_email = '<EMAIL>'
    business_existing_email = '<EMAIL>'
    customer_existing_email = '<EMAIL>'
    business_customer_existing_email = '<EMAIL>'

    def setUp(self):
        self.business_profile = baker.make_recipe(
            'webapps.user.business_user', email=self.business_existing_email
        )
        self.customer_profile = baker.make_recipe(
            'webapps.user.customer_user', email=self.customer_existing_email
        )
        self.both_biz_cus_profile = baker.make_recipe(
            'webapps.user.user_with_profiles', email=self.business_customer_existing_email
        )
        baker.make_recipe('webapps.user.user_recipe', email=self.existing_email)
        super().setUp()

    def tearDown(self):
        User.objects.all().delete()
        UserProfile.objects.all().delete()

    def test_account_exists_ff_off(self):
        params = {'email': self.existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']
        with pytest.raises(KeyError):
            assert json.loads(response.body)['profile_type]']

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_account_exists_ff_on(self):
        params = {'email': self.existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']
        assert json.loads(response.body)['profile_type'] == []

    def test_account_does_not_exists_ff_off(self):
        params = {'email': self.non_existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK
        assert not json.loads(response.body)['account_exists']
        with pytest.raises(KeyError):
            assert json.loads(response.body)['profile_type]']

    @override_eppo_feature_flag({DisableDisposableEmailBlacklistBusinessFlag.flag_name: True})
    def test_disposable_email_error(self):
        params = {'email': '<EMAIL>'}
        url = ''.join([self.url, urlencode(params)])
        expected_error = {
            'code': 'invalid',
            'description': 'Email not accepted. Please use a different email address.',
            'field': 'email',
            'type': 'validation',
        }

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert json.loads(response.body)['errors'][0] == expected_error

    @parameterized.expand(
        [
            ('<EMAIL>', False),
            ('<EMAIL>', True),
            ('<EMAIL>', False),
        ]
    )
    def test_no_disposable_email_error(self, email, flag):
        params = {'email': email}
        url = ''.join([self.url, urlencode(params)])

        with override_eppo_feature_flag(
            {DisableDisposableEmailBlacklistBusinessFlag.flag_name: flag}
        ):
            response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_business_account_profile_type_ff_on(self):
        params = {'email': self.business_existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']
        assert [UserProfile.Type.BUSINESS] == json.loads(response.body)['profile_type']

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_customer_account_profile_type_ff_on(self):
        params = {'email': self.customer_existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']
        assert [UserProfile.Type.CUSTOMER] == json.loads(response.body)['profile_type']

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_business_customer_account_profile_type_ff_on(self):
        params = {'email': self.business_customer_existing_email}
        url = ''.join([self.url, urlencode(params)])

        response = self.fetch(url, headers=self.business_headers)

        assert response.code == status.HTTP_200_OK
        assert json.loads(response.body)['account_exists']
        assert [UserProfile.Type.CUSTOMER, UserProfile.Type.BUSINESS] == json.loads(response.body)[
            'profile_type'
        ]


@pytest.mark.django_db
class TestCheckAccountExistsHandlerHCaptcha(BaseAsyncHTTPTest):

    def setUp(self):
        self.existing_email = '<EMAIL>'
        params = {'email': self.existing_email}
        self.url = f'/business_api/account/exists/?{urlencode(params)}'
        baker.make_recipe('webapps.user.user_recipe', email=self.existing_email)

        super().setUp()

        self.business_api_key = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=FRONTDESK,
        ).api_key

    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_inactive(self, mocked_verify):
        response = self.fetch(self.url, extra_headers={'x-hcaptcha-token': 'test_token'})

        mocked_verify.assert_not_called()
        self.assertEqual(status.HTTP_200_OK, response.code)

    @override_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=True)
    def test_hcaptcha_active_request_passed(self, mocked_verify):
        response = self.fetch(self.url, extra_headers={'x-hcaptcha-token': 'test_token'})

        mocked_verify.assert_called()
        self.assertEqual(status.HTTP_200_OK, response.code)

    @override_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_active_request_blocked(self, _):
        response = self.fetch(self.url, extra_headers={'x-hcaptcha-token': 'test_token'})

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        error = response.json['errors'][0]
        self.assertIn('Request blocked', error['description'])

    @override_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_active_no_token(self, mocked_verify):
        response = self.fetch(self.url)

        mocked_verify.assert_not_called()
        self.assertEqual(status.HTTP_200_OK, response.code)

    @override_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_force_no_token(self, _):
        response = self.fetch(self.url)

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        error = response.json['errors'][0]
        self.assertIn('Request blocked', error['description'])

    @override_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=True)
    def test_hcaptcha_force_request_passed(self, mocked_verify):
        response = self.fetch(self.url, extra_headers={'x-hcaptcha-token': 'test_token'})

        mocked_verify.assert_called()
        self.assertEqual(status.HTTP_200_OK, response.code)

    @override_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_force_request_blocked(self, _):
        response = self.fetch(self.url, extra_headers={'x-hcaptcha-token': 'test_token'})

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        error = response.json['errors'][0]
        self.assertIn('Request blocked', error['description'])
