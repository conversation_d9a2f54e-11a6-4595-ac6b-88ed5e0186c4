from contextlib import contextmanager
from datetime import timedelta
import logging

import responses
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db.models import Q
from django.db.models.query import Prefetch
from django.utils.translation import gettext as _
from rest_framework import status

import lib.tools
from cliapps.business.b_listing import check_blisting_email
from lib import safe_json, segment_analytics
from lib.db import retry_on_sync_error, using_db_for_reads, READ_ONLY_DB
from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.business import (
    AutomateProviderRecruitmentFlag,
    MyBusinessesMigrationFlag,
)
from lib.feature_flag.feature.security import HCaptchaBusinessFlag, HCaptchaBusinessForceFlag
from lib.feature_flag.killswitch import HintsAndWalkThroughEventsPublishingFlag
from lib.tools import get_meta_data_from_handler
from service.business.hcaptcha import HCaptchaBusinessRequestValidator
from service.business.serializers.business_account import (
    MyBusinessesSearchSerializer,
)
from service.business.utils import prepare_subdomain_params
from service.exceptions import ServiceError
from service.mixins.paginator import PaginatorMixin
from service.tools import (
    AnalyticsTokensMixin,
    AppsFlyerMixin,
    RequestHandler,
    ServiceTypeMarketingMyBusinessHandlerMixin,
    filter_allowed_business_ids,
    json_request,
    session,
)
from webapps.admin_extra.bank_importer_new import (
    ENTERPRISE_IMPORTER_ID,
    IMPORTER_KEY,
)
from webapps.business.events import business_activated_event
from webapps.business.messages.business import BusinessPrimaryDataChangedMessage
from webapps.business.models import (
    BListing,
    Business,
    BusinessPromotion,
    Resource,
)
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.external import BusinessFacebookPage
from webapps.business.searchables.business import BusinessAccountSearchable
from webapps.business.searchables.serializers import (
    BusinessAccountHitSerializer,
)
from webapps.business.serializers import (
    BListingSearchRequestSerializer,
    BusinessPackageSerializer,
    BusinessSerializer,
    BusinessVisibleDelayRequestSerializer,
    CurrentStafferSerializer,
    SimpleBusinessClaimSerializer,
    SimpleBusinessInfoSerializer,
)
from webapps.business.services import (
    events,
    business_categories as business_categories_services,
    messagebus,
)
from webapps.business.tasks import post_business_activate_task
from webapps.business.tools import claim_b_listing_immediate
from webapps.c2b_referral.models import RewardC2B
from webapps.experiment_v3.exp import HintAndWalkthroughExperiment
from webapps.marketing.models import DelayedGTMEventAuthData
from webapps.notification.models import UserNotification
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_business import (
    BusinessActivityScenario,
)
from webapps.notification.tools import set_notification_receivers
from webapps.pos.models import POS
from webapps.pos.tools import create_pos
from webapps.segment.models import AppUsedByUserByDate
from webapps.segment.tasks import (
    analytics_business_info_updated_task,
    analytics_business_registration_completed_task,
    analytics_business_registration_started_task,
    analytics_business_status_updated_task,
    analytics_continuous_discovery_business_created_task,
    analytics_onboarding_delay_set_task,
)
from webapps.subdomain_grpc.client import SubdomainGRPCValidationError
from webapps.user.models import (
    User,
    UserProfile,
)

onboarding_analytics_logger = logging.getLogger('booksy.onboarding_analytics_logger')


@retry_on_sync_error
def insert_business_categories(business, categories):
    """Perform bulk inserting of business categories M2M into business."""
    business.categories.set(categories, clear=True)  # @transaction.atomic


@contextmanager
def handle_subdomain_exceptions(error_name_field: bool = True):
    try:
        yield
    except SubdomainGRPCValidationError as e:
        if error_name_field:
            error = {
                "code": "invalid_name",
                "description": _("Business name invalid. Please choose another."),
                "field": "name",
            }
        else:
            error = {
                "code": "invalid",
                "description": _("Subdomain is not available."),
                "field": "subdomain",
            }

        raise ServiceError(code=status.HTTP_400_BAD_REQUEST, errors=[error]) from e


# pylint: disable=too-many-ancestors
class MyBusinessHandler(RequestHandler, AppsFlyerMixin, ServiceTypeMarketingMyBusinessHandlerMixin):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR, BooksyTeams.PROVIDER_ONBOARDING)
    SESSION_BUMP_DISABLED_FOR = [responses.GET]

    @session(login_required=True)
    @json_request
    def put(self, business_id):
        """Update my Business.

        swagger:
            summary: Update my Business
            notes: >
                This endpoint supports PATCH logic - partial updates.
                Send only those fields that are relevant to the screen user
                currently sees, i.e. only opening hours fields on the
                opening hours edit screen.
            type: BusinessDetailsResponse
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: path
                - name: body
                  description: JSON with CreateBusinessDetails
                  type: CreateBusinessDetails
                  paramType: body
        """
        business = self.business_with_manager(business_id, __check_region=False)

        categories_before_change = set(business.categories.values_list('id', flat=True))

        extra_attrs = ['get_categories_internal_names']
        business_before_change = BusinessChange.extract_vars(
            business,
            extra_attrs=extra_attrs,
        )

        business_serializer = BusinessSerializer(
            business,
            data=self.data,
            step_with_draft=False,
            partial=True,
            context={
                'current_user': self.user,
            },
        )

        self.validate_serializer(business_serializer)

        if (
            business.integrations
            and business.integrations.get(IMPORTER_KEY) == ENTERPRISE_IMPORTER_ID
        ):
            if business_categories_services.are_categories_changed(
                categories_before_change,
                business_before_change['primary_category_id'],
                business_serializer.validated_data,
            ):
                errors = [
                    {
                        "description": _(
                            'Enterprise business is not allowed to modify its own categories',
                        )
                    }
                ]
                self.return_error(errors, status.HTTP_403_FORBIDDEN)
                return

        BusinessPromotion.cache_promotion_updater(
            business_id,
            BusinessPromotion.BY_BUSINESS,
        )

        if (
            business.public_email is None
            and business_serializer.validated_data.get('public_email') is None
        ):
            business.public_email = business.owner.email

        with handle_subdomain_exceptions(business.status == Business.Status.SETUP):
            business_serializer.save()

        BusinessChange.add(
            business,
            business,
            business_before_change,
            handler=self,
            metadata={'endpoint': '/me/businesses/$'},
            extra_attrs=extra_attrs,
        )

        categories_changed = business_categories_services.are_categories_changed(
            categories_before_change,
            business_before_change['primary_category_id'],
            business_serializer.validated_data,
        )

        # <editor-fold desc="early finish section">
        segment_api = segment_analytics.get_segment_api(
            business=business,
            source=self.booking_source,
        )
        segment_api.business_created(list(self.data.keys()))

        if self.data.get('location', {}).get('zipcode'):
            segment_api.identify_merchant_metropolis(business)

        messagebus.handle(
            events.MaybeCategoriesChanged(
                categories_before_change=categories_before_change,
                business_before_change_primary_category_id=business_before_change[
                    'primary_category_id'
                ],
                business=business_serializer.validated_data,
                business_id=business.id,
                booking_source_id=self.booking_source.id,
            )
        )

        analytics_business_info_updated_task.delay(
            business_id=business_id,
            context={
                'business_id': business_id,
                'source_id': self.booking_source.id,
            },
        )
        # </editor-fold>

        self.set_status(status.HTTP_200_OK)
        self.finish(
            {
                'business': business_serializer.data,
                'categories_changed': categories_changed,
                'current_staffer': (
                    CurrentStafferSerializer(self.user_staffer).data if self.user_staffer else None
                ),
            }
        )

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id):
        """Get my Business Details.

        swagger:
            summary: Get my Business Details
            type: BusinessDetailsResponse
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: path

        """
        business = self.business_with_staffer(
            business_id,
            __select_related=('owner', 'renting_venue'),
            __check_region=False,
        )

        start_scenario(BusinessActivityScenario, business=business)

        business_serializer = BusinessSerializer(
            business,
            context={
                'current_user': self.user,
            },
        )
        color_palette = None
        if self.user:
            self.update_appsflyer(business.id)
            AppUsedByUserByDate.set_river_cache(
                user_id=self.user.id,
                booking_source_id=self.booking_source.id,
                business=business,
            )
            color_palette = self.user.service_color_palette

        response_data = {
            'business': business_serializer.data,
            'current_staffer': (
                CurrentStafferSerializer(self.user_staffer).data if self.user_staffer else None
            ),
            'service_color_palette': color_palette,
            'service_type_marketing': self.get_service_type_marketing_data(business_id=business_id),
        }

        self.finish_with_json(status.HTTP_200_OK, response_data)


class MyBusinessBListingListHandler(RequestHandler):
    @session(login_required=True)
    def get(self):
        """swagger:
        summary: Get BListing details.
        type: BusinessSimpleResults
        parameters:
            - name: source_phone
              type: string
              paramType: query
              description: potential business owner phone
              required: false
        :swagger
        """

        self.handle_whitelisting_ip_access_for_staffer()
        request_serializer = BListingSearchRequestSerializer(
            data=self._prepare_get_arguments(),
        )
        request_data = self.validate_serializer(request_serializer)

        b_listings = BListing.objects.none()
        source_email = self.user.email
        if check_blisting_email(email=source_email):
            b_listings = BListing.objects.filter(source_email=source_email)
        source_phone = request_data.get('source_phone')
        if source_phone and not b_listings.exists():
            # search by phone number only when there is no match on email
            b_listings = BListing.objects.filter(
                Q(phone=source_phone.local_nice) | Q(phone=source_phone.global_nice)
            )

        # add prefetch and ordering
        b_listings = b_listings.prefetch_related(
            Prefetch(
                'pos_set',
                queryset=POS.objects.filter(active=True).only('id', 'active', 'business_id'),
            ),
        ).order_by('id')

        serializer = SimpleBusinessClaimSerializer(
            instance=b_listings,
            many=True,
        )
        self.set_status(status.HTTP_200_OK)
        self.finish(
            {
                'businesses': serializer.data,
            }
        )


class MyBusinessBListingHandler(RequestHandler):
    @session(login_required=True)
    @json_request
    def post(self, b_listing_id):
        """swagger:
        summary: Claim BListing.
        type: BusinessSimpleDetailsResponse
        parameters:
            - name: source_phone
              type: string
              paramType: body
              description: potential business owner phone
              required: false
        :swagger
        """
        self.handle_whitelisting_ip_access_for_staffer()

        request_serializer = BListingSearchRequestSerializer(
            data=self.data,
        )
        request_data = self.validate_serializer(request_serializer)
        b_listings = BListing.objects.none()
        source_email = self.user.email
        if check_blisting_email(email=source_email):
            b_listings = BListing.objects.filter(Q(id=b_listing_id) & Q(source_email=source_email))
        source_phone = request_data.get('source_phone')
        if source_phone and not b_listings.exists():
            # search by phone number only when there is no match on email
            b_listings = BListing.objects.filter(
                Q(id=b_listing_id)
                & (Q(phone=source_phone.local_nice) | Q(phone=source_phone.global_nice))
            )

        b_listing = b_listings.first()
        lib.tools.sasrt(
            b_listing,
            status.HTTP_400_BAD_REQUEST,
            {
                'code': 'invalid',
                'type': 'validation',
            },
        )

        claim_b_listing_immediate(b_listing, self.user)
        self.set_status(status.HTTP_200_OK)
        self.finish({'business': BusinessSerializer(instance=b_listing).data})


class MyBusinessPackageHandler(RequestHandler):

    @session(login_required=True)
    def put(self, business_id, business_package):
        """Update my Business package

        swagger:
            summary: Update my Business package
            notes: >
                This endpoint updates only package of Business
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: path
                - name: business_package
                  type: string
                  paramType: path
                  enum_from_const:
                    webapps.business.models.Business.ELIGIBLE_PACKAGE
        """
        business = self.business_with_manager(business_id, __check_region=False)
        business_before_change = BusinessChange.extract_vars(business)

        serializer = BusinessPackageSerializer(
            business,
            data={
                'package': business_package,
            },
        )
        self.validate_serializer(serializer)
        serializer.save()

        if business_package == business.Package.LITE:
            if (pos := business.pos) and not pos.auto_charge_cancellation_fee:
                pos.auto_charge_cancellation_fee = True
                pos.save(update_fields=['auto_charge_cancellation_fee'])

        BusinessChange.add(
            business,
            business,
            business_before_change,
            handler=self,
            metadata={'endpoint': '/me/businesses/$'},
        )

        self.set_status(status.HTTP_200_OK)
        self.finish({})

        analytics_business_info_updated_task.delay(
            business_id=business_id,
            context={
                'business_id': business_id,
                'source_id': self.booking_source.id,
            },
        )


class MyBusinessesHandler(RequestHandler, PaginatorMixin):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    __tornado_migration_feature_flag__ = MyBusinessesMigrationFlag
    page_name = 'businesses_page'
    per_page_name = 'businesses_per_page'
    default_per_page_setting = 'BUSINESSES_PER_PAGE'

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self):
        """Get list of my Businesses.

        swagger:
            summary: Get list of my Businesses
            type: MyBusinessesResponse
            parameters:
              - name: name
                type: string
                paramType: query
                description: >
                    DEPRECATED in favor of "query". Name of the business to be used as filter
                required: false
              - name: query
                type: string
                paramType: query
                description: part of a business name or address to search for
                required: false
              - name: businesses_page
                type: integer
                paramType: query
                description: current page of businesses results to be returned
                defaultValue: 1
              - name: businesses_per_page
                type: integer
                paramType: query
                description: number of businesses per page
                default_from_const: settings.BUSINESSES_PER_PAGE
        :swagger
        """
        self.handle_whitelisting_ip_access_for_staffer()

        data = self._prepare_get_arguments()
        serializer = MyBusinessesSearchSerializer(data=data)
        request_data = self.validate_serializer(serializer)

        page = request_data['businesses_page']
        per_page = request_data['businesses_per_page']

        search_data = self.get_search_data(request_data)
        searchable = BusinessAccountSearchable(
            ESDocType.BUSINESS_ACCOUNT,
            serializer=BusinessAccountHitSerializer,
        ).search(search_data)
        businesses_count = searchable.count()

        sort_args = ('-active', 'id')
        resp = (
            searchable.params(
                from_=(page - 1) * per_page,
                size=per_page,
            )
            .sort(*sort_args)
            .execute()
        )
        business_ids_es = [business.id for business in resp]

        # add prefetch and ordering
        businesses_qs = (
            Business.objects.filter(
                id__in=business_ids_es,
            )
            .prefetch_related(
                Prefetch(
                    'pos_set',
                    queryset=POS.objects.filter(active=True).only('id', 'active', 'business_id'),
                ),
            )
            .order_by(*sort_args)
        )

        serializer = SimpleBusinessInfoSerializer(
            instance=businesses_qs,
            many=True,
        )

        ret = {
            'businesses': serializer.data,
            'businesses_per_page': per_page,
            'businesses_count': businesses_count,
        }
        self.finish_with_json(status.HTTP_200_OK, ret)

    def get_search_data(self, request_data: dict) -> dict:
        search_data = {
            'excluded_statuses': [
                Business.Status.VENUE,
                Business.Status.B_LISTING,
            ],
            'business_ids': self.get_business_ids(
                self.user,
                self.get_access_level(self.user),
                self.request.user_ip,
            ),
        }

        query = request_data.get('query') or request_data.get('name')
        if query is not None:
            search_data['query'] = query

        return search_data

    @staticmethod
    def get_business_ids(user, access_level, request_ip) -> list[int]:
        # If someone was a staffer, we will always show businesses
        # based on this relation. We use owner only as a fallback
        if access_level in Resource.STAFF_ACCESS_LEVELS_NOT_OWNER:
            qs = Resource.objects.filter(
                staff_user_id=user.id,
                active=True,
                deleted__isnull=True,
            )
            business_ids = list(set(qs.values_list('business_id', flat=True)))
        # handle default case - businesses by owner
        else:
            resource_business_ids = set(
                Resource.objects.filter(
                    staff_user_id=user.id,
                ).values_list('business_id', flat=True)
            )
            business_ids = Business.objects.filter(
                owner=user.id,
            ).values_list('id', flat=True)
            business_ids = list(set.union(resource_business_ids, business_ids))

        business_ids = filter_allowed_business_ids(business_ids, request_ip)
        return business_ids

    @session(login_required=True)
    @json_request
    def post(self):
        """Create my Business.

        swagger:
            summary: Create my Business
            type: BusinessDetailsResponse
            parameters:
                - name: body
                  description: JSON with CreateBusinessDetails
                  type: CreateBusinessDetails
                  paramType: body
        :swagger
        """
        if HCaptchaBusinessFlag(
            UserData(custom={'path': self.request.path.strip('/').removeprefix('core/v2')})
        ) and (self.request.headers.get('x-hcaptcha-token') or HCaptchaBusinessForceFlag()):
            HCaptchaBusinessRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            ).validate()

        self.handle_whitelisting_ip_access_for_staffer()

        setup_business_exists = (
            self.user.businesses.count() == 1
            and self.user.businesses.first().status == Business.Status.SETUP
        )

        if not setup_business_exists:
            self.quick_assert(
                not (self.user.businesses.exists() or self.user.staffers.exists()),
                ('unique', 'validation', 'name'),
                _('Business already exists for email {email}.').format(
                    email=self.user.email,
                ),
            )
        else:
            # substitute business and staffer
            business = self.user.businesses.first()
            current_staffer = business.resources.filter(active=True, staff_user=self.user).first()
            # set not created status
            self.set_status(status.HTTP_200_OK)
            business_id = business.id
            analytics_business_registration_started_task.delay(
                business_id=business_id,
                booking_source_id=self.booking_source.id,
                context={
                    'source_id': self.booking_source.id,
                    'business_id': business_id,
                },
            )
            self.finish(
                {
                    'business': BusinessSerializer(
                        self.user.businesses.first(),
                        context={
                            'current_user': self.user,
                        },
                    ).data,
                    'current_staffer': (
                        CurrentStafferSerializer(current_staffer).data
                        if current_staffer is not None
                        else None
                    ),
                }
            )
            create_pos(business)
            # end post
            return

        data = prepare_subdomain_params(self.data)

        if data.get('public_email') is None:
            data['public_email'] = self.user.email

        business_serializer = BusinessSerializer(
            data=data,
            context={
                "owner": self.user,
                "booking_source": self.booking_source,
                "fingerprint": self.fingerprint,
                "current_user": self.user,
            },
        )
        with handle_subdomain_exceptions():
            self.validate_serializer(business_serializer)
            business = business_serializer.save()

        if settings.ADD_OWNER_TO_NOTIFICATIONS_RECIEVERS:
            self.set_business_owner_notifications(business)

        business_id = business.id
        analytics_business_registration_started_task.delay(
            business_id=business_id,
            booking_source_id=self.booking_source.id,
            context={
                'business_id': business_id,
                'source_id': self.booking_source.id,
            },
        )

        # <editor-fold desc="early finish section">
        BusinessChange.add(
            business,
            business,
            {},
            handler=self,
            metadata={'endpoint': 'MyBusinessesHandler.post'},
        )

        # create c2b referral reward
        if data.get('invited_by_customer'):
            user = User.objects.filter(id=data['invited_by_customer']).first()
            reward_c2b = RewardC2B(business=business, user=user)
            reward_c2b.save()

        # CREATE_POS
        create_pos(business)

        business.create_resource_from_owner()

        # segment stuff
        segment_api = segment_analytics.get_segment_api(
            business=business,
            source=self.booking_source,
        )
        segment_api.business_created(list(data.keys()))

        messagebus.handle(
            events.MaybeCategoriesCreated(
                business=business,
                booking_source_id=self.booking_source.id,
            )
        )

        # </editor-fold>
        self.set_status(status.HTTP_201_CREATED)
        self.finish(
            {
                'business': business_serializer.data,
                'current_staffer': None,
            }
        )

    @staticmethod
    def set_business_owner_notifications(business):
        profile = business.owner.profiles.get(
            profile_type=UserProfile.Type.BUSINESS,
        )
        receivers = [
            {
                'identifier': business.owner.email,
                'business_id': business.id,
            }
        ]

        set_notification_receivers(
            profile,
            UserNotification.EMAIL_NOTIFICATION,
            receivers,
            is_superuser=False,
        )


class BusinessActivationMixin:

    def ensure_valid_booking_mode(self):
        booking_mode_valid = Business.BookingMode.values()

        if 'booking_mode' in self.data:
            self.quick_assert(
                self.data['booking_mode'] in booking_mode_valid,
                ('invalid', 'validation', 'booking_mode'),
                _('Invalid booking mode'),
            )
        else:
            # #16793 - Ukrycie wyboru trybu, defaultowo ma byc Automatic
            self.data['booking_mode'] = Business.BookingMode.AUTO

    @staticmethod
    def can_activate_business(business=None, data=None):
        """
        Checks if business can

        NOTE: This function might alter business.latitude/longitude.

        :param business:
        :param data:
        :return:
        """

        errors = []

        if not business.region:
            errors.append(
                {
                    'code': 'required',
                    'field': 'zipcode',
                    'type': 'validation',
                    'description': _('Zip code is required before business is activated.'),
                }
            )

        if not business.categories.exists():
            errors.append(
                {
                    'code': 'required',
                    'field': 'category',
                    'type': 'validation',
                    'description': _('Category must be set before business is activated.'),
                }
            )

        booking_mode = business._meta.get_field('booking_mode')
        choices = [ch[0] for ch in booking_mode.choices]
        if data.get('booking_mode', '') not in choices:
            errors.append(
                {
                    'code': 'invalid',
                    'field': 'booking_mode',
                    'type': 'validation',
                    'description': _('Invalid booking mode'),
                }
            )

        if not business.services.exists() and not data.get('hidden_in_search', False):
            errors.append(
                {
                    'code': 'required',
                    'field': 'services',
                    'type': 'validation',
                    'description': _('At least one service is required to activate business.'),
                }
            )

        if not business.resources.exists():
            errors.append(
                {
                    'code': 'required',
                    'field': 'resources',
                    'type': 'validation',
                    'description': _(
                        'At least one staff or resource is required to activate business.'
                    ),
                }
            )

        if business.latitude is None or business.longitude is None:
            errors.append(
                {
                    'code': 'required',
                    'field': 'coordinates',
                    'type': 'validation',
                    'description': _('Business geo location is required.'),
                }
            )

        return errors


class BusinessActivationStatusHandler(BusinessActivationMixin, RequestHandler):
    """
    swagger:
        parameters:
            - name: business_id
              description: Business id
              type: integer
              paramType: path
    """

    @session(login_required=True)
    def get(self, business_id):
        self.data = {}

        business = self.business_with_manager(business_id, __check_region=False)

        self.data['booking_mode'] = business.booking_mode

        errors = self.can_activate_business(business=business, data=self.data)

        ret = {
            'activation_status': 'ready',
            'errors': [],
        }

        if len(errors) > 0:
            ret['activation_status'] = 'not_ready'
            ret['errors'] = errors

        self.finish(ret)


class BusinessActivationHandler(
    AnalyticsTokensMixin,
    BusinessActivationMixin,
    RequestHandler,
):
    """
    swagger:
        description: Activates Bussiness and sets it's booking mode
        summary: Activates Bussiness and sets it's booking mode
        notes: |
          Since version 1.1.1 `booking_mode` can be omitted which defaults
          to "A".
          Additionally if value is given is checked if is one of "A", "S", "M".
          If not then error 400 is raised with 'code': 'invalid'.
          <br><br>
          Before version 1.1.1 `booking_mode` was required.
          If was missing then error 400 was raised with 'code': 'required'.
          Value was not checked.
        parameters:
            - name: business_id
              description: Business id
              type: integer
              paramType: path
              required: True
            - name: body
              description:
                JSON with optional `booking_mode`
                (AUTOMATIC, MANUAL, SEMIAUTOMATIC)
              type: BusinessActivationRequest
              paramType: body
        type: BusinessDetailsResponse
    :swagger

    swaggerModels:
        BusinessActivationRequest:
            id: BusinessActivationRequest
            description:
                JSON with optional `booking_mode`
            properties:
                booking_mode:
                    type: string
                    description: Business booking mode
                    enum_from_const: business.Business.BookingMode
                    defaultValue: A
                    required: False
                visible_delay:
                    type: integer
                    required: false
                    default: 0
                    description: visibility activation delay in days (0-14)
                app_instance_id:
                    type: string
                    description: Firebase app installation id for analytics
                    required: false
                client_id:
                    type: string
                    description: Firebase web sdk client id for analytics
                    required: false
                hidden_in_search:
                    type: boolean
                    description: Hide business in search
                    required: false

    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)

    # pylint: disable=too-many-branches, too-many-statements
    @session(login_required=True)
    @json_request
    def post(self, business_id):
        self.ensure_valid_booking_mode()

        business = self.business_with_manager(business_id)

        errors = self.can_activate_business(business=business, data=self.data)
        if errors:
            self.return_error(errors, status.HTTP_400_BAD_REQUEST)
            return

        # hidden_in_search for Minimum Bookable Profile experiment
        business.hidden_in_search = self.data.get('hidden_in_search', False)
        business_visible = True
        if business.package == Business.Package.PRO:
            visible_serializer = BusinessVisibleDelayRequestSerializer(data=self.data)
            visible_data = self.validate_serializer(visible_serializer)
            delay_in_days = visible_data['visible_delay']  # in days
            # if not delay than make business visible as default
            business_visible = not bool(delay_in_days)

        business_before_change = BusinessChange.extract_vars(business)

        # activate business
        old_business_status = business.status
        analytics_status_updated_trigger = False
        if business.status == business.Status.SETUP:
            business.status = business.Status.TRIAL
            business.active_from = lib.tools.tznow()
            business.active = True
            # #30299 - send invitation after onboarding
            for resource in business.resources.filter(
                active=True,
                staff_user__isnull=False,
            ):
                resource.staff_user_invite()
            analytics_status_updated_trigger = True

        # #16177: Manual -> Semi-Automatic hack
        business.booking_mode = self.data['booking_mode']
        if business.booking_mode == Business.BookingMode.MANUAL:
            business.booking_mode = Business.BookingMode.SEMIAUTO

        firebase_auth_dict = self.firebase_auth_dict
        if firebase_auth_dict:
            DelayedGTMEventAuthData.objects.get_or_create(
                business=business,
                booking_source=self.booking_source,
                event_type=DelayedGTMEventAuthData.EventType.ONBOARDING_GO_LIVE,
                **firebase_auth_dict,
            )
        business.visible = business_visible

        if business.owner.is_test_user():
            business.visible = False
        elif business_visible:
            business_activated_event.send(
                business,
                logged_in_user_id=self.user.id,
                fingerprint=self.request.headers.get('X-Fingerprint'),
            )
        else:
            # set business visible delay
            business.visible_delay_till = lib.tools.tznow() + timedelta(days=delay_in_days)

        business.save()
        if analytics_status_updated_trigger:
            business_id = business.id
            analytics_business_status_updated_task.delay(
                business_id=business_id,
                context={
                    'business_id': business_id,
                },
            )

        self.set_status(status.HTTP_200_OK)

        analytics_business_registration_completed_task.delay(
            business_id=business.id,
            booking_source_id=self.booking_source.id,
            context={
                'business_id': business.id,
                'source_id': self.booking_source.id,
            },
        )

        primary_category = (
            business.primary_category.internal_name if business.primary_category else ''
        )
        msg = (
            f"Will try to run discovery task for business {business.id}, "
            f"category: {primary_category}"
        )
        onboarding_analytics_logger.warning(msg)

        if AutomateProviderRecruitmentFlag(
            UserData(custom={'primary_category': primary_category}, subject_key=business.id)
        ):
            msg = (
                f"Discovery flag passed for business {business.id}, "
                f"category: {primary_category}"
            )
            onboarding_analytics_logger.warning(msg)
            analytics_continuous_discovery_business_created_task.delay(
                context={'business_id': business_id}
            )

        if HintsAndWalkThroughEventsPublishingFlag():
            BusinessPrimaryDataChangedMessage(business).publish()
            HintAndWalkthroughExperiment(business_id).get_variant(draw=True)

        DelayedGTMEventAuthData.objects.get_or_create(
            business=business,
            booking_source=self.booking_source,
            app_instance_id=self.data.get('app_instance_id'),
            client_id=self.data.get('client_id'),
            event_type=DelayedGTMEventAuthData.EventType.BUSINESS_ACTIVATED_INVITES,
        )
        if not business_visible:
            analytics_onboarding_delay_set_task.delay(
                business_id=business.id,
                delay_in_days=delay_in_days,
                context={
                    'business_id': business.id,
                    'source_id': self.booking_source.id,
                },
            )
        resp = {
            'business': BusinessSerializer(business).data,
            'current_staffer': (
                CurrentStafferSerializer(self.user_staffer).data if self.user_staffer else None
            ),
        }

        # <editor-fold desc="early_finish section">
        post_business_activate_task.delay(
            business_id=business_id,
            business_before_change=safe_json.dumps(business_before_change),
            old_business_status=old_business_status,
            handler_metadata=safe_json.dumps(
                get_meta_data_from_handler(
                    handler=self,
                )
            ),
            operator_id=self.user.id if self.user else None,
        )
        # </editor-fold>
        self.finish(resp)


class BusinessFBPageAppHandler(RequestHandler):
    @session()  # login_required=True)
    @json_request
    def put(self, business_id):
        """
        swagger:
            summary: Associate Facebook Pages with Businesses.
            notes:
                In order for Facebook Page Tab App to work, we must store
                a relation between Facebook Page ID and Business ID.
                This API operation enables that.
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: body
                  description: JSON with Facebook Page IDs list
                  type: FacebookPagesIDs
                  paramType: body
            type: PutFacebookPageResponse
        :swagger

        swaggerModels:
            FacebookPagesIDs:
                id: FacebookPagesIDs
                description: a dict containing list of Facebook Pages IDs
                required:
                  - facebook_pages
                properties:
                    facebook_pages:
                        description: list of Facebook Page IDs
                        type: array
                        items:
                           type: integer
                           description: Facebook Page ID
            PutFacebookPageResponse:
                id: PutFacebookPageResponse
                description: a list of created/updated/unchanged Facebook Pages
                required:
                  - facebook_pages
                properties:
                    facebook_pages:
                        description:
                            a list of dicts indicating status of Facebook Pages
                            after PUT operation
                        type: array
                        items:
                            type: PutFacebookPageStatus
            PutFacebookPageStatus:
                id: PutFacebookPageStatus
                description:
                    dict indicating status of Facebook Page after PUT operation
                required:
                  - id
                  - status
                properties:
                    id:
                        type: integer
                        description: Facebook Page ID
                    status:
                        type: string
                        description: status of PUT operation
                        enum:
                          - created
                          - updated
                          - unchanged
                        defaultValue: created
        :swaggerModels
        """
        self.get_object_or_404(
            ('business', 'Business'),
            id=business_id,
        )

        # if self.user != business.owner or \
        #        self.user.username not in ['operator1', 'operator2']:
        #    raise tornado.web.HTTPError(404)

        pages = self.data.get('facebook_pages', [])
        resp = []
        # pylint: disable=redefined-outer-name
        for page in pages:
            biz_fb_page, created = BusinessFacebookPage.objects.get_or_create(
                id=page, defaults={'business_id': business_id}
            )
            if created:
                page_status = 'created'
            elif biz_fb_page.business_id != business_id:
                biz_fb_page.business_id = business_id
                biz_fb_page.save()
                page_status = 'updated'
            else:
                page_status = 'unchanged'
            resp.append({'id': page, 'status': page_status})
        # pylint: enable=redefined-outer-name
        self.finish(
            {
                'facebook_pages': resp,
            }
        )
