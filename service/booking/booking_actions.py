import logging
from datetime import timedelta

import grpc
from bo_obs.datadog.enums import BooksyTeams
from celery import chain
from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import serializers, status

import lib.tools
from lib.feature_flag.feature.booking import RemoveUpdateFutureBookings
from lib.locks import ConfirmAppointmentPrepaymentLock
from lib.segment_analytics.enums import EventType
from lib.tools import tznow
from merger_grpc.appointment_serializers import AppointmentUpdateSerializer
from service.booking.appointments import BaseAppointmentHandler
from service.exceptions import ServiceError
from service.partner_forward import PartnerForward, partner_forward
from service.tools import (
    HTTPErrorWithCode,
    json_request,
    session,
)
from webapps import consts
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    AppointmentType,
    AppointmentTypeSM as AT,
    BookingAction,
    UpdateFutureBooking,
    WhoMakesChange,
)
from webapps.booking.events import (
    appointment_canceled_by_business_event,
    appointment_confirmed_by_business_event,
    appointment_declined_by_business_event,
    appointment_no_show_by_business_event,
    appointment_status_changed_by_customer_event,
    business_ready_for_appointment,
    customer_no_show,
)
from webapps.booking.exceptions import BookingConflict
from webapps.booking.merger.appointment_client import AppointmentClient
from webapps.booking.merger.tools import merger_appointment_error
from webapps.booking.models import (
    Appointment,
    BookingChange,
)
from webapps.booking.serializers.appointment import (
    AnalyticsAppointmentSerializer,
    AnalyticsCustomerAppointmentSerializer,
    CustomerAppointmentSerializer,
    UpdateFutureBookingsField,
)
from webapps.booking.tasks import (
    create_late_cancellation_notifications_task,
    update_any_mobile_customer_appointments_task,
)
from webapps.business.context import business_context
from webapps.business.service_promotions import (
    CustomerAppointmentServicePromotionMixin,
)
from webapps.family_and_friends.helpers.booking import (
    change_family_and_friends_client_type_if_needed,
)
from webapps.kill_switch.models import KillSwitch
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import (
    BookingChangedScenario as BCS,
)
from webapps.pos.deposit import charge_prepayment_on_confirm
from webapps.pos.enums import receipt_status, ReissueGiftCardReasonEnum
from webapps.pos.models import Transaction
from webapps.pos.provider import get_payment_provider
from webapps.pos.tasks import (
    auto_refund_transaction,
    CheckoutBooksyPayTransaction,
    CheckoutPrepaidTransaction,
    ReleaseDepositOnCancel,
    handle_no_show_booksy_gift_card_task,
    reissue_gift_card_task,
)
from webapps.pos.typing import RefundEligibility
from webapps.pos.utils import get_refund_eligibility

log_action = logging.getLogger('booksy.actions')

# 'change' action is not an action - it means that booking is editable
# Customer only
# Business only
BOOKING_ACTIONS = [
    BookingAction.CANCEL,
    BookingAction.CANCEL_NO_SHOW,
    BookingAction.CONFIRM,
    BookingAction.DECLINE,
    BookingAction.NO_SHOW,
    # Those are not actions dispatched by actions endpoint: they occur in `allowed actions` only
    # to inform, that editing (by appointment endpoint) is allowed
    # TODO: add validation to `action` field?
    # BookingAction.CHANGE,
    # BookingAction.CHANGE_TIME_OR_NOTE,
]
BOOKING_ACTIONS__RELEASE_DEPOSIT = [
    BookingAction.CANCEL,
    BookingAction.DECLINE,
]
BOOKING_ACTIONS__AUTO_CHECKOUT_TRANSACTION = [
    BookingAction.CANCEL,
    BookingAction.DECLINE,
]
BOOKING_ACTIONS__AUTO_REFUND = (BookingAction.CANCEL,)
ACTION_TO_STATUS = {
    BookingAction.CANCEL: Appointment.STATUS.CANCELED,
    # Customer only
    BookingAction.ACCEPT: Appointment.STATUS.ACCEPTED,
    # Business only
    BookingAction.CANCEL_NO_SHOW: Appointment.STATUS.FINISHED,
    BookingAction.CONFIRM: Appointment.STATUS.ACCEPTED,
    BookingAction.DECLINE: Appointment.STATUS.DECLINED,
    BookingAction.NO_SHOW: Appointment.STATUS.NOSHOW,
}
ACTION_TO_SCENARIO_ACTION = {
    BookingAction.CANCEL: BCS.BUSINESS_CANCEL,
    BookingAction.CONFIRM: BCS.BUSINESS_CONFIRM,
    BookingAction.DECLINE: BCS.BUSINESS_DECLINE,
}


class BookingActionsRequest(serializers.Serializer):
    action = serializers.CharField(required=True)
    business_note = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=consts.BUSINESS_NOTE__MAX_LENGTH,
    )
    customer_note = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=consts.CUSTOMER_NOTE__MAX_LENGTH,
    )
    # default=True
    _notify_about_cancel = serializers.BooleanField(
        required=False,
        allow_null=True,
    )
    # default=_notify_about_cancel
    _notify_about_cancel_email = serializers.BooleanField(
        required=False,
        allow_null=True,
    )
    # default=_notify_about_cancel
    _notify_about_cancel_sms = serializers.BooleanField(
        required=False,
        allow_null=True,
    )
    _update_future_bookings = UpdateFutureBookingsField()
    release_deposit = serializers.BooleanField(
        required=False,
        default=False,
    )
    _version = serializers.IntegerField(required=False)
    no_thumbs = serializers.BooleanField(required=False, default=False)
    charge_bgc = serializers.BooleanField(required=False, default=True)

    def validate(self, attrs):
        """Some additional validation for _update_future_bookings.

        Booking actions split_repeating_by_booking only in CANCEL action
        and only in update_future=True mode.
        Any other action or update_future falls back to 'skip'.

        """
        action = attrs['action']
        if (
            attrs['_update_future_bookings'] == UpdateFutureBooking.NO
            or action != BookingAction.CANCEL
        ):
            attrs['_update_future_bookings'] = UpdateFutureBooking.SKIP

        # cancel notifications
        if action == BookingAction.CANCEL:
            old_notification = attrs.get('_notify_about_cancel')
            notify_email = attrs.get('_notify_about_cancel_email')
            notify_sms = attrs.get('_notify_about_cancel_sms')
            if old_notification is None:
                old_notification = True

            if notify_email is None:
                attrs['_notify_about_cancel_email'] = old_notification
            if notify_sms is None:
                attrs['_notify_about_cancel_sms'] = old_notification

        # Ticket: 48703
        # Only customer action should be override
        now = lib.tools.tznow()
        appt = self.context.get('appointment')
        if (
            self.context.get('who_makes_change') == WhoMakesChange.CUSTOMER
            and action == BookingAction.CANCEL
            and appt
            and appt.booked_from <= now <= appt.booked_till
        ):
            attrs['action'] = BookingAction.NO_SHOW

        return attrs


class CustomerBookingActionsRequest(BookingActionsRequest):
    _update_future_bookings = None

    def validate(self, attrs):
        attrs['_update_future_bookings'] = UpdateFutureBooking.SKIP
        action = attrs['action']

        # cancel notifications
        if action == BookingAction.CANCEL:
            old_notification = attrs.get('_notify_about_cancel')
            notify_email = attrs.get('_notify_about_cancel_email')
            notify_sms = attrs.get('_notify_about_cancel_sms')
            if old_notification is None:
                old_notification = True

            if notify_email is None:
                attrs['_notify_about_cancel_email'] = old_notification
            if notify_sms is None:
                attrs['_notify_about_cancel_sms'] = old_notification

        # Ticket: 48703
        # customer action should be override
        now = lib.tools.tznow()
        appt = self.context.get('appointment')
        if action == BookingAction.CANCEL and appt and appt.booked_from <= now <= appt.booked_till:
            attrs['action'] = BookingAction.NO_SHOW

        return attrs


class ActionForward(PartnerForward):
    def forward(  # pylint: disable=arguments-differ,too-many-arguments
        self,
        handler,
        appointment,
        _action,
        appointment_status,
        customer_note,
    ):
        self.handler = handler
        self.appointment = appointment
        self.appointment_status = appointment_status
        self.customer_note = customer_note

        request = {
            'business_id': self.appointment.business_id,
            'id': self.appointment.id,
            'import_id': self.appointment.import_uid,
            'status': self.appointment_status,
            'customer_note': self.customer_note,
        }
        message = AppointmentUpdateSerializer(request).message
        try:
            update = AppointmentClient().UpdateAppointment(
                message,
                timeout=settings.GRPC_API_TIMEOUT,
            )
        except grpc.RpcError as e:
            status_code = e.code()
            if status_code == grpc.StatusCode.INVALID_ARGUMENT:
                raise merger_appointment_error(e.details()) from e
            raise ServiceError(
                code=503,
                errors=[_('Service temporarily unavailable. Please try again later')],
            ) from e
        serializer = AppointmentUpdateSerializer(message=update)
        serializer.is_valid(raise_exception=True)
        update = serializer.validated_data

        # comment moved from BookingActionsRequest.validate:
        # # Booking actions split_repeating_by_booking only in CANCEL action
        # # and only in update_future=True mode.
        # # Any other action or update_future falls back to 'skip'.
        # Customer does not know update_future feature, so never does `update_repeating_booking`
        # Plus: Partners don't support RepeatingBooking
        appointment.update_appointment(
            updated_by=self.handler.user,
            status=update['status'],
            customer_note=update.get('customer_note', customer_note),
            who_makes_change=WhoMakesChange.CUSTOMER,
        )


class OldCustomerAppointmentActionsHandler(  # pylint: disable=too-many-ancestors
    CustomerAppointmentServicePromotionMixin, BaseAppointmentHandler
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(
        self, appointment_type, appointment_id
    ):  # pylint:disable=too-many-statements, too-many-branches
        """DEPRECATED Execute an action on Appointment.

        swagger:
            summary: DEPRECATED Execute an action on Appointment.
                     [will be deprecated soon use appointment_uid
                     instead appointment_id and appointment_type]
            parameters:
                - name: appointment_type
                  type: integer
                  paramType: path
                  description: Appointment Type (single or multi)
                  enum:
                    - single
                    - multi
                - name: appointment_id
                  type: integer
                  paramType: path
                  description: (Multi-)Booking ID
                - name: body
                  description: Booking action request
                  type: BookingActionRequest
                  paramType: body
                  required: True
            type: CustomerAppointmentResponse
        :swagger
        swaggerModels:
            CustomerAppointment:
                id: CustomerAppointment
                required:
                    - status
                    - actions
                    - _version
                properties:
                    status:
                        type: string
                        description: new status of a booking
                        enum_from_const: booking.Appointment.TYPE_CHOICES
                    _version:
                        type: integer
                        description: Booking version number
                    actions:
                        type: BookingActions
                        description: what actions are allowed

        """
        # pylint: disable=too-many-branches
        appointment = self.get_appointment(
            customer_user_id=self.user.id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
            prefetch_all=False,
        )
        previous_status = appointment.status

        serializer_class = (
            CustomerBookingActionsRequest if RemoveUpdateFutureBookings() else BookingActionsRequest
        )

        # validate request
        serializer = serializer_class(
            data=self.data,
            context={
                'who_makes_change': WhoMakesChange.CUSTOMER,
                'appointment': appointment,
            },
        )
        data = self.validate_serializer(serializer)
        action = data['action']

        if action == BookingAction.ACCEPT and appointment_id:
            lock_acquired = ConfirmAppointmentPrepaymentLock.try_to_lock(appointment_id)
            if not lock_acquired:
                return self.return_error(
                    code=409,
                    errors=[
                        {
                            'code': 'lock_error',
                            'description': _('Could not confirm appointment'),
                        }
                    ],
                )

        self.verify_booking_version(appointment, data.get('_version'))
        possible_actions = CustomerAppointmentSerializer().get_actions(appointment)
        action_possible = possible_actions.get(action)
        appointment_status = ACTION_TO_STATUS[action]
        # if action false
        if action_possible is not None and not action_possible:
            self.quick_error(
                ('invalid', 'validation', 'action'),
                _('This status transition is not allowed. Forbidden action.'),
            )
            return

        customer_note = data.get('customer_note') or appointment.customer_note

        if KillSwitch.alive(KillSwitch.System.APPOINTMENT_HISTORY):
            changed_fields = ['status', 'business_note', 'customer_note']
            before = appointment.extract_state(fields=changed_fields)

        # DO THE UPDATE
        try:
            with business_context(appointment.business):
                self.update_appointment(
                    appointment.appointment,
                    action,
                    appointment_status,
                    customer_note,
                )
        except BookingConflict as e:
            self.quick_error(
                ('invalid', 'validation', 'action'),
                e.message,
                {'notices': e.notices},
            )
            return

        # cover 'Call for Prepayment' transaction for cancelled appointment
        self.cancel_transaction_for_cancelled_pending_prepayment_appointment(
            original_status=previous_status,
            new_status=appointment_status,
            log_note='CustomerTransactionActionHandler Cancel Payment',
            appointment=appointment,
        )

        # reload appointment
        appointment: AppointmentWrapper = self.get_appointment(
            customer_user_id=self.user.id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
        )

        # release any hanging deposit if booking cancel was within policy
        if (
            action in BOOKING_ACTIONS__RELEASE_DEPOSIT
            and appointment.business.pos
            and (
                (appointment.booked_from - appointment.business.pos.deposit_cancel_time)
                > lib.tools.tznow()
            )
        ):
            ReleaseDepositOnCancel.run(
                appointment_id=appointment.appointment_uid,
            )

        # FINISH
        no_thumbs = data.get('no_thumbs', False)
        serializer = AnalyticsCustomerAppointmentSerializer(
            instance=appointment,
            context={
                'business': appointment.business,
                'single_category': appointment.business.is_single_category,
                'user': self.user,
                'no_thumbs': no_thumbs,
                'device_fingerprint': self.fingerprint,
                'cell_phone': self.user.cell_phone,
            },
        )
        ret = {
            'appointment': serializer.data,
        }

        appointment.send_signal()
        log_action.info(
            'CustomerAppointment action. Type %(type)s. User %(user)s. Action: %(action)s.',
            {
                'type': appointment_type,
                'user': self.user.id,
                'action': action,
            },
        )

        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_CUSTOMER,
            changed_user=self.user,
            handler=self,
            metadata={
                'action': action,
            },
        )

        if KillSwitch.alive(KillSwitch.System.APPOINTMENT_HISTORY):
            state = appointment.extract_state(fields=changed_fields)
            Appointment.history.create(before, state, pk=appointment.appointment_uid)

        if action == BookingAction.CANCEL:
            if tznow() + timedelta(hours=1) > appointment.booked_from:
                create_late_cancellation_notifications_task.run(appointment)

        refund_eligibility = (
            get_refund_eligibility(appointment.appointment)
            if action in BOOKING_ACTIONS__AUTO_REFUND
            else RefundEligibility()
        )
        appointment_status_changed_by_customer_event.send(
            appointment.appointment,
            status=appointment.status,
            previous_status=previous_status,
            refundable=refund_eligibility.refundable,
            is_auto_refund_possible=refund_eligibility.is_auto_refund_possible,
        )

        if previous_status == Appointment.STATUS.PROPOSED:
            if (
                appointment.status == Appointment.STATUS.CANCELED
                and appointment.appointment.is_booksy_gift_card_appointment
            ):
                reissue_gift_card_task.delay(
                    appointment_id=appointment.appointment_uid,
                    reason=ReissueGiftCardReasonEnum.CANCELLED_BY_CUSTOMER,
                )
            # if action is in reaction to Appointment.STATUS.PROPOSED
            # then there is a separate scenario for this
            start_scenario(
                BCS,
                appointment=appointment,
                action=BCS.CUSTOMER_BOOKING_RESCHEDULE_RESPONSE,
            )
        elif appointment_status == Appointment.STATUS.CANCELED:
            # disable till iOS app available https://pm.booksy.net/issues/52764
            self.push_last_minute_incentive(appointment=appointment.appointment)

            start_scenario(
                BCS,
                appointment=appointment,
                action=BCS.CUSTOMER_CANCEL,
            )

            self._process_cancelled_appointment_transaction(
                appointment=appointment,
                is_auto_refund_possible=refund_eligibility.is_auto_refund_possible,
            )
            if appointment.appointment.is_booksy_gift_card_appointment:
                reissue_gift_card_task.delay(
                    appointment_id=appointment.appointment_uid,
                    reason=ReissueGiftCardReasonEnum.CANCELLED_BY_CUSTOMER,
                )

            if appointment.type == Appointment.TYPE.CUSTOMER:
                update_any_mobile_customer_appointments_task.delay(
                    customer_id=appointment.booked_for_id,
                    skip_if_true=False,
                )

        if (
            appointment.status == Appointment.STATUS.NOSHOW
            and appointment.appointment.is_booksy_gift_card_appointment
        ):
            handle_no_show_booksy_gift_card_task.delay(
                appointment_id=appointment.appointment_uid,
                business_id=appointment.business_id,
                charge_bgc=True,
            )

        self.finish_with_json(200, ret)

    @partner_forward(ActionForward)
    def update_appointment(  # pylint: disable=too-many-arguments
        self,
        appointment: Appointment,
        action,
        appointment_status,
        customer_note,
    ):
        if action == BookingAction.ACCEPT:
            charge_prepayment_on_confirm(
                appointment_id=appointment.id,
                device_data=DeviceDataDict(
                    fingerprint=self.fingerprint,
                    phone_number=self.user.cell_phone,
                    user_agent=self.user_agent,
                ),
                extra_data=self.payment_extra_data(),
            )
        # comment moved from BookingActionsRequest.validate:
        # # Booking actions split_repeating_by_booking only in CANCEL action
        # # and only in update_future=True mode.
        # # Any other action or update_future falls back to 'skip'.
        # Customer does not know update_future feature, so never does `update_repeating_booking`
        appointment.update_appointment(
            updated_by_id=self.user.id,
            status=appointment_status,
            customer_note=customer_note,
            who_makes_change=WhoMakesChange.CUSTOMER,
        )

    def _process_cancelled_appointment_transaction(
        self,
        appointment: AppointmentWrapper,
        is_auto_refund_possible: bool = False,
    ):
        """
        Processes transactions for a cancelled appointment.

        For Booksy Pay appointments, it schedules a checkout task. If an auto-refund is possible
        an auto-refund task is chained to run after the checkout.

        For other payment types (e.g., prepaid), it schedules a
        standard checkout task.

        All tasks are run asynchronously via Celery.
        """
        if appointment.is_paid_by_booksy_pay:
            signatures = [
                CheckoutBooksyPayTransaction.si(
                    appointment_id=appointment.appointment_uid,
                    customer_user_id=self.user.id,
                )
            ]
            if is_auto_refund_possible:
                signatures.append(
                    auto_refund_transaction.si(
                        appointment_id=appointment.appointment_uid,
                        customer_user_id=self.user.id,
                    )
                )

            chain(*signatures).apply_async()
        else:
            CheckoutPrepaidTransaction.delay(
                appointment_id=appointment.appointment_uid,
                customer_user_id=self.user.id,
            )


class CustomerAppointmentActionsHandler(OldCustomerAppointmentActionsHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(optional_login=True)
    def post(self, appointment_uid):
        """Execute an action on Appointment.

        swagger:
            summary: Execute an action on Appointment.
            parameters:
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
                - name: body
                  description: Booking action request
                  type: BookingActionRequest
                  paramType: body
                  required: True
            type: CustomerAppointmentResponse
        :swagger
         swaggerModels:
             CustomerAppointment:
                 id: CustomerAppointment
                 required:
                     - status
                     - actions
                     - _version
                 properties:
                     status:
                         type: string
                         description: new status of a booking
                         enum_from_const: booking.Appointment.TYPE_CHOICES
                     _version:
                         type: integer
                         description: Booking version number
                     actions:
                         type: BookingActions
                         description: what actions are allowed
        """
        return super().post(AT.MULTI, appointment_uid)


# pylint: disable=too-many-ancestors
class BusinessAppointmentActionsHandler(BaseAppointmentHandler):
    # pylint: disable=too-many-branches
    # pylint: disable=too-many-statements
    # pylint: disable=too-many-locals
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id, appointment_uid):
        """Execute an action on Appointment.

        swagger:
            summary: Execute an action on Appointment.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
                - name: body
                  description: Booking action request
                  type: BookingActionRequest
                  paramType: body
                  required: True
            type: AppointmentResponse
        """
        # pylint: disable=too-many-branches
        business = self.business_with_staffer(business_id)
        appointment = self.get_appointment(
            business_id=business_id,
            appointment_type=AT.MULTI,
            appointment_id=appointment_uid,
            prefetch_all=False,
        )
        if not appointment.check_access(self.user_staffer):
            raise HTTPErrorWithCode(404, reason=_('Access Denied'))

        # validate request
        serializer = BookingActionsRequest(data=self.data)
        data = self.validate_serializer(serializer)
        action = data['action']

        previous_status = appointment.status

        if action == BookingAction.CONFIRM and appointment_uid:
            lock_acquired = ConfirmAppointmentPrepaymentLock.try_to_lock(appointment_uid)
            if not lock_acquired:
                return self.return_error(
                    code=409,
                    errors=[
                        {
                            'code': 'lock_error',
                            'description': _('Could not confirm appointment'),
                        }
                    ],
                )

        self.verify_booking_version(appointment, data.get('_version'))
        business_note = data.get('business_note') or appointment.business_note
        release_deposit = data.get('release_deposit')

        if KillSwitch.alive(KillSwitch.System.APPOINTMENT_HISTORY):
            changed_fields = ['status', 'business_note', 'customer_note']
            before = appointment.extract_state(fields=changed_fields)

        # Kosyl legacy hack:
        # TODO: replace with compute_new_status after 30849-free-willy merge
        ugly_status_transition_hack = False
        if appointment.booked_till >= lib.tools.tznow() and action == BookingAction.CANCEL_NO_SHOW:
            action = BookingAction.CONFIRM
            ugly_status_transition_hack = True

        # must be after ugly_status_transition_hack
        appointment_status = ACTION_TO_STATUS[action]

        # DO THE UPDATE
        try:
            if action == BookingAction.CONFIRM:
                charge_prepayment_on_confirm(
                    appointment_id=appointment.appointment_uid,
                    # Money charged by business should not save device data
                    # in order to prevent any fraud score errors.
                    # Otherwise, it would result in a business device info
                    # being associated with customer's payment card.
                )
            # comment moved from BookingActionsRequest.validate:
            # # Booking actions split_repeating_by_booking only in CANCEL action
            # # and only in update_future=True mode.
            # # Any other action or update_future falls back to 'skip'.
            if (
                appointment.repeating
                and action == BookingAction.CANCEL
                and data['_update_future_bookings']
            ):
                appointment.appointment.repeating.update_repeating_booking(
                    prototype=appointment.appointment,
                    updated_by=self.user,
                    status=appointment_status,
                    business_note=business_note,
                    who_makes_change=WhoMakesChange.BUSINESS,
                    force_status_transition=ugly_status_transition_hack,
                    update_future=True,
                )
            else:
                appointment.appointment.update_appointment(
                    updated_by=self.user,
                    status=appointment_status,
                    business_note=business_note,
                    who_makes_change=WhoMakesChange.BUSINESS,
                    force_status_transition=ugly_status_transition_hack,
                )
        except BookingConflict as e:
            self.quick_error(
                ('invalid', 'validation', 'action'),
                e.message,
                {'notices': e.notices},
            )
            return

        appointment.send_signal()
        log_action.info(
            'Edit BusinessBooking action. User %(user)s. '
            'Action: %(action)s Appointment: %(type)s-%(id)s.',
            {
                'user': self.user.id,
                'action': action,
                'type': AT.MULTI,
                'id': appointment_uid,
            },
        )
        if action == BookingAction.NO_SHOW:
            customer_no_show.send(appointment.appointment)
            first_no_show = business.events.first_no_show
            if not first_no_show:
                from webapps.notification.scenarios.scenarios_noshowprotection import (
                    NoShowPropositionScenario,
                )

                # Mark first_no_show event as used
                business.events.first_no_show = True
                business.events.save()

                from webapps.segment.tasks import analytics_1st_no_show_for_business_task

                analytics_1st_no_show_for_business_task.delay(
                    booking_id=appointment.subbookings[0].id,
                    context={
                        'business_id': business_id,
                        'event_type': EventType.BUSINESS,
                        'source_id': self.booking_source.id,
                    },
                )

                start_scenario(
                    NoShowPropositionScenario,
                    business_id=business.id,
                    booking_id=appointment.subbookings[0].id,
                )

            self.spawn_no_show_analytics(
                appointment=appointment,
                business_id=business_id,
                source_id=self.booking_source.id,
            )

            no_show_transaction = Transaction.objects.by_appointment_id(
                appointment.appointment.id,
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
                latest_receipt__status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            ).last()
            if no_show_transaction:
                register = business.pos.get_open_register(
                    operator=self.user,
                    takeover_when_only_one_is_open=self.is_frontdesk,
                )
                provider = get_payment_provider(
                    codename=no_show_transaction.latest_receipt.payment_rows.get().provider,
                    txn=no_show_transaction,
                )
                if release_deposit:
                    provider.cancel_deposit(no_show_transaction)
                else:
                    provider.charge_deposit(
                        no_show_transaction,
                        operator=self.user,
                        register=register,
                        device_data=DeviceDataDict(
                            fingerprint=self.fingerprint,
                            phone_number=self.user.cell_phone,
                            user_agent=self.user_agent,
                        ),
                    )

            change_family_and_friends_client_type_if_needed(appointment.appointment)

        # cover 'Call for Prepayment' transaction for cancelled appointment
        self.cancel_transaction_for_cancelled_pending_prepayment_appointment(
            original_status=previous_status,
            new_status=appointment_status,
            log_note='BusinessAppointmentActionsHandler Cancel Payment',
            appointment=appointment,
        )

        # reload appointment
        appointment: AppointmentWrapper = self.get_appointment(
            business_id=business_id,
            appointment_type=AT.MULTI,
            appointment_id=appointment_uid,
        )

        # FINISH
        customer_info = appointment.booked_for_data(access_level=self.access_level) or {}

        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_BUSINESS,
            changed_user=self.user,
            handler=self,
            metadata={
                'action': action,
                'ugly_status_transition_hack': ugly_status_transition_hack,
            },
        )

        if KillSwitch.alive(KillSwitch.System.APPOINTMENT_HISTORY):
            state = appointment.extract_state(fields=changed_fields)
            Appointment.history.create(before, state, pk=appointment.appointment_uid)

        _notify_sms = data.get('_notify_about_cancel_sms')
        # validate _notify_about_cancel_sms
        if _notify_sms:
            phone = None
            if customer_info:
                # check if customer has phone
                merged_data = customer_info['merged_data']
                if merged_data:
                    phone = merged_data['cell_phone']
                if phone is None:
                    business_customer = customer_info['business_customer']
                    if business_customer:
                        phone = business_customer['cell_phone']
                # decide
            _notify_sms = _notify_sms if phone is not None else False

        if action == BookingAction.CANCEL:
            appointment_canceled_by_business_event.send(
                appointment.appointment,
                notify_about_cancel_email=data.get('_notify_about_cancel_email'),
                notify_about_cancel_sms=_notify_sms,
            )
            self._update_tuning_update_any_mobile_customer_appointments_task(appointment)
            change_family_and_friends_client_type_if_needed(appointment.appointment)
        elif action == BookingAction.CONFIRM:
            appointment_confirmed_by_business_event.send(
                appointment.appointment,
            )
            self._update_tuning_update_any_mobile_customer_appointments_task(
                appointment,
                skip_if_true=True,
            )
        elif action == BookingAction.DECLINE:
            appointment_declined_by_business_event.send(
                appointment.appointment,
            )
        elif action == BookingAction.NO_SHOW:
            appointment_no_show_by_business_event.send(
                appointment.appointment,
            )
            self._update_tuning_update_any_mobile_customer_appointments_task(appointment)
            if appointment.appointment.is_booksy_gift_card_appointment:
                handle_no_show_booksy_gift_card_task.delay(
                    appointment_id=appointment.appointment_uid,
                    business_id=business.id,
                    charge_bgc=data.get('charge_bgc', True),
                )

        start_scenario(
            BCS,
            appointment=appointment,
            # use original action, before ugly hack
            action=ACTION_TO_SCENARIO_ACTION.get(data['action']),
            # _notify_about_cancel
            _notify_about_cancel_email=True,
            _notify_about_cancel_sms=True,
        )

        if (
            action in BOOKING_ACTIONS__RELEASE_DEPOSIT
            and appointment.business.pos
            and (
                (
                    (appointment.booked_from - appointment.business.pos.deposit_cancel_time)
                    > lib.tools.tznow()
                )
                or release_deposit
                or action == BookingAction.DECLINE  # always release
            )
        ):
            ReleaseDepositOnCancel.delay(
                appointment_id=appointment.appointment_uid,
            )

        if (
            action in BOOKING_ACTIONS__AUTO_CHECKOUT_TRANSACTION
            and not Transaction.objects.filter(
                appointment=appointment.id,
                latest_receipt__status_code=receipt_status.REFUNDED,
            ).exists()
        ):
            if appointment.is_paid_by_booksy_pay:
                # Execute synchronously to return the latest payment row created after checkout
                CheckoutBooksyPayTransaction(
                    appointment_id=appointment.appointment_uid,
                    customer_user_id=self.user.id,
                )
                appointment.clear_cache_payment_and_deposit()
            else:
                CheckoutPrepaidTransaction.delay(
                    appointment_id=appointment.appointment_uid,
                    business_id=business_id,
                )
            if appointment.appointment.is_booksy_gift_card_appointment:
                reissue_gift_card_task.delay(
                    appointment_id=appointment.appointment_uid,
                    reason=ReissueGiftCardReasonEnum.CANCELLED_BY_BUSINESS,
                )

        appointment_serializer = AnalyticsAppointmentSerializer(
            instance=appointment,
            context={
                'business': business,
                'access_level': self.access_level,
                'single_category': business.is_single_category,
            },
        )

        ret = {
            'appointment': appointment_serializer.data,
            'customer': customer_info,
        }

        self.finish_with_json(200, ret)

    @staticmethod
    def spawn_no_show_analytics(appointment: Appointment, business_id: int, source_id: int):
        if appointment.appointment.type == AppointmentType.BUSINESS.value:
            from webapps.segment.tasks import analytics_bb_no_show_for_business_task

            analytics_bb_no_show_for_business_task.delay(
                booking_id=appointment.subbookings[0].id,
                context={
                    'business_id': business_id,
                    'event_type': EventType.BUSINESS,
                    'source_id': source_id,
                },
            )
        elif appointment.appointment.type == AppointmentType.CUSTOMER.value:
            from webapps.segment.tasks import analytics_cb_no_show_for_business_task

            analytics_cb_no_show_for_business_task.delay(
                booking_id=appointment.subbookings[0].id,
                context={
                    'business_id': business_id,
                    'event_type': EventType.BUSINESS,
                    'source_id': source_id,
                },
            )

    @staticmethod
    def _update_tuning_update_any_mobile_customer_appointments_task(
        appointment: Appointment,
        skip_if_true=False,
    ):
        if (appointment.type == Appointment.TYPE.CUSTOMER) and appointment.booked_for:
            update_any_mobile_customer_appointments_task.delay(
                customer_id=appointment.booked_for_id,
                skip_if_true=skip_if_true,
            )


# pylint: disable=too-many-ancestors
class OldBusinessAppointmentNotifyReadyHandler(BaseAppointmentHandler):
    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id, appointment_type, appointment_id):
        """DEPRECATED Ticket: #64517

        swagger:
            summary: DEPRECATED Notify customer that merchant is ready for the appointment
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: appointment_type
                  type: string
                  paramType: path
                  description: Appointment Type (single or multi)
                  enum:
                    - single
                    - multi
                - name: appointment_id
                  type: integer
                  paramType: path
                  description: (Multi-)Booking ID
        :swagger
        """
        self.business_with_staffer(business_id)
        appointment = self.get_appointment(
            business_id=business_id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
            prefetch_all=False,
        )
        lib.tools.sasrt(
            appointment.status == Appointment.STATUS.ACCEPTED,
            status.HTTP_400_BAD_REQUEST,
            [
                {
                    'code': 'invalid',
                    'type': 'validation',
                    'description': _(
                        'Invalid appointment status: {}. '
                        'This notification can be send only for confirmed '
                        'appointments.'
                    ).format(appointment.status),
                }
            ],
        )
        business_ready_for_appointment.send(appointment.appointment)
        self.finish_with_json(status.HTTP_200_OK, {})


class BusinessAppointmentNotifyReadyHandler(  # pylint: disable=too-many-ancestors
    OldBusinessAppointmentNotifyReadyHandler
):
    @session(optional_login=True)
    def post(self, business_id, appointment_uid):
        """
        swagger:
            summary: Notify customer that merchant is ready for the appointment
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: Appointment ID
        :swagger
        """
        return super().post(business_id, AT.MULTI, appointment_uid)
