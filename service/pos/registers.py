#!/usr/bin/env python
import logging
from django.core.paginator import Paginator, EmptyPage
from django.db.models.query import Prefetch
from django.utils.translation import gettext as _

from lib import jinja_renderer
from bo_obs.datadog.enums import BooksyTeams
from lib.email import send_email
from lib.serializers import PaginatorSerializer, SendEmailRequest
from service.tools import session, json_request, RequestHandler, deprecated_tornado_handler
from webapps.notification.models import NotificationHistory
from webapps.pos.models import POS
from webapps.register.models import RegisterClosingAmount, Register
from webapps.register.serializers import (
    RegisterListingSerializer,
    RegisterSerializer,
    RegisterOperationSerializer,
    RegisterOpenSerializer,
    RegisterReOpenSerializer,
    CashInOutSerializer,
)

from lib.pdf_render import RegisterPDFReportRenderer


log = logging.getLogger('booksy.registers')


class BaseRegisterHandler(RequestHandler):
    """Base class for all Register related handlers.

    It has some common methods for validation of POS and Registers
    given in URL pattern parameters.

    """

    def validate_pos(self, business_id):
        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_advanced_staffer(pos.business)
        return pos

    def validate_register(self, business_id, register_id):
        pos = self.validate_pos(business_id)
        register = self.get_object_or_404(
            pos.registers.all().prefetch_related('operations'),
            id=register_id,
        )
        return register

    def validate_operation(self, business_id, register_id, operation_id):
        register = self.validate_register(business_id, register_id)
        operation = self.get_object_or_404(
            register.operations.filter(
                # only Cash In/Out operations
                receipt__isnull=True,
            ),
            id=operation_id,
        )
        return operation


class RegistersHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id):
        """Get list of Registers.

        swagger:
            summary: Get list of Registers.
            notes: Registers are ordered from the newest to oldest
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
                - name: only_open
                  description: Return only open cash registers
                  type: string
                  paramType: query
                  default: 0
            type: RegisterListingResponse

        """
        pos = self.validate_pos(business_id)

        # validate GET data
        data = self._prepare_get_arguments()
        only_open = {'1': True, '0': False, 'true': True, 'false': False}.get(
            data.get('only_open'), False
        )

        serializer = PaginatorSerializer(data=data)
        data = self.validate_serializer(serializer)

        # paginate registers
        registers_qs = (
            pos.registers.select_related(
                'opened_by',
                'closed_by',
            )
            .prefetch_related(
                Prefetch(
                    'closing_amounts',
                    queryset=RegisterClosingAmount.objects.select_related('payment_type'),
                    to_attr='existing_summaries',  # cached_property
                ),
            )
            .order_by('-is_open', '-opened_at')
        )
        if only_open:
            registers_qs = registers_qs.filter(is_open=True)
        pager = Paginator(registers_qs, data['per_page'])
        try:
            page = pager.page(data['page'])
        except EmptyPage:
            registers = []
        else:
            registers = list(page.object_list)

        # inject operation totals
        Register.bulk_fetch_operation_totals(registers)

        # return response
        self.finish_with_json(
            200,
            {
                'page': data['page'],
                'per_page': data['per_page'],
                'count': pager.count,
                'registers': RegisterListingSerializer(
                    instance=registers,
                    many=True,
                    context={'pos': pos},
                ).data,
            },
        )

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """Open new Register.

        swagger:
            summary: Open new Register.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: body
                  paramType: body
                  type: RegisterOpenRequest
                  description: data of Register to open
            type: RegisterDetailsResponse

        """
        pos = self.validate_pos(business_id)

        # validate and create an open Register
        serializer = RegisterOpenSerializer(
            instance=None,
            data=self.data,
            context={
                'pos': pos,
                'user': self.user,
            },
        )
        self.validate_serializer(serializer)
        register = serializer.save()

        # return Register details
        self.finish_with_json(
            201,
            {
                'register': RegisterSerializer(
                    instance=register,
                ).data,
            },
        )


class RegisterDetailsHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id, register_id):
        """Get Register details.

        swagger:
            summary: Get Register details.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
            type: RegisterDetailsResponse

        """
        register = self.validate_register(business_id, register_id)
        Register.bulk_fetch_operation_totals([register])

        self.finish_with_json(
            200,
            {
                'register': RegisterSerializer(
                    instance=register,
                ).data,
            },
        )


class RegisterCloseHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @json_request
    def post(self, business_id, register_id):
        """Close the Register and update counted summary values.

        swagger:
            summary: Close the Register and update counted summary values.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
                - name: body
                  paramType: body
                  type: RegisterCloseRequest
                  description: data of Register to close
            type: RegisterDetailsResponse

        """
        register = self.validate_register(business_id, register_id)

        # validate counted summaries and close the Register
        serializer = RegisterSerializer(
            instance=register,
            data=self.data,
            context={
                'user': self.user,
            },
        )
        self.validate_serializer(serializer)
        register = serializer.save()

        # return Register details
        self.finish_with_json(
            201,
            {
                'register': RegisterSerializer(
                    instance=register,
                ).data,
            },
        )


class RegisterReopenHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @json_request
    def post(self, business_id, register_id):
        """Reopen Register.

        swagger:
            summary: Reopen Register.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
                - name: body
                  paramType: body
                  type: object
                  description: no data is required, please send empty object
            type: RegisterDetailsResponse

        """
        register = self.validate_register(business_id, register_id)

        # validate counted summaries and close the Register
        serializer = RegisterReOpenSerializer(
            instance=register,
            data={},  # no data is needed, only validation
            context={
                'user': self.user,
            },
        )
        self.validate_serializer(serializer)
        register = serializer.save()

        # return Register details
        self.finish_with_json(
            201,
            {
                'register': RegisterSerializer(
                    instance=register,
                ).data,
            },
        )


class RegisterOperationsHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id, register_id):
        """Get list of RegisterOperations for given Register.

        swagger:
            summary: Get list of RegisterOperations for given Register.
            notes: RegisterOperations are ordered from the newest to oldest
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
                - name: new_format
                  type: string
                  paramType: query
                  description: (For new aplications) If parametr exists will also be sent open and close records
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
            type: RegisterOperationsListingResponse

        """
        register = self.validate_register(business_id, register_id)

        # validate GET data
        data = self._prepare_get_arguments()
        new_format = True if data.get('new_format') else False
        serializer = PaginatorSerializer(data=data)
        data = self.validate_serializer(serializer)

        # paginate operations
        pager = Paginator(register.get_visible_operations(new_format), data['per_page'])
        try:
            page = pager.page(data['page'])
        except EmptyPage:
            operations = []
        else:
            operations = list(page.object_list)

        # return response
        self.finish_with_json(
            200,
            {
                'page': data['page'],
                'per_page': data['per_page'],
                'count': pager.count,
                'operations': RegisterOperationSerializer(
                    instance=operations,
                    many=True,
                    context={'business': register.pos.business},
                ).data,
            },
        )

    @session(login_required=True)
    @json_request
    def post(self, business_id, register_id):
        """Create Cash In/Out RegisterOperation for given Register.

        swagger:
            summary: Create Cash In/Out RegisterOperation for given Register.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
                - name: body
                  paramType: body
                  type: RegisterCashInOutRequest
                  description: details of Cash In/Out RegisterOperation
            type: RegisterOperationResponse

        """
        register = self.validate_register(business_id, register_id)

        # validate and create the Cash In/Out operation
        serializer = CashInOutSerializer(
            instance=None,
            data=self.data,
            context={'register': register, 'user': self.user},
        )
        self.validate_serializer(serializer)
        operation = serializer.save()

        # return RegisterOperation details
        self.finish_with_json(
            201,
            {
                'operation': RegisterOperationSerializer(
                    instance=operation,
                    context={'business': register.pos.business},
                ).data,
            },
        )


class RegisterReportDownloadHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id, register_id):
        """Return pdf report for given register.

        swagger:
            summary: Return file with pdf report to download
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
        """
        register = self.validate_register(business_id, register_id)

        renderer = RegisterPDFReportRenderer(
            language=self.language,
            register=register,
        )
        report = renderer.render_pdf()

        self.write(report)
        self.set_header('Content-Type', 'application/pdf')
        self.set_header(
            'Content-Disposition', f'attachment; filename=cash_register_report_{register_id}.pdf'
        )
        self.set_status(200)


class RegisterOperationSendEmailHandler(BaseRegisterHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @json_request
    def post(self, business_id, register_id, operation_id):
        """Send email with pseudo-receipt for Cash In/Out operation.

        swagger:
            summary: Send email with pseudo-receipt for Cash In/Out operation.
            notes: >
                It only supports Cash In/Out operations.
                For operations with receipts, please use proper
                transaction receipt handler.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: register_id
                  type: integer
                  paramType: path
                  description: Register id
                - name: operation_id
                  type: integer
                  paramType: path
                  description: RegisterOperation id
                - name: body
                  paramType: body
                  description: optional email, defaults to current user
                  type: SendEmailRequest
            type: SendEmailResponse

        """
        operation = self.validate_operation(business_id, register_id, operation_id)

        serializer = SendEmailRequest(
            data=self.data or {},
            context={
                'email': self.user.email,
            },
        )
        data = self.validate_serializer(serializer)

        sjr = jinja_renderer.ScenariosJinjaRenderer()
        body = 'TODO: create an email template for this' or sjr.render(
            scenario_name='register',
            template_name='register',
            language=self.language,
            template_args={
                '_': _,
                'tz': operation.register.pos.business.get_timezone(),
                'user': self.user,
                'operation': operation,
            },
            extension='html',
            default=(),
        )

        send_email(
            data['email'],
            body,
            history_data={
                'sender': NotificationHistory.SENDER_BUSINESS,
                'business_id': operation.register.pos.business.id,
                'customer_id': None,
                'task_id': 'pos:register:register_id=%d:operation_id=%d'
                % (
                    operation.register.id,
                    operation.id,
                ),
            },
            to_name=self.user.full_name,
        )

        self.finish_with_json(200, {'email': data['email']})
