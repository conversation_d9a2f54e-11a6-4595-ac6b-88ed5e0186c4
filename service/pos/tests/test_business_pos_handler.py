from decimal import Decimal

import mock
import pytest
from django.conf import settings
from django.test import override_settings
from mock import MagicMock, patch
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status
from segment.analytics import Client

from country_config import Country
from lib.feature_flag.feature.payment import (
    ShowNewFinancialCenterFlag,
    ShowBooksyWalletForTrialKYCPx,
)
from lib.payment_providers.entities import (
    PortResponse,
    ProviderAccountStatus,
)
from lib.payment_providers.enums import ProviderAccountHolderStatus, ResponseEntityType
from lib.payments.enums import PaymentProviderCode
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import id_to_external_api
from service.pos.tests.common import POSTestsMixin
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.business.enums import PriceType
from webapps.business.models import Business, ServiceAddOn
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.kill_switch.models import KillSwitch
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS, PaymentType, TaxRate
from webapps.pos.tasks import remove_square_payment_method_for_selected_pos_ids


@pytest.mark.django_db
class BusinessPOSHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/pos/?'

    def test_get(self):
        response = self.fetch(self.url.format(self.business.id))
        assert response.code == status.HTTP_200_OK
        assert len(response.json) == 1
        response_keys = response.json['pos'].keys()
        assert set(response_keys).issuperset(
            {
                'tax_rates',
                'payment_types',
                'deposit_cancel_time',
                'bank_account',
                'auto_charge_cancellation_fee',
                'products_stock_enabled',
                'registers_enabled',
                'registers_max_opened',
                'registers_reopening',
                'registers_shared_enabled',
                'waive_amount',
                'pay_by_app_status',
                'receipt_footer_line_1',
                'tips_enabled',
                'tips',
                'donations_enabled',
                'bsx_settings',
                'active',
                'service_tax_mode',
                'product_tax_mode',
                'voucher_tax_mode',
                'tip_calculation_mode',
                'deprecated_tip_rounding_mode',
                'tax_in_receipt_visible',
                'commissions_enabled',
                'payment_auto_accept',
                'force_pba_for_cf',
                'force_stripe_pba',
                'force_stripe_kyc',
                'item_discount_enabled',
                'global_discount_enabled',
                'deposit_policy',
                'pay_by_app_request_date',
                'prepayment_enabled',
                'refund_enabled',
                'service_fee',
                'deprecated_pos_plan',
                'deprecated_pos_plan_locked',
                'pos_plans',
                'receipt_footer_line_2',
                'marketpay_enabled',
                'stripe_terminal_enabled',
                'business_id',
                'no_show_cancel_time_options',
                'online_payment_status',
                'has_marketpay_account',
                'marketpay_payouts_enabled',
                'tap_to_pay_enabled',
                'booksy_pay_enabled',
            }
        )

    @override_settings(POS__SQUARE=True)
    def test_get_payment_types(self):
        pos = POS.get_default_pos()
        assert pos.business is None
        for payment_type in PaymentType.UNDELETABLE_TYPES:
            baker.make(PaymentType, pos=pos, code=payment_type)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.SQUARE)

        response = self.fetch(self.url.format(self.business.id))
        response_payment_types = response.json['pos']['payment_types']
        assert len(response_payment_types) == 4
        assert [x['code'] for x in response_payment_types] == [
            'cash',
            'prepayment',
            'split',
            'square',
        ]
        assert PaymentType.objects.filter(
            pos=self.business.pos,
            code=PaymentTypeEnum.SQUARE,
        ).exists()

    @override_settings(POS__SQUARE=True)
    def test_get_payment_types_square_deprecated(self):
        self.test_get_payment_types()  # it is creating square payment method for current business
        PaymentType.objects.filter(
            pos=self.business.pos,
            code=PaymentTypeEnum.SQUARE,
        ).update(
            enabled=True,
        )
        remove_square_payment_method_for_selected_pos_ids.run([self.business.pos.id])
        assert PaymentType.objects.filter(
            pos=self.business.pos,
            code=PaymentTypeEnum.SQUARE,
            enabled=False,
            available=False,
        ).exists()

        response = self.fetch(self.url.format(self.business.id))
        response_payment_types = response.json['pos']['payment_types']
        assert len(response_payment_types) == 3
        assert [x['code'] for x in response_payment_types] == [
            'cash',
            'prepayment',
            'split',
        ]

    @parameterized.expand(
        (
            (False, False, False),
            (False, True, False),
            (True, False, False),
            (True, True, True),
        )
    )
    def test_get_show_new_financial_center(
        self, mocked_flag_value, mocked_force_stripe_pba, mocked_show_new_financial_center
    ):
        baker.make(
            POS,
            business=self.business,
            pos_refactor_stage2_enabled=mocked_force_stripe_pba,
            _force_stripe_pba=mocked_force_stripe_pba,
        )

        with override_eppo_feature_flag({ShowNewFinancialCenterFlag.flag_name: mocked_flag_value}):
            response = self.fetch(self.url.format(self.business.id))
            assert (
                response.json['pos']['show_new_financial_center']
                == mocked_show_new_financial_center
            )

    def test_get_show_new_financial_center_status_trial(self):
        baker.make(
            POS,
            business=self.business,
            pos_refactor_stage2_enabled=True,
            _force_stripe_pba=True,
        )

        self.business.status = Business.Status.TRIAL
        self.business.save()

        with override_eppo_feature_flag({ShowNewFinancialCenterFlag.flag_name: True}):
            response = self.fetch(self.url.format(self.business.id))
            assert response.json['pos']['show_new_financial_center'] is False

    @mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_business_wallet', MagicMock())
    @mock.patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.get_provider_account_status'
    )
    def _test_base_show_new_financial_center_trial_status(
        self,
        get_provider_account_status_mock,
        account_holder_status,
        show_new_financial_center,
    ):
        get_provider_account_status_mock.return_value = PortResponse(
            entity=ProviderAccountStatus(
                payment_provider_code=PaymentProviderCode.STRIPE,
                status=account_holder_status,
                payouts_enabled=False,
                kyc_verified_at_least_once=False,
            ),
            entity_type=ResponseEntityType.ACCOUNT_HOLDER_STATUS_ENTITY,
        )
        baker.make(
            POS,
            business=self.business,
            pos_refactor_stage2_enabled=True,
            _force_stripe_pba=True,
        )

        self.business.status = Business.Status.TRIAL
        self.business.save()

        with override_eppo_feature_flag(
            {
                ShowNewFinancialCenterFlag.flag_name: True,
                ShowBooksyWalletForTrialKYCPx.flag_name: True,
            }
        ):
            response = self.fetch(self.url.format(self.business.id))
            assert response.json['pos']['show_new_financial_center'] is show_new_financial_center

    @override_settings(API_COUNTRY=Country.US)
    def test_get_show_new_financial_center_status_trial_kyc_verified_us(self):
        self._test_base_show_new_financial_center_trial_status(  # pylint: disable=no-value-for-parameter
            account_holder_status=ProviderAccountHolderStatus.VERIFIED,
            show_new_financial_center=True,
        )

    @override_settings(API_COUNTRY=Country.US)
    def test_get_show_new_financial_center_status_trial_kyc_not_verified_us(self):
        self._test_base_show_new_financial_center_trial_status(  # pylint: disable=no-value-for-parameter
            account_holder_status=ProviderAccountHolderStatus.NOT_VERIFIED,
            show_new_financial_center=False,
        )

    @override_settings(API_COUNTRY=Country.PL)
    def test_get_show_new_financial_center_status_trial_kyc_verified_pl(self):
        #   not possible case in current state
        self._test_base_show_new_financial_center_trial_status(  # pylint: disable=no-value-for-parameter
            account_holder_status=ProviderAccountHolderStatus.VERIFIED,
            show_new_financial_center=False,
        )

    @override_settings(API_COUNTRY=Country.PL)
    def test_get_show_new_financial_center_status_trial_kyc_not_verified_pl(self):
        self._test_base_show_new_financial_center_trial_status(  # pylint: disable=no-value-for-parameter
            account_holder_status=ProviderAccountHolderStatus.NOT_VERIFIED,
            show_new_financial_center=False,
        )

    def set_business_package(self, business_package: Business.Package):
        self.business.package = business_package
        self.business.save(update_fields=['package'])
        self.business.pos.auto_charge_cancellation_fee = True
        self.business.pos.save(update_fields=['auto_charge_cancellation_fee'])

    @override_settings(POS=True)
    @override_settings(POS__PAY_BY_APP=True)
    def test_put_bsx_settings(self, *_):
        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'bsx_settings': None,
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)

    @override_settings(POS=True)
    @override_settings(POS__PAY_BY_APP=True)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_put(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'products_stock_enabled': True,
                'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                'donations_enabled': True,
                'auto_charge_cancellation_fee': False,
            },
        )
        assert response.code == status.HTTP_200_OK
        resp_pos = response.json['pos']
        assert resp_pos['pay_by_app_status'] == POS.PAY_BY_APP_PENDING
        assert resp_pos['donations_enabled'] is True
        assert resp_pos['products_stock_enabled'] is True
        assert resp_pos['auto_charge_cancellation_fee'] is False
        assert resp_pos['stripe_account_tokens_enabled'] is False
        assert analytics_track_mock.call_count == 1
        assert analytics_identify_mock.call_count == 1
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.business.owner.id),
                'event': 'Business_POS_Updated',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'id': id_to_external_api(self.business.id),
                    'name': self.business.name,
                    'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                    'donations_enabled': True,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.business.owner.id),
                'traits': {
                    'event_name': 'python.analytics_business_pos_updated_task',
                    'email': self.business.owner.email,
                    'country': settings.API_COUNTRY,
                    'user_role': 'Owner',
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                    'donations_enabled': True,
                },
            },
        )
        self.set_business_package(self.business.Package.PRO)
        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'products_stock_enabled': False,
                'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                'donations_enabled': True,
                'auto_charge_cancellation_fee': False,
            },
        )
        assert response.code == status.HTTP_200_OK
        resp_pos = response.json['pos']
        assert resp_pos['pay_by_app_status'] == POS.PAY_BY_APP_PENDING
        assert resp_pos['donations_enabled'] is True
        assert resp_pos['products_stock_enabled'] is False
        assert resp_pos['auto_charge_cancellation_fee'] is False
        assert resp_pos['stripe_account_tokens_enabled'] is False

        self.set_business_package(self.business.Package.LITE)
        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'products_stock_enabled': False,
                'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                'donations_enabled': False,
                'auto_charge_cancellation_fee': False,
            },
        )
        assert response.code == status.HTTP_200_OK
        resp_pos = response.json['pos']
        assert resp_pos['pay_by_app_status'] == POS.PAY_BY_APP_PENDING
        assert resp_pos['donations_enabled'] is False
        assert resp_pos['products_stock_enabled'] is False
        assert resp_pos['auto_charge_cancellation_fee'] is False
        assert resp_pos['stripe_account_tokens_enabled'] is False
        assert analytics_track_mock.call_count == 2
        assert analytics_identify_mock.call_count == 2
        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'user_id': id_to_external_api(self.business.owner.id),
                'event': 'Business_POS_Updated',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'id': id_to_external_api(self.business.id),
                    'name': self.business.name,
                    'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                    'donations_enabled': False,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'user_id': id_to_external_api(self.business.owner.id),
                'traits': {
                    'event_name': 'python.analytics_business_pos_updated_task',
                    'email': self.business.owner.email,
                    'country': settings.API_COUNTRY,
                    'user_role': 'Owner',
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                    'donations_enabled': False,
                },
            },
        )

    def test_booksy_gift_cards_are_permanently_enabled(self):
        self.pos = baker.make(POS, business=self.business)
        self.payment = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.BOOKSY_GIFT_CARD)
        self.pos.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == 200
        assert len(resp.json['pos']['payment_types']) == 1
        assert resp.json['pos']['payment_types'][0]["code"] == "booksy_gift_card"
        assert resp.json['pos']['payment_types'][0]["editable"] is False
        assert resp.json['pos']['payment_types'][0]["enabled"] is True

    @override_settings(POS=True)
    @override_settings(POS__PAY_BY_APP=True)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_put_analytics_killswitch(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_POS_UPDATED,
            is_killed=True,
        )
        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'products_stock_enabled': True,
                'pay_by_app_status': POS.PAY_BY_APP_PENDING,
                'donations_enabled': True,
            },
        )
        assert response.code == status.HTTP_200_OK
        resp_pos = response.json['pos']
        assert resp_pos['pay_by_app_status'] == POS.PAY_BY_APP_PENDING
        assert resp_pos['donations_enabled'] is True
        assert resp_pos['products_stock_enabled'] is True
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0


@pytest.mark.django_db
class FCBusinessPOSHandlerTestCase(POSTestsMixin, BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/pos/?'

    @french_certification_enabled(certification_enabled=True)
    def test_put_remove_tax_rate_is_blocked(self):
        pos = self.create_pos(self.business)
        default_tax_rate = baker.make(
            TaxRate,
            pos=pos,
            rate=Decimal('10.00'),
            default_for_service=True,
            default_for_product=True,
        )
        baker.make(
            TaxRate,
            pos=pos,
            rate=Decimal('20.00'),
            default_for_service=False,
            default_for_product=False,
        )

        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'tax_rates': [
                    {
                        'id': default_tax_rate.id,
                        'rate': '10.00',
                        'default_for_service': True,
                        'default_for_product': True,
                    }
                ]
            },
        )

        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

    @french_certification_enabled(certification_enabled=True)
    def test_put_add_5_digit_tax_rate_is_blocked(self):
        pos = self.create_pos(self.business)
        default_tax_rate = baker.make(
            TaxRate,
            pos=pos,
            rate=Decimal('10.00'),
            default_for_service=True,
            default_for_product=True,
        )

        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'tax_rates': [
                    {
                        'id': default_tax_rate.id,
                        'rate': '10.00',
                        'default_for_service': True,
                        'default_for_product': True,
                    },
                    {
                        'rate': '100.00',
                        'default_for_service': False,
                        'default_for_product': False,
                    },
                ]
            },
        )

        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

    @french_certification_enabled(certification_enabled=True)
    def test_put_add_valid_tax_rate_is_allowed(self):
        pos = self.create_pos(self.business)
        default_tax_rate = baker.make(
            TaxRate,
            pos=pos,
            rate=Decimal('10.00'),
            default_for_service=True,
            default_for_product=True,
        )

        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'tax_rates': [
                    {
                        'id': default_tax_rate.id,
                        'rate': '10.00',
                        'default_for_service': True,
                        'default_for_product': True,
                    },
                    {
                        'rate': '20.00',
                        'default_for_service': False,
                        'default_for_product': False,
                    },
                ]
            },
        )

        self.assertEqual(response.code, status.HTTP_200_OK)
        tax_rates = pos.tax_rates.values_list('rate', flat=True)
        self.assertSetEqual({Decimal('10.00'), Decimal('20.00')}, set(tax_rates))

    @override_settings(API_COUNTRY=Country.FR, STRIPE_ACCOUNT_TOKENS_ENABLED=True)
    def test_stripe_account_tokens_enabled_for_france(self):
        response = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={},
        )
        self.assertEqual(
            response.code,
            status.HTTP_200_OK,
        )
        self.assertEqual(
            response.json['pos']['stripe_account_tokens_enabled'],
            True,
        )


@pytest.mark.django_db
class ApplyDefaultTaxRateHandlerTestCase(POSTestsMixin, BaseAsyncHTTPTest):
    def test_update_taxes(self):
        pos = self.create_pos(self.business)
        self.create_default_tax_rate_20(pos)
        service, _ = self.create_service_and_service_variant(self.business)
        addon = baker.make(
            ServiceAddOn,
            name='some addon',
            business=self.business,
            price=33.00,
            max_allowed_quantity=5,
            price_type=PriceType.FIXED,
            services=[service],
            tax_rate=25.00,
        )

        url = f"/business_api/me/businesses/{self.business.id}/pos/apply_tax_rate/services/"
        res = self.fetch(url, body={}, method='POST')

        assert res.code == status.HTTP_200_OK
        assert ServiceAddOn.objects.get(id=addon.id).tax_rate == Decimal(20)
