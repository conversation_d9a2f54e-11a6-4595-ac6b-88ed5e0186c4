from abc import abstractmethod

from lib.abc import ThreadSafeSingletonMeta
from webapps.user.models import User
from webapps.user.v2.domain.enums import UserProfileType


class AbstractUserRepository(metaclass=ThreadSafeSingletonMeta):

    @abstractmethod
    def exists(self, email: str) -> bool:
        raise NotImplementedError

    @abstractmethod
    def get_profile_type(self, email: str) -> list[UserProfileType] | list:
        raise NotImplementedError


class UserRepository(AbstractUserRepository):

    def exists(self, email: str) -> bool:
        return User.objects.filter(email=email).exists()

    def get_profile_type(self, email: str) -> list[UserProfileType] | list:
        return list(
            User.objects.filter(email=email)
            .filter(profiles__profile_type__isnull=False)
            .values_list('profiles__profile_type', flat=True)
            .distinct()
        )
