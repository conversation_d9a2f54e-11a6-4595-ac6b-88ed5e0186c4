import pytest

from lib.feature_flag.feature.business import AccountExistsAddProfileTypeFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.user.v2.application.serializers.user_exists import (
    UserExistsBusinessQuerySerializer,
    UserExistsBusinessResponseSerializer,
)
from webapps.user.v2.domain.enums import UserProfileType


class TestUserExistsBusinessQuerySerializer:
    def test_validate_email_valid(self):
        serializer = UserExistsBusinessQuerySerializer(data={'email': '<EMAIL> '})

        assert serializer.is_valid() is True
        assert serializer.validated_data['email'] == '<EMAIL>'

    def test_validate_email_cleaned(self):
        serializer = UserExistsBusinessQuerySerializer(data={'email': ' <EMAIL> '})

        assert serializer.is_valid() is True
        assert serializer.validated_data['email'] == '<EMAIL>'

    def test_validate_email_missing(self):
        serializer = UserExistsBusinessQuerySerializer(data={})

        assert serializer.is_valid() is False
        assert serializer.errors['email']
        assert serializer.errors['email'][0].code == 'required'
        assert str(serializer.errors['email'][0]) == 'This field is required.'

    @pytest.mark.parametrize(
        'email', ['user', 'user@', 'user@test', 'user\ntest.com', 'uś***********']
    )
    def test_validate_email_invalid(self, email: str):
        serializer = UserExistsBusinessQuerySerializer(data={'email': email})

        assert serializer.is_valid() is False
        assert serializer.errors['email']
        assert serializer.errors['email'][0].code == 'invalid'
        assert str(serializer.errors['email'][0]) == 'Enter a valid email address.'


class TestUserExistsBusinessResponseSerializer:

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_to_representation_ff_on(self):
        serializer = UserExistsBusinessResponseSerializer(
            instance={'account_exists': True, 'profile_type': UserProfileType.BUSINESS}
        )
        assert serializer.data == {
            'account_exists': True,
            'profile_type': UserProfileType.BUSINESS,
        }

        serializer = UserExistsBusinessResponseSerializer(
            {'account_exists': False, 'profile_type': None}
        )
        assert serializer.data == {
            'account_exists': False,
            'profile_type': None,
        }

    def test_to_representation_ff_off(self):
        serializer = UserExistsBusinessResponseSerializer(instance={'account_exists': True})
        assert serializer.data == {
            'account_exists': True,
        }

        serializer = UserExistsBusinessResponseSerializer({'account_exists': False})
        assert serializer.data == {
            'account_exists': False,
        }
