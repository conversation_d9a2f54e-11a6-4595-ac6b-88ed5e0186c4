from rest_framework import serializers

from lib.feature_flag.feature.business import AccountExistsAddProfileTypeFlag
from webapps.user.v2.domain.enums import UserProfileType


class UserExistsBusinessQuerySerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value: str) -> str:
        return value.strip().lower()


class UserExistsBusinessResponseSerializer(serializers.Serializer):
    def to_representation(self, instance: dict) -> dict[str, bool | UserProfileType]:
        data = {'account_exists': instance.get('account_exists')}
        if AccountExistsAddProfileTypeFlag():
            data['profile_type'] = instance.get('profile_type', [])
        return data
