import pytest
from django.urls import reverse
from mock import patch

from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.business import AccountExistsAddProfileTypeFlag
from lib.feature_flag.feature.security import (
    DisableDisposableEmailBlacklistBusinessFlag,
    HCaptchaBusinessFlag,
    HCaptchaBusinessForceFlag,
    HCaptchaBusinessFrontdeskFlag,
)
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from webapps.booking.models import BookingSources
from webapps.consts import FRONTDESK
from webapps.user.baker_recipes import user_recipe
from webapps.user.v2.domain.enums import UserProfileType


@pytest.mark.django_db
class TestUserExistsBusinessView(BaseBusinessApiTestCase):
    # pylint: disable=too-many-instance-attributes
    def setUp(self):
        self.url = reverse('account_exists_business')
        self.existing_email = '<EMAIL>'
        self.non_existing_email = '<EMAIL>'
        self.business_existing_email = '<EMAIL>'
        self.customer_existing_email = '<EMAIL>'
        self.business_customer_existing_email = '<EMAIL>'

        self.user = baker.make_recipe('webapps.user.user_recipe', email=self.existing_email)
        self.biz_user = baker.make_recipe(
            'webapps.user.business_user', email=self.business_existing_email
        )
        self.cus_user = baker.make_recipe(
            'webapps.user.customer_user', email=self.customer_existing_email
        )
        self.both_biz_cus_profile = baker.make_recipe(
            'webapps.user.user_with_profiles', email=self.business_customer_existing_email
        )
        self.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key='biz_key',
        )
        super().setUp()

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_account_exists_ff_on(self):
        response = self.client.get(self.url, data={'email': self.existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is True
        assert response.json()['profile_type'] == []

    def test_account_exists_ff_off(self):
        response = self.client.get(self.url, data={'email': self.existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is True
        with pytest.raises(KeyError):
            assert response.json()['profile_type]']

    def test_account_does_not_exists_ff_off(self):
        response = self.client.get(self.url, data={'email': self.non_existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is False
        with pytest.raises(KeyError):
            assert response.json()['profile_type']

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_account_does_not_exists_ff_on(self):
        response = self.client.get(self.url, data={'email': self.non_existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is False
        assert response.json()['profile_type'] == []

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_cus_account_ff_on(self):
        response = self.client.get(self.url, data={'email': self.customer_existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is True
        assert response.json()['profile_type'] == [UserProfileType.CUSTOMER]

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_biz_account_ff_on(self):
        response = self.client.get(self.url, data={'email': self.business_existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is True
        assert response.json()['profile_type'] == [UserProfileType.BUSINESS]

    @override_eppo_feature_flag({AccountExistsAddProfileTypeFlag.flag_name: True})
    def test_biz_cus_account_ff_on(self):
        response = self.client.get(self.url, data={'email': self.business_customer_existing_email})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is True
        assert response.json()['profile_type'] == [
            UserProfileType.BUSINESS,
            UserProfileType.CUSTOMER,
        ]

    def test_invalid_query_params(self):
        expected_error = {
            'code': 'required',
            'description': 'This field is required.',
            'field': 'email',
        }
        response = self.client.get(self.url, data={'mail': self.existing_email})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['errors'][0].items() >= expected_error.items()

    def test_missing_query_params(self):
        expected_error = {
            'code': 'required',
            'description': 'This field is required.',
            'field': 'email',
        }
        response = self.client.get(self.url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['errors'][0].items() >= expected_error.items()

    def test_email_sanitized(self):
        response = self.client.get(self.url, data={'email': ' <EMAIL>\n'})

        assert response.status_code == status.HTTP_200_OK
        assert response.json()['account_exists'] is True

    @override_eppo_feature_flag({DisableDisposableEmailBlacklistBusinessFlag.flag_name: True})
    def test_disposable_email_error(self):
        expected_error = {
            'code': 'invalid',
            'description': 'Email not accepted. Please use a different email address.',
            'field': 'email',
        }

        response = self.client.get(self.url, data={'email': '<EMAIL>'})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()['errors'][0].items() >= expected_error.items()

    @parameterized.expand(
        [
            ('<EMAIL>', False),
            ('<EMAIL>', True),
            ('<EMAIL>', False),
        ]
    )
    def test_no_disposable_email_error(self, email, flag):
        with override_eppo_feature_flag(
            {DisableDisposableEmailBlacklistBusinessFlag.flag_name: flag}
        ):
            response = self.client.get(self.url, data={'email': email})
        assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
class TestUserExistsBusinessViewHCaptcha(BaseBusinessApiTestCase):
    def setUp(self):
        self.url = reverse('account_exists_business')
        self.existing_email = '<EMAIL>'
        self.non_existing_email = '<EMAIL>'

        self.user = user_recipe.make(email=self.existing_email)
        self.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=FRONTDESK,
            api_key='biz_key',
        )

        super().setUp()

    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_inactive(self, mocked_verify):
        response = self.client.get(
            self.url,
            data={'email': '<EMAIL>'},
            HTTP_X_HCAPTCHA_TOKEN='test_token',
        )

        mocked_verify.assert_not_called()
        assert response.status_code == status.HTTP_200_OK

    @override_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch(
        'service.business.hcaptcha.HCaptchaBusinessRequestValidator.verify_hcaptcha',
        return_value=True,
    )
    def test_hcaptcha_active_request_passed(self, mocked_verify):
        response = self.client.get(
            self.url,
            data={'email': '<EMAIL>'},
            HTTP_X_HCAPTCHA_TOKEN='test_token',
        )

        mocked_verify.assert_called()
        assert response.status_code == status.HTTP_200_OK

    @override_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_active_request_blocked(self, _):
        response = self.client.get(
            self.url,
            data={'email': '<EMAIL>'},
            HTTP_X_HCAPTCHA_TOKEN='test_token',
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error = response.json()['errors'][0]
        assert 'Request blocked' in error['description']

    @override_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_active_no_token(self, mocked_verify):
        response = self.client.get(self.url, data={'email': '<EMAIL>'})

        mocked_verify.assert_not_called()
        assert response.status_code == status.HTTP_200_OK

    @override_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_force_no_token(self, _):
        response = self.client.get(self.url, data={'email': '<EMAIL>'})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error = response.json()['errors'][0]
        assert 'Request blocked' in error['description']

    @override_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=True)
    def test_hcaptcha_force_request_passed(self, mocked_verify):
        response = self.client.get(
            self.url,
            data={'email': '<EMAIL>'},
            HTTP_X_HCAPTCHA_TOKEN='test_token',
        )

        mocked_verify.assert_called()
        assert response.status_code == status.HTTP_200_OK

    @override_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_force_request_blocked(self, _):
        response = self.client.get(
            self.url,
            data={'email': '<EMAIL>'},
            HTTP_X_HCAPTCHA_TOKEN='test_token',
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error = response.json()['errors'][0]
        assert 'Request blocked' in error['description']
