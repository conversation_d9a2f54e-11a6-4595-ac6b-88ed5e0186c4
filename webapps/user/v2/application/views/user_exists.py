from bo_obs.datadog.enums import BooksyTeams
from django.utils.translation import gettext as _
from lagom import magic_bind_to_container
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.request import Request
from rest_framework.response import Response

from drf_api.base_views import BaseBooksyNoSessionGenericAPIView
from drf_api.mixins import QuerySerializerMixin
from lib.exceptions import BooksyValidationError
from lib.feature_flag.feature.business import (
    AccountExistsBusinessMigrationFlag,
    AccountExistsAddProfileTypeFlag,
)
from lib.feature_flag.feature.security import DisableDisposableEmailBlacklistBusinessFlag
from service.business.hcaptcha import hcaptcha_for_business
from webapps.user.v2.application.services.user_details import AbstractUserDetailsService
from webapps.user.v2.application.services.validators import AbstractBusinessUserEmailValidator
from webapps.user.v2.application.serializers.user_exists import (
    UserExistsBusinessResponseSerializer,
    UserExistsBusinessQuerySerializer,
)
from webapps.user.v2.containers import container


class UserExistsBusinessView(QuerySerializerMixin, BaseBooksyNoSessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    query_serializer_class = UserExistsBusinessQuerySerializer
    serializer_class = UserExistsBusinessResponseSerializer
    __tornado_migration_feature_flag__ = AccountExistsBusinessMigrationFlag

    @hcaptcha_for_business
    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_user_email_validator: AbstractBusinessUserEmailValidator,
        business_user_details: AbstractUserDetailsService,
        *args,
        **kwargs,
    ) -> Response:
        try:
            query_serializer = self.get_query_serializer(data=request.query_params)
            query_serializer.is_valid(raise_exception=True)
            email = query_serializer.validated_data['email']
        except ValidationError as exc:
            errors = [
                {'field': field, 'code': error[0].code, 'description': error[0]}
                for field, error in exc.detail.items()
            ]
            return Response(data={'errors': errors}, status=status.HTTP_400_BAD_REQUEST)

        verify_domain_disposable: bool = DisableDisposableEmailBlacklistBusinessFlag()
        data = {}
        try:
            data['account_exists'] = business_user_email_validator.validate(
                email=email, verify_domain_disposable=verify_domain_disposable
            )
            if AccountExistsAddProfileTypeFlag():
                data['profile_type'] = business_user_details.user_profile_type(email)
        except BooksyValidationError as exc:
            error_message = exc.to_dict()
            error_message['description'] = _(error_message['description'])
            return Response(data={'errors': [error_message]}, status=status.HTTP_400_BAD_REQUEST)
        response_serializer = self.get_serializer(data)
        return Response(data=response_serializer.data, status=status.HTTP_200_OK)
