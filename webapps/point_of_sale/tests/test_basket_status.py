from django.test import TestCase
from model_bakery import baker

from lib.point_of_sale.enums import (
    BasketPaymentStatus,
    BasketPaymentType,
    BasketStatus,
    PaymentMethodType,
)
from webapps.point_of_sale.models import Basket, BasketPayment


class BasketStatusTests(TestCase):
    def setUp(self):
        self.basket = baker.make(Basket, business_id=1)

    def test_empty_basket_status(self):
        """Test that a basket with no payments has PENDING status."""
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_single_successful_payment(self):
        """Test that a basket with one successful payment has SUCCESS status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_single_pending_payment(self):
        """Test that a basket with one pending payment has PENDING status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.PENDING,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_single_failed_payment(self):
        """Test that a basket with one failed payment has FAILED status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.FAILED)

    def test_single_canceled_payment(self):
        """Test that a basket with one canceled payment has CANCELED status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.CANCELED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CANCELED)

    def test_action_required_payment(self):
        """Test that a basket with action required payment has ACTION_REQUIRED status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.ACTION_REQUIRED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.ACTION_REQUIRED)

    def test_multiple_successful_payments(self):
        """Test that a basket with multiple successful payments has SUCCESS status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_mixed_payment_statuses_with_action_required(self):
        """Test that action required takes priority over other statuses."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.ACTION_REQUIRED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.ACTION_REQUIRED)

    def test_mixed_payment_statuses_success_and_pending(self):
        """Test that mixed success and pending payments result in PENDING status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.PENDING,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_mixed_payment_statuses_success_and_failed(self):
        """Test that mixed success and failed payments result in PENDING status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_all_failed_payments(self):
        """Test that all failed payments result in FAILED status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.FAILED)

    def test_all_canceled_payments(self):
        """Test that all canceled payments result in CANCELED status."""
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.CANCELED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.CANCELED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CANCELED)

    def test_pending_refund(self):
        """Test that pending refund results in SENT_FOR_REFUND status."""
        # First create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        # Then create a pending refund
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.PENDING,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.SENT_FOR_REFUND)

    def test_successful_full_refund(self):
        """Test that successful refund covering full amount results in REFUNDED status."""
        # Create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        # Create a successful refund for the full amount
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_successful_partial_refund(self):
        """Test that successful partial refund doesn't result in REFUNDED status."""
        # Create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        # Create a successful partial refund
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_successful_over_refund(self):
        """Test that successful refund exceeding payment amount results in REFUNDED status."""
        # Create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        # Create a successful refund for more than the payment amount
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=1200,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_chargeback_priority(self):
        """Test that successful chargeback has higher priority than other statuses."""
        # Create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        # Create a successful chargeback
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK)

    def test_chargeback_reversed_priority(self):
        """Test that successful chargeback reversal has correct priority."""
        # Create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        # Create a successful chargeback reversal
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK_REVERSED,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK_REVERSED)

    def test_second_chargeback_highest_priority(self):
        """Test that second chargeback has the highest priority."""
        # Create various payments
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK_REVERSED,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        # Create a successful second chargeback
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.SECOND_CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SECOND_CHARGEBACK)

    def test_failed_chargeback_no_priority(self):
        """Test that failed chargeback doesn't affect status."""
        # Create a successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        # Create a failed chargeback
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.FAILED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_no_payment_type_payments(self):
        """Test basket with only non-payment type transactions."""
        # Create only a refund (no original payment)
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_complex_scenario_with_multiple_payment_types(self):
        """Test complex scenario with multiple payment types and statuses."""
        # Create multiple payments
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        # Create a partial refund
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=300,
            payment_method=PaymentMethodType.CASH,
        )
        # Since refund doesn't cover full payment amount, should be SUCCESS
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_priority_order_verification(self):
        """Test that priority order is correctly maintained."""
        # Start with successful payment
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

        # Add chargeback - should override
        chargeback = baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK)

        # Add second chargeback - should override chargeback
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.SECOND_CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SECOND_CHARGEBACK)
