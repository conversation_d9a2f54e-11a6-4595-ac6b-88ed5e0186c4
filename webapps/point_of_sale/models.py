from django.db import models

from lib.models import (
    ArchiveManager,
    HistoryModelAbstract,
    UUIDArchiveModel,
)
from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
)
from lib.point_of_sale.entities import (
    BasketPaymentEntity,
    CancellationFeeAuthEntity,
    POSEntity,
    BasketEntity,
    BasketItemEntity,
    RelatedBasketItemEntity,
    ExtendedBasketEntity,
    ExtendedBasketItemEntity,
    BasketTipEntity,
)
from lib.point_of_sale.enums import (
    BasketItemTaxType,
    BasketItemType,
    BasketPaymentAnalyticsTrigger,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketTipSource,
    BasketTipType,
    BasketType,
    CancellationFeeAuthStatus,
    PaymentMethodType,
    RelatedBasketItemType,
    BasketStatus,
)
from webapps.point_of_sale.adapters import get_receipt_details_adapter


class Basket(UUIDArchiveModel):
    business_id = models.IntegerField(
        null=False, blank=False, db_index=True
    )  # models: Business.Business
    type = models.CharField(max_length=20, choices=BasketType.choices(), default=BasketType.PAYMENT)
    archived = models.DateTimeField(default=None, null=True)

    customer_card_id = models.IntegerField(
        null=True,
        db_index=True,
    )  # Model: Business.BusinessCustomerInfo
    metadata = models.JSONField(default=dict)

    class Meta:
        ordering = ('-created',)

    def __str__(self):
        return f"Basket {self.id}"

    @property
    def status(self) -> BasketStatus:
        """
        Calculate basket status based on the status and type of all basket payments.
        """
        payments = list(self.payments.all())

        if not payments:
            return BasketStatus.PENDING

        payment_payments = [p for p in payments if p.type == BasketPaymentType.PAYMENT]
        refund_payments = [p for p in payments if p.type == BasketPaymentType.REFUND]
        chargeback_payments = [p for p in payments if p.type == BasketPaymentType.CHARGEBACK]
        chargeback_reversed_payments = [p for p in payments if p.type == BasketPaymentType.CHARGEBACK_REVERSED]
        second_chargeback_payments = [p for p in payments if p.type == BasketPaymentType.SECOND_CHARGEBACK]

        if any(p.status == BasketPaymentStatus.SUCCESS for p in second_chargeback_payments):
            return BasketStatus.SECOND_CHARGEBACK

        if any(p.status == BasketPaymentStatus.SUCCESS for p in chargeback_reversed_payments):
            return BasketStatus.CHARGEBACK_REVERSED

        if any(p.status == BasketPaymentStatus.SUCCESS for p in chargeback_payments):
            return BasketStatus.CHARGEBACK

        successful_refunds = [p for p in refund_payments if p.status == BasketPaymentStatus.SUCCESS]
        pending_refunds = [p for p in refund_payments if p.status == BasketPaymentStatus.PENDING]

        if successful_refunds:
            return BasketStatus.REFUNDED

        if pending_refunds:
            return BasketStatus.SENT_FOR_REFUND

        payment_statuses = [p.status for p in payment_payments]

        if BasketPaymentStatus.ACTION_REQUIRED in payment_statuses:
            return BasketStatus.ACTION_REQUIRED

        if all(status == BasketPaymentStatus.SUCCESS for status in payment_statuses):
            return BasketStatus.SUCCESS

        if all(status == BasketPaymentStatus.FAILED for status in payment_statuses):
            return BasketStatus.FAILED

        if all(status == BasketPaymentStatus.CANCELED for status in payment_statuses):
            return BasketStatus.CANCELED

        # Default to pending
        return BasketStatus.PENDING

    @property
    def entity(self) -> BasketEntity:
        return BasketEntity(
            id=self.id,
            business_id=self.business_id,
            type=BasketType(self.type),
            archived=self.archived,
            customer_card_id=self.customer_card_id,
            metadata=self.metadata,
            created=self.created,
        )

    @property
    def extended_entity(self) -> ExtendedBasketEntity:
        basket_payments = self.payments.all()
        total = sum(basket_payment.amount for basket_payment in basket_payments)
        already_paid = sum(
            basket_payment.amount
            for basket_payment in basket_payments
            if basket_payment.status == BasketPaymentStatus.SUCCESS
        )
        remaining = total - already_paid
        last_basket_payment = basket_payments.last()
        last_payment_type = last_basket_payment.payment_method if last_basket_payment else None

        receipt_details = get_receipt_details_adapter(basket_id=self.id)

        basket_payment_entities = [payment.entity for payment in basket_payments]

        return ExtendedBasketEntity(
            id=self.id,
            business_id=self.business_id,
            type=BasketType(self.type),
            archived=self.archived,
            customer_card_id=self.customer_card_id,
            metadata=self.metadata,
            created=self.created,
            public_id=receipt_details.id if receipt_details else None,
            basket_number=receipt_details.receipt_number if receipt_details else None,
            status=self.status,
            total=total,
            already_paid=already_paid,
            remaining=remaining,
            payment_type=last_payment_type,
            payments=basket_payment_entities,
            items=[item.extended_entity for item in self.items.all()],
            tips=[tip.entity for tip in self.tips.all()],
        )


class BasketPayment(UUIDArchiveModel):
    basket = models.ForeignKey(
        Basket,
        on_delete=models.CASCADE,
        related_name='payments',
    )
    amount = models.IntegerField()
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethodType.choices(),
        null=False,
        blank=False,
    )
    payment_provider_code = models.CharField(
        max_length=20,
        choices=PaymentProviderCode.choices(),
        null=True,
        blank=True,
    )
    balance_transaction_id = models.UUIDField(
        null=True,
        blank=True,
        unique=True,
        db_index=True,
    )

    status = models.CharField(
        max_length=20,
        choices=BasketPaymentStatus.choices(),
        null=False,
        blank=False,
        default=BasketPaymentStatus.PENDING,
    )
    user_id = models.IntegerField(
        null=True,
        db_index=True,
    )  # Model: User.User
    operator_id = models.IntegerField(
        null=True, db_index=True
    )  # Model: Business.Resource [Staffer]

    type = models.CharField(
        max_length=20,
        choices=BasketPaymentType.choices(),
        default=BasketPaymentType.PAYMENT,
    )
    parent_basket_payment = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )

    error_code = models.CharField(
        max_length=100,
        choices=PaymentError.choices(),
        default=None,
        null=True,
        blank=True,
    )
    action_required_details = models.JSONField(null=True)
    metadata = models.JSONField(default=dict)
    auto_capture = models.BooleanField(default=False)

    source = models.CharField(
        max_length=100,
        choices=BasketPaymentSource.choices(),
        default=BasketPaymentSource.PAYMENT,
    )

    class Meta:
        ordering = ('-created',)

    def __str__(self):
        return f"BasketPayment {self.id} {self.type} {self.status}"

    @property
    def entity(self) -> BasketPaymentEntity:
        return BasketPaymentEntity(
            id=self.id,
            basket_id=self.basket_id,
            amount=self.amount,
            payment_method=PaymentMethodType(self.payment_method),
            payment_provider_code=(
                PaymentProviderCode(self.payment_provider_code)
                if self.payment_provider_code
                else None
            ),
            balance_transaction_id=self.balance_transaction_id,
            status=BasketPaymentStatus(self.status),
            user_id=self.user_id,
            type=BasketPaymentType(self.type),
            parent_basket_payment_id=self.parent_basket_payment_id,
            error_code=PaymentError(self.error_code) if self.error_code else None,
            action_required_details=self.action_required_details,
            metadata=self.metadata,
            auto_capture=self.auto_capture,
            source=BasketPaymentSource(self.source),
            created=self.created,
            operator_id=self.operator_id,
        )


class BasketItem(UUIDArchiveModel):
    basket = models.ForeignKey(
        Basket,
        on_delete=models.CASCADE,
        related_name='items',
    )
    type = models.CharField(
        max_length=20,
        choices=BasketItemType.choices(),
        null=False,
        blank=False,
        default=BasketItemType.SERVICE,
    )
    order = models.PositiveSmallIntegerField(default=0)
    name_line_1 = models.CharField(max_length=255, blank=True)
    name_line_2 = models.CharField(max_length=255, blank=True)
    quantity = models.PositiveSmallIntegerField(default=1)
    item_price = models.IntegerField(null=True, blank=True)

    # Discounts
    discount_rate = models.PositiveSmallIntegerField(
        default=0, help_text='Row discount rate (only for this row)'
    )
    discounted_item_price = models.IntegerField(
        null=True,
        blank=True,
        help_text='Item price after row discount, but before global discount',
    )

    # Taxes
    tax_amount = models.IntegerField(null=True, blank=True)
    tax_rate = models.IntegerField(null=True, blank=True)
    tax_type = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        choices=BasketItemTaxType.choices(),
        default=None,
    )

    # Totals
    total = models.IntegerField(
        null=True,
        blank=True,
        help_text='Total before global discount but with excluded tax added '
        '(as displayed to the User)',
    )
    net_total = models.IntegerField(
        null=True,
        blank=True,
        help_text='Total after global discount, but without all taxes ' '(used in sales report)',
    )
    gross_total = models.IntegerField(
        null=True,
        blank=True,
        help_text='Total after global discount, but with all taxes ' '(used in sales report)',
    )
    discounted_total = models.IntegerField(
        null=True,
        blank=True,
        help_text='Total after global discount, but without excluded tax ' '(used in commissions)',
    )
    net_total_wo_discount = models.IntegerField(
        null=True,
        blank=True,
        help_text='What would a net total be if there were no discounts ' '(used in sales report)',
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ('order', '-created')

    def __str__(self):
        return f"BasketItem {self.id}"

    @property
    def entity(self) -> BasketItemEntity:
        return BasketItemEntity(
            id=self.id,
            basket_id=self.basket.id,
            item_price=self.item_price,
            discount_rate=self.discount_rate,
            gross_total=self.gross_total,
            type=self.type,
        )

    @property
    def extended_entity(self) -> ExtendedBasketItemEntity:
        return ExtendedBasketItemEntity(
            id=self.id,
            basket_id=self.basket.id,
            item_price=self.item_price,
            discount_rate=self.discount_rate,
            gross_total=self.gross_total,
            type=self.type,
            name_line_1=self.name_line_1,
            name_line_2=self.name_line_2,
            total=self.total,
            quantity=self.quantity,
            tax_amount=self.tax_amount,
            discounted_total=self.discounted_total,
        )


class BasketTip(UUIDArchiveModel):
    basket = models.ForeignKey(
        Basket,
        on_delete=models.CASCADE,
        related_name='tips',
    )
    rate = models.IntegerField(null=True, blank=True)
    type = models.CharField(
        max_length=10, choices=BasketTipType.choices(), default=BasketTipType.PERCENT
    )
    amount = models.IntegerField(null=True, blank=True)
    staffer_id = models.IntegerField(null=True, db_index=True)  # Model: Business.Resource
    source = models.CharField(
        choices=BasketTipSource.choices(), null=True, blank=True, max_length=30
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def __str__(self):
        return f'BasketTip {self.id}'

    @property
    def entity(self) -> BasketTipEntity:
        return BasketTipEntity(
            id=self.id,
            amount=self.amount,
        )


class CancellationFeeAuth(UUIDArchiveModel):
    basket = models.ForeignKey(
        Basket,
        on_delete=models.CASCADE,
        related_name='auths',
        null=True,
    )
    amount = models.IntegerField(null=True, blank=True)
    business_id = models.IntegerField(
        null=True, blank=True, db_index=True
    )  # Model Business.business
    appointment_id = models.IntegerField(
        null=False, blank=False, db_index=True
    )  # Model: Booking.Appointment
    status = models.CharField(
        max_length=20,
        choices=CancellationFeeAuthStatus.choices(),
        default=CancellationFeeAuthStatus.PENDING,
    )
    balance_transaction_id = models.UUIDField(
        null=True,
        db_index=True,
    )
    payment_provider_code = models.CharField(
        max_length=20,
        choices=PaymentProviderCode.choices(),
        default=PaymentProviderCode.ADYEN,
    )
    metadata = models.JSONField(default=dict)

    class Meta:
        ordering = ('-created',)

    def __str__(self):
        return f'CancellationFeeAuthorization {self.id} | {self.appointment_id}'

    @property
    def entity(self) -> CancellationFeeAuthEntity:
        return CancellationFeeAuthEntity(
            id=self.id,
            basket_id=self.basket_id,
            business_id=self.business_id,
            appointment_id=self.appointment_id,
            amount=self.amount,
            status=CancellationFeeAuthStatus(self.status),
            balance_transaction_id=self.balance_transaction_id,
            payment_provider_code=PaymentProviderCode(self.payment_provider_code),
            metadata=self.metadata,
        )


class RelatedBasketItem(UUIDArchiveModel):
    type = models.CharField(
        max_length=20,
        choices=RelatedBasketItemType.choices(),
        null=False,
        blank=False,
    )
    basket_item = models.ForeignKey(
        BasketItem, on_delete=models.CASCADE, related_name='related_items'
    )
    external_id = models.IntegerField(
        null=False,
        blank=False,
    )

    class Meta:
        ordering = ('-created',)

    @property
    def entity(self) -> RelatedBasketItemEntity:
        return RelatedBasketItemEntity(
            id=self.id,
            type=RelatedBasketItemType(self.type),
            basket_item_id=self.basket_item.id,
            external_id=self.external_id,
        )


class BasketHistory(HistoryModelAbstract):
    basket = models.ForeignKey(
        Basket,
        on_delete=models.CASCADE,
        related_name='history',
    )

    class Meta:
        ordering = ('-created',)

    @property
    def previous(self):
        return (
            BasketHistory.objects.filter(
                basket_id=self.basket_id,
                created__lt=self.created,
            )
            .order_by('-created')
            .first()
        )


class POS(UUIDArchiveModel):
    business_id = models.IntegerField(unique=True, null=True)
    default = models.BooleanField(default=False)  # only 1 default pos is allowed
    statement_name = models.CharField(max_length=255)
    custom_pos_plans = models.ManyToManyField("POSPlan", related_name="poses")

    class Meta:
        verbose_name_plural = 'POSes'
        constraints = [
            models.UniqueConstraint(
                fields=['default'], name='only_one_default_pos', condition=models.Q(default=True)
            )
        ]
        ordering = ('-created',)

    def __str__(self):
        return f"POS {self.id}"

    @property
    def entity(self) -> POSEntity:
        return POSEntity(
            id=self.id,
            business_id=self.business_id,
            default=self.default,
            statement_name=self.statement_name,
        )


class PaymentMethodVariant(UUIDArchiveModel):
    """
    Combines two info - payment method and provider code (optional in case of the offline payments)
    """

    pos = models.ForeignKey(
        "POS",
        on_delete=models.CASCADE,
        related_name='payment_method_variants',
    )
    payment_method_type = models.CharField(
        max_length=20,
        choices=PaymentMethodType.choices(),
        null=False,
        blank=False,
    )
    payment_provider_code = models.CharField(
        max_length=20,
        choices=PaymentProviderCode.choices(),
        null=True,
        blank=True,
    )
    default = models.BooleanField(default=False)
    available = models.BooleanField(
        default=False
    )  # if it is possible for pmv to be enabled by the user
    enabled = models.BooleanField(default=False)  # if user checked the checkbox

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['pos', 'payment_method_type', 'payment_provider_code'],
                name='unique_payment_method_variant',
            ),
            models.UniqueConstraint(
                fields=['default', 'pos'],
                name='only_one_default_pmv_per_pos',
                condition=models.Q(default=True),
            ),
        ]
        ordering = ('-created',)

    def __str__(self):
        return f"PaymentMethodVariant {self.id}"


class POSPlan(UUIDArchiveModel):
    provider_code = models.CharField(
        max_length=20,
        choices=PaymentProviderCode.choices(),
        null=False,
        blank=False,
    )
    payment_method_type = models.CharField(
        max_length=20,
        choices=PaymentMethodType.choices(),
        null=False,
        blank=False,
    )
    default = models.BooleanField(default=False)
    payment_provision_percentage = models.DecimalField(  # 13.69 = 13.69%
        max_digits=10,
        decimal_places=2,
    )
    payment_provision_fee = models.IntegerField()  # 86 = 0.86 usd

    refund_provision_percentage = models.DecimalField(  # 13.69 = 13.69%
        max_digits=10,
        decimal_places=2,
    )
    refund_provision_fee = models.IntegerField()  # 86 = 0.86 usd

    dispute_provision_percentage = models.DecimalField(  # 13.69 = 13.69%
        max_digits=10,
        decimal_places=2,
    )
    dispute_provision_fee = models.IntegerField()  # 86 = 0.86 usd

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['provider_code', 'payment_method_type'],
                condition=models.Q(default=True),
                name="pos_plan_unique_default",
            ),
        ]
        ordering = ('-created',)

    def __str__(self):
        return f"POSPlan {self.id}"

    @property
    def payment_splits(self) -> PaymentSplitsEntity:
        return PaymentSplitsEntity(
            percentage_fee=self.payment_provision_percentage,
            fixed_fee=self.payment_provision_fee,
        )

    @property
    def refund_splits(self) -> RefundSplitsEntity:
        return RefundSplitsEntity(
            percentage_fee=self.refund_provision_percentage,
            fixed_fee=self.refund_provision_fee,
        )

    @property
    def dispute_splits(self) -> DisputeSplitsEntity:
        return DisputeSplitsEntity(
            percentage_fee=self.dispute_provision_percentage,
            fixed_fee=self.dispute_provision_fee,
        )


class CancellationFeeAuthAnalytics(UUIDArchiveModel):
    cf_auth = models.ForeignKey(
        CancellationFeeAuth,
        on_delete=models.CASCADE,
        related_name='analytics',
    )
    device_data = models.JSONField(default=dict)

    def __str__(self):
        return f'CancellationFeeAuthAnalytics {self.id} [cf_auth_id={self.cf_auth_id}]'


class BasketPaymentAnalytics(UUIDArchiveModel):
    basket_payment = models.ForeignKey(
        BasketPayment,
        on_delete=models.CASCADE,
        related_name='analytics',
    )
    device_data = models.JSONField(default=dict)
    trigger = models.CharField(max_length=255, choices=BasketPaymentAnalyticsTrigger.choices())
    payment_link = models.BooleanField(default=False)

    def __str__(self):
        return f'BasketPaymentAnalytics {self.id} [basket_payment_id={self.basket_payment_id}]'
