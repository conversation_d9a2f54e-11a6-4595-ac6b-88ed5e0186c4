import abc

from webapps.google_business_profile.domain.dtos import VerificationOptionsDTO
from webapps.google_business_profile.shared import LocationId, LanguageCode


class GoogleLocationVerificationGateway(abc.ABC):
    @abc.abstractmethod
    def fetch_verification_options(self, language_code: LanguageCode) -> VerificationOptionsDTO: ...

    @abc.abstractmethod
    def verify_location(self, language_code: LanguageCode, location_id: LocationId) -> None: ...

    @abc.abstractmethod
    def complete_location_verification(self) -> None: ...
