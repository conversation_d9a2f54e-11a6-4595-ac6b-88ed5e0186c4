import abc

from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.shared import LanguageCode


class GoogleLocationVerificationAbstractClient(abc.ABC):
    @abc.abstractmethod
    def fetch_verification_options(self, language_code: LanguageCode) -> ApiResponse: ...

    @abc.abstractmethod
    def verify_location(self) -> ApiResponse: ...

    @abc.abstractmethod
    def complete_location_verification(self) -> ApiResponse: ...
