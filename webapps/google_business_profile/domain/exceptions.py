class GoogleApiError(Exception):
    default_message = "Google API error occurred"

    def __init__(self):
        super().__init__(self.default_message)


class AuthenticationErrorGoogle(GoogleApiError):
    default_message = "Authentication failed"


class RateLimitExceededErrorGoogle(GoogleApiError):
    default_message = "Rate limit exceeded for Google Business Profile API"


class PermissionDeniedErrorGoogle(GoogleApiError):
    default_message = "Permission denied for this operation"


class ResourceNotFoundErrorGoogle(GoogleApiError):
    default_message = "The requested resource was not found"


class ValidationErrorGoogle(GoogleApiError):
    default_message = "Data validation error"

    def __init__(self, errors=None):
        self.errors = errors or {}
        super().__init__()
        if self.errors:
            if isinstance(self.errors, dict):
                self.args = (f"Validation failed: {', '.join(self.errors.keys())}",)
            elif isinstance(self.errors, list):
                self.args = (f"Validation failed: {', '.join(str(e) for e in self.errors)}",)
            else:
                self.args = (f"Validation failed: {self.errors}",)


class BusinessProfileSaveError(Exception):
    def __init__(self, message="Failed to save business profile"):
        super().__init__(message)


class BusinessSaveError(Exception):
    def __init__(self, message="Failed to save business profile"):
        super().__init__(message)


class AddressValidationValueError(ValueError):
    pass


class GoogleAccountValidationError(Exception):
    def __init__(self, message="GoogleAccount validation failed"):
        super().__init__(message)


class BusinessValidationError(Exception):
    def __init__(self, message="Business validation failed"):
        super().__init__(message)


class LocationValidationError(Exception):
    def __init__(self, message="Google Location validation failed"):
        super().__init__(message)


class RegionCodeValidationError(ValueError):
    def __init__(self, value, message=None):
        self.value = value
        message = message or f"Invalid region code format: {value}"
        super().__init__(message)


class LanguageCodeValidationError(ValueError):
    def __init__(self, value, message=None):
        self.value = value
        message = message or f"Invalid language code format: {value}"
        super().__init__(message)


class PostalCodeValidationError(ValueError):
    def __init__(self, value, message=None):
        self.value = value
        message = message or f"Invalid postal code format: {value}"
        super().__init__(message)


class EmailValidationError(ValueError):
    def __init__(self, value, message=None):
        self.value = value
        message = message or f"Invalid email address format: {value}"
        super().__init__(message)


class VerificationDataValueError(ValueError):
    pass


class AddressDataValueError(ValueError):
    def __init__(self, message="Address verification data value error"):
        super().__init__(message)


class EmailDataValueError(ValueError):
    def __init__(self, message="Email verification data value error"):
        super().__init__(message)


class PhoneNumberValueError(ValueError):
    def __init__(self, message="Phone verification data value error"):
        super().__init__(message)


class VerificationMethodValueError(ValueError):
    def __init__(self, message="Method verification data value error"):
        super().__init__(message)


class AnnouncementValueError(ValueError):
    def __init__(self, message="Method verification data value error"):
        super().__init__(message)
