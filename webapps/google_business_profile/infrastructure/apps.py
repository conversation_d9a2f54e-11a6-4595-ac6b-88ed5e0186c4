# pylint: disable=line-too-long

from django.apps import AppConfig

from webapps.google_business_profile.application.services.location_verification_service import (
    LocationVerificationService,
)
from webapps.google_business_profile.infrastructure.factories.location_verification_gateway_factory import (
    LocationVerificationGatewayFactory,
)


class GoogleBusinessProfileConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'webapps.google_business_profile.infrastructure'
    label = 'google_business_profile'

    def ready(self):
        from webapps.google_business_profile.application.services.accounts_service import (
            AccountsService,
        )
        from webapps.google_business_profile.application.services.category_service import (
            CategoriesService,
        )
        from webapps.google_business_profile.application.services.locations_service import (
            LocationsService,
        )
        from webapps.google_business_profile.application.services.locations_status_service import (
            LocationStatusService,
        )
        from webapps.google_business_profile.infrastructure.analytics.segment_service import (
            GBPSegmentAnalyticsService,
        )
        from webapps.google_business_profile.application.services.bp_service import (
            BooksyProfileSaveService,
            BooksyProfileGetService,
        )
        from webapps.google_business_profile.application.services.check_address_service import (
            AddressCheckService,
        )

        from webapps.google_business_profile.infrastructure.factories.gbp_gateway_factory import (
            GoogleBusinessProfileGatewayFactory,
        )
        from webapps.google_business_profile.infrastructure.gateways.business_gateway import (
            BusinessDetailsGateway,
        )
        from webapps.google_business_profile.infrastructure.factories.gbp_gateway_factory import (
            GoogleAddressValidationGatewayFactory,
        )

        from webapps.google_business_profile.infrastructure.firestore.client import (
            FirestoreClient,
            BusinessFirestoreRepository,
            BusinessProfileFirestoreRepository,
        )
        from webapps.google_business_profile.application.services.bp_service import (
            RepositoriesUnitOfWork,
        )
        from webapps.google_business_profile.domain.interfaces.language_detector import (
            LanguageDetector,
        )
        from webapps.google_business_profile.infrastructure.libs.django_language_detector import (
            DjangoLanguageDetector,
        )
        from webapps.google_business_profile.domain.interfaces.comparator import AddressComparator
        from webapps.google_business_profile.infrastructure.libs.fuzz_comparator import (
            FuzzComparator,
        )
        from v2.domain.interfaces.settings import SettingsProvider
        from v2.infrastructure.settings import SettingsProviderDjango

        from webapps.google_business_profile.containers import container

        # Converters
        container[LanguageDetector] = DjangoLanguageDetector()
        container[AddressComparator] = FuzzComparator()
        container[GBPSegmentAnalyticsService] = GBPSegmentAnalyticsService

        # Factories and clients
        container[GoogleBusinessProfileGatewayFactory] = GoogleBusinessProfileGatewayFactory
        container[GoogleAddressValidationGatewayFactory] = (
            GoogleAddressValidationGatewayFactory.create_gateway()
        )
        container[LocationVerificationGatewayFactory] = LocationVerificationGatewayFactory
        container[FirestoreClient] = FirestoreClient()
        container[BusinessDetailsGateway] = BusinessDetailsGateway()
        container[SettingsProvider] = SettingsProviderDjango()

        # Repositories
        container[BusinessFirestoreRepository] = lambda c: BusinessFirestoreRepository(
            c[FirestoreClient]
        )
        container[BusinessProfileFirestoreRepository] = (
            lambda c: BusinessProfileFirestoreRepository(c[FirestoreClient])
        )

        # Unit of work
        container[RepositoriesUnitOfWork] = lambda c: RepositoriesUnitOfWork(
            client=c[FirestoreClient],
            business_repository=c[BusinessFirestoreRepository],
            business_profile_repository=c[BusinessProfileFirestoreRepository],
        )

        # Services
        container[AccountsService] = lambda c: AccountsService(gateway=None)

        container[CategoriesService] = lambda c: CategoriesService(gateway=None)

        container[LocationsService] = lambda c: LocationsService(
            google_gateway=None,
            business_gateway=c[BusinessDetailsGateway],
            unit_of_work=c[RepositoriesUnitOfWork],
            language_detector=c[LanguageDetector],
            settings_provider=c[SettingsProvider],
            analytic_service=c[GBPSegmentAnalyticsService],
            address_comparator=c[AddressComparator],
        )

        container[LocationStatusService] = lambda c: LocationStatusService(
            gateway=None,
            analytic_service=c[GBPSegmentAnalyticsService],
        )

        container[BooksyProfileSaveService] = lambda c: BooksyProfileSaveService(
            business_gateway=c[BusinessDetailsGateway],
            unit_of_work=c[RepositoriesUnitOfWork],
            settings_provider=c[SettingsProvider],
        )

        container[AddressCheckService] = lambda c: AddressCheckService(
            business_gateway=c[BusinessDetailsGateway],
            google_gateway=c[GoogleAddressValidationGatewayFactory],
        )

        container[BooksyProfileGetService] = lambda c: BooksyProfileGetService(
            unit_of_work=c[RepositoriesUnitOfWork],
        )
        container[LocationVerificationService] = lambda c: LocationVerificationService(
            gateway=None,
        )
