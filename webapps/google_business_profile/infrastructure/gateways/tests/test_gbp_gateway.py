# pylint: disable=line-too-long, redefined-outer-name
from http import HTTPStatus
from unittest.mock import Mock, patch

import pytest

from webapps.google_business_profile.domain.models import (
    GoogleAccount,
    GoogleBusinessProfile,
)
from webapps.google_business_profile.domain.exceptions import (
    GoogleApiError,
    AuthenticationErrorGoogle,
)
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
    CategoryQueryParams,
    UpdateLocationParams,
    CreateLocationParams,
)
from webapps.google_business_profile.domain.value_objects import (
    LocationCategory,
    LocationCategories,
    LocationAddress,
    LocationPhoneNumbers,
    GoogleLocationResource,
    GoogleLocationDetails,
    GoogleLocationLinkage,
    LocationStatus,
    OpenInfo,
    GoogleAccountResource,
)
from webapps.google_business_profile.domain.const import OpenInfoStatus
from webapps.google_business_profile.infrastructure.gateways.gbp_gateway import (
    GoogleBusinessProfileApiGateway,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.client import (
    GoogleBusinessProfileClient,
)
from webapps.google_business_profile.shared import (
    LocationId,
    AccountId,
    PostalCode,
    WebsiteUri,
    BusinessId,
    PhoneNumber,
)
from webapps.google_business_profile.domain.value_types import (
    RegionCode,
    LanguageCode,
    CountryCode,
)


@pytest.fixture
def mock_api_client():
    return Mock(spec=GoogleBusinessProfileClient)


@pytest.fixture
def sample_location():
    resource = GoogleLocationResource(id=LocationId(67890), name="locations/67890")

    details = GoogleLocationDetails(
        title="Test Location",
        website_uri=WebsiteUri("https://example.com"),
        open_info=OpenInfo(status=OpenInfoStatus.OPEN),
    )

    linkage = GoogleLocationLinkage(
        account_id=AccountId(12345), business_id=BusinessId(54321), country_code=CountryCode("US")
    )

    phone_numbers = LocationPhoneNumbers(
        primary_phone=PhoneNumber("+***********"), additional_phones=[]
    )

    categories = LocationCategories(
        primary_category=LocationCategory(
            name="gcid:beauty_salon", display_name="Beauty Salon", category_id="beauty_salon"
        )
    )

    storefront_address = LocationAddress(
        region_code=RegionCode("US"),
        language_code=LanguageCode("en-US"),
        postal_code=PostalCode("12345"),
        administrative_area="CA",
        locality="San Francisco",
        address_lines=["123 Main St"],
    )

    return GoogleBusinessProfile(
        resource=resource,
        details=details,
        linkage=linkage,
        phone_numbers=phone_numbers,
        categories=categories,
        storefront_address=storefront_address,
    )


@pytest.fixture
def sample_account():
    resource = GoogleAccountResource(id=AccountId(12345), name="accounts/12345")

    return GoogleAccount(resource=resource, account_name="Test Account")


@pytest.fixture
def sample_location_status():
    return LocationStatus(
        has_business_authority=True,
        has_voice_of_merchant=True,
        has_pending_verification=False,
        verifications=[],
    )


class TestGoogleBusinessProfileApiGateway:
    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.AccountMapper')
    def test_get_accounts_success(self, mock_account_mapper, mock_api_client, sample_account):
        account_mapper_instance = mock_account_mapper.return_value
        account_mapper_instance.to_domain_entity.return_value = sample_account

        mock_api_client.get_gbp_accounts.return_value = ApiResponse(
            status_code=HTTPStatus.OK,
            data={'accounts': [{'name': 'accounts/12345', 'accountName': 'Test Account'}]},
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        result = gateway.get_accounts()

        mock_api_client.get_gbp_accounts.assert_called_once()
        account_mapper_instance.to_domain_entity.assert_called_with(
            {'name': 'accounts/12345', 'accountName': 'Test Account'}
        )
        assert len(result) == 1
        assert result[0] == sample_account

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_get_accounts_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = AuthenticationErrorGoogle()

        error_response = ApiResponse(
            status_code=HTTPStatus.UNAUTHORIZED, data={'error': {'message': 'Invalid credentials'}}
        )
        mock_api_client.get_gbp_accounts.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        with pytest.raises(AuthenticationErrorGoogle):
            gateway.get_accounts()

        mock_api_client.get_gbp_accounts.assert_called_once()
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.LocationMapper')
    def test_get_locations_success(self, mock_location_mapper, mock_api_client, sample_location):
        location_mapper_instance = mock_location_mapper.return_value
        location_mapper_instance.to_domain_entity.return_value = sample_location

        mock_api_client.get_gbp_locations.return_value = ApiResponse(
            status_code=HTTPStatus.OK,
            data={'locations': [{'name': 'locations/67890', 'title': 'Test Location'}]},
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        result = gateway.get_locations()

        mock_api_client.get_gbp_locations.assert_called_once()
        location_mapper_instance.to_domain_entity.assert_called_with(
            {'name': 'locations/67890', 'title': 'Test Location'}
        )
        assert len(result) == 1
        assert result[0] == sample_location

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_get_locations_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST, data={'error': {'message': 'Invalid request'}}
        )
        mock_api_client.get_gbp_locations.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        with pytest.raises(GoogleApiError):
            gateway.get_locations()

        mock_api_client.get_gbp_locations.assert_called_once()
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.LocationMapper')
    def test_get_location_success(self, mock_location_mapper, mock_api_client, sample_location):
        location_mapper_instance = mock_location_mapper.return_value
        location_mapper_instance.to_domain_entity.return_value = sample_location

        mock_api_client.get_particular_location.return_value = ApiResponse(
            status_code=HTTPStatus.OK, data={'name': 'locations/67890', 'title': 'Test Location'}
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        result = gateway.get_location()

        mock_api_client.get_particular_location.assert_called_once()
        location_mapper_instance.to_domain_entity.assert_called_once_with(
            {'name': 'locations/67890', 'title': 'Test Location'}
        )
        assert result == sample_location

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_get_location_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.NOT_FOUND, data={'error': {'message': 'Location not found'}}
        )
        mock_api_client.get_particular_location.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        with pytest.raises(GoogleApiError):
            gateway.get_location()

        mock_api_client.get_particular_location.assert_called_once()
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.LocationMapper')
    def test_create_location_success(self, mock_location_mapper, mock_api_client, sample_location):
        location_mapper_instance = mock_location_mapper.return_value
        location_mapper_instance.to_domain_entity.return_value = sample_location

        mock_api_client.create_gbp_location.return_value = ApiResponse(
            status_code=HTTPStatus.OK, data={'name': 'locations/67890', 'title': 'New Location'}
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        location_data = Mock(spec=CreateLocationParams)

        result = gateway.create_location(location_data)

        mock_api_client.create_gbp_location.assert_called_once_with(location_data)
        location_mapper_instance.to_domain_entity.assert_called_once_with(
            {'name': 'locations/67890', 'title': 'New Location'}
        )
        assert result == sample_location

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_create_location_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST, data={'error': {'message': 'Invalid location data'}}
        )
        mock_api_client.create_gbp_location.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        location_data = Mock(spec=CreateLocationParams)

        with pytest.raises(GoogleApiError):
            gateway.create_location(location_data)

        mock_api_client.create_gbp_location.assert_called_once_with(location_data)
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.LocationMapper')
    def test_update_location_success(self, mock_location_mapper, mock_api_client, sample_location):
        location_mapper_instance = mock_location_mapper.return_value
        location_mapper_instance.to_domain_entity.return_value = sample_location

        mock_api_client.update_gbp_location.return_value = ApiResponse(
            status_code=HTTPStatus.OK, data={'name': 'locations/67890', 'title': 'Updated Location'}
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        location_data = Mock(spec=UpdateLocationParams)

        result = gateway.update_location(location_data)

        mock_api_client.update_gbp_location.assert_called_once_with(location_data)
        location_mapper_instance.to_domain_entity.assert_called_once_with(
            {'name': 'locations/67890', 'title': 'Updated Location'}
        )
        assert result == sample_location

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_update_location_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST, data={'error': {'message': 'Invalid location data'}}
        )
        mock_api_client.update_gbp_location.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        location_data = Mock(spec=UpdateLocationParams)

        with pytest.raises(GoogleApiError):
            gateway.update_location(location_data)

        mock_api_client.update_gbp_location.assert_called_once_with(location_data)
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch(
        'webapps.google_business_profile.infrastructure.gateways.gbp_gateway.LocationStatusMapper'
    )
    def test_get_location_status_success(
        self, mock_location_status_mapper, mock_api_client, sample_location_status
    ):
        location_status_mapper_instance = mock_location_status_mapper.return_value
        location_status_mapper_instance.to_domain_entity.return_value = sample_location_status

        status_data = {
            'hasBusinessAuthority': True,
            'hasVoiceOfMerchant': True,
            'verify': {'hasPendingVerification': False},
        }
        verifications_data = {'verifications': []}
        mock_api_client.get_gbp_location_status.return_value = ApiResponse(
            status_code=HTTPStatus.OK, data=status_data
        )
        mock_api_client.get_gbp_location_verifications.return_value = ApiResponse(
            status_code=HTTPStatus.OK, data=verifications_data
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        result = gateway.get_location_status()

        mock_api_client.get_gbp_location_status.assert_called_once()
        mock_api_client.get_gbp_location_verifications.assert_called_once()
        location_status_mapper_instance.to_domain_entity.assert_called_once_with(
            status_data, verifications_data
        )
        assert result == sample_location_status

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_get_location_status_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.NOT_FOUND, data={'error': {'message': 'Location not found'}}
        )
        mock_api_client.get_gbp_location_status.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        with pytest.raises(GoogleApiError):
            gateway.get_location_status()

        mock_api_client.get_gbp_location_status.assert_called_once()
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.CategoryMapper')
    def test_get_categories_success(self, mock_category_mapper, mock_api_client):
        category_mapper_instance = mock_category_mapper.return_value
        category_mapper_instance.to_domain_entity.side_effect = [
            LocationCategory(name="gcid:beauty_salon", display_name="Beauty Salon"),
            LocationCategory(name="gcid:hair_salon", display_name="Hair Salon"),
        ]

        mock_api_client.get_categories.return_value = ApiResponse(
            status_code=HTTPStatus.OK,
            data={
                'categories': [
                    {'name': 'gcid:beauty_salon', 'displayName': 'Beauty Salon'},
                    {'name': 'gcid:hair_salon', 'displayName': 'Hair Salon'},
                ]
            },
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        category_data = Mock(spec=CategoryQueryParams)

        result = gateway.get_categories(category_data)

        mock_api_client.get_categories.assert_called_once_with(category_data)
        assert category_mapper_instance.to_domain_entity.call_count == 2
        assert len(result) == 2
        assert result[0].name == "gcid:beauty_salon"
        assert result[1].name == "gcid:hair_salon"

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_get_categories_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST, data={'error': {'message': 'Invalid request'}}
        )
        mock_api_client.get_categories.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        category_data = Mock(spec=CategoryQueryParams)

        with pytest.raises(GoogleApiError):
            gateway.get_categories(category_data)

        mock_api_client.get_categories.assert_called_once_with(category_data)
        error_handler_instance.handle_error.assert_called_once_with(error_response)

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_delete_location_success(self, mock_error_handler, mock_api_client):
        mock_api_client.delete_gbp_location.return_value = ApiResponse(
            status_code=HTTPStatus.OK, data={}
        )

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)
        gateway.delete_location()

        mock_api_client.delete_gbp_location.assert_called_once()
        mock_error_handler.return_value.handle_error.assert_not_called()

    @patch('webapps.google_business_profile.infrastructure.gateways.gbp_gateway.ApiErrorHandler')
    def test_delete_location_error(self, mock_error_handler, mock_api_client):
        error_handler_instance = mock_error_handler.return_value
        error_handler_instance.handle_error.side_effect = GoogleApiError()

        error_response = ApiResponse(
            status_code=HTTPStatus.BAD_REQUEST,
            data={'error': {'message': 'Failed to delete location'}},
        )
        mock_api_client.delete_gbp_location.return_value = error_response

        gateway = GoogleBusinessProfileApiGateway(api_client=mock_api_client)

        with pytest.raises(GoogleApiError):
            gateway.delete_location()

        mock_api_client.delete_gbp_location.assert_called_once()
        error_handler_instance.handle_error.assert_called_once_with(error_response)
