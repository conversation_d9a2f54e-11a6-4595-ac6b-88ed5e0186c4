# pylint: disable=line-too-long
import unittest
from unittest.mock import patch, MagicMock

from webapps.google_business_profile.domain.models import Business
from webapps.google_business_profile.domain.value_objects import (
    BusinessCategory,
    LocationCategory,
    BusinessIdentity,
    BusinessSupplementaryDetails,
    BusinessGbpDetails,
    BusinessPrimaryContact,
    BooksyCategory,
)
from webapps.google_business_profile.domain.value_types import (
    CountryCode,
    Email,
    RegionCode,
    LanguageCode,
)
from webapps.google_business_profile.infrastructure.gateways.business_gateway import (
    BusinessDetailsGateway,
)
from webapps.google_business_profile.shared import (
    BusinessId,
    UserId,
    PhoneNumber,
    WebsiteUri,
    BusinessStatus,
    PostalCode,
)


class TestBusinessDetailsGateway(unittest.TestCase):
    def setUp(self):
        self.gateway = BusinessDetailsGateway()

        self.business_id = BusinessId(12345)

        self.mock_rwg_dto = MagicMock()
        self.mock_rwg_dto.active = True
        self.mock_rwg_dto.owner_id = 1001
        self.mock_rwg_dto.visible_from = "2023-01-01"
        self.mock_rwg_dto.name = "Test Business"
        self.mock_rwg_dto.official_name = "Test Business Official"
        self.mock_rwg_dto.region_id = 42
        self.mock_rwg_dto.address = "123 Test St"
        self.mock_rwg_dto.address2 = "Suite 100"
        self.mock_rwg_dto.city = "New York City"
        self.mock_rwg_dto.zipcode = "12345"
        self.mock_rwg_dto.longitude = 40.7128
        self.mock_rwg_dto.latitude = -74.0060
        self.mock_rwg_dto.region_state_code = "NY"
        self.mock_rwg_dto.city_or_region_city = "New York City"
        self.mock_rwg_dto.primary_category_id = 1
        self.mock_rwg_dto.primary_category_name = "Hair Salon"
        self.mock_rwg_dto.primary_category_slug = "hair-salon"
        self.mock_rwg_dto.primary_category_report_name = "Hair salons"
        self.mock_rwg_dto.renting_venue = False
        self.mock_rwg_dto.booking_mode = "appointment"
        self.mock_rwg_dto.phone = "************"
        self.mock_rwg_dto.website = "https://example.com"
        self.mock_rwg_dto.description = "Test business description"
        self.mock_rwg_dto.country_code = "us"
        self.mock_rwg_dto.public_email = "<EMAIL>"
        self.mock_rwg_dto.created = None
        self.mock_rwg_dto.updated = None
        self.mock_rwg_dto.active_from = None
        self.mock_rwg_dto.active_till = None
        self.mock_rwg_dto.disable_google_reserve = False
        self.mock_rwg_dto.verification = "verified"
        self.mock_rwg_dto.status = "active"
        self.mock_rwg_dto.visible = True
        self.mock_rwg_dto.subdomain = "test-business"

    @patch(
        'webapps.google_business_profile.infrastructure.gateways.business_gateway.business_rwg_service'
    )
    def test_get_business_details_success(self, mock_rwg_service):
        mock_rwg_service.get_business_rwg_data.return_value = self.mock_rwg_dto

        result = self.gateway.get_business_details(self.business_id)

        self.assertIsInstance(result, Business)
        self.assertEqual(result.id, self.business_id)
        self.assertEqual(result.name, "Test Business")
        self.assertEqual(result.country_code, CountryCode("US"))
        self.assertEqual(result.owner_id, UserId(1001))

        self.assertIsInstance(result.identity, BusinessIdentity)
        self.assertEqual(result.identity.name, "Test Business")
        self.assertEqual(result.identity.official_name, "Test Business Official")

        self.assertIsInstance(result.supplementary_details, BusinessSupplementaryDetails)
        self.assertEqual(result.supplementary_details.description, "Test business description")
        self.assertEqual(result.supplementary_details.renting_venue, False)
        self.assertEqual(result.supplementary_details.subdomain, "test-business")

        self.assertIsInstance(result.gbp_details, BusinessGbpDetails)
        self.assertEqual(result.gbp_details.disable_google_reserve, False)
        self.assertEqual(result.gbp_details.verification, "verified")

        self.assertIsInstance(result.primary_contact, BusinessPrimaryContact)
        self.assertEqual(result.primary_contact.phone, PhoneNumber("************"))
        self.assertEqual(result.primary_contact.website, WebsiteUri("https://example.com"))
        self.assertEqual(result.primary_contact.email, Email("<EMAIL>"))

        self.assertEqual(result.address.region_code, RegionCode("US"))
        self.assertEqual(result.address.language_code, LanguageCode("en-US"))
        self.assertEqual(result.address.postal_code, PostalCode("12345"))
        self.assertEqual(result.address.administrative_area, "NY")
        self.assertEqual(result.address.locality, "New York City")
        self.assertEqual(result.address.address_lines, ["123 Test St"])

        self.assertEqual(result.category.name, "hair-salon")
        self.assertEqual(result.category.display_name, "Hair Salon")

        self.assertIsInstance(result.booksy_category, BooksyCategory)
        self.assertEqual(result.booksy_category.id, 1)
        self.assertEqual(result.booksy_category.name, "Hair Salon")
        self.assertEqual(result.booksy_category.slug, "hair-salon")

        self.assertEqual(result.status, BusinessStatus("active"))
        self.assertEqual(result.active, True)
        self.assertEqual(result.visible, True)

        mock_rwg_service.get_business_rwg_data.assert_called_once_with(self.business_id)

    @patch(
        'webapps.google_business_profile.infrastructure.gateways.business_gateway.business_rwg_service'
    )
    def test_get_business_details_not_found(self, mock_rwg_service):
        mock_rwg_service.get_business_rwg_data.return_value = None

        result = self.gateway.get_business_details(self.business_id)

        self.assertIsNone(result)
        mock_rwg_service.get_business_rwg_data.assert_called_once_with(self.business_id)

    @patch(
        'webapps.google_business_profile.infrastructure.gateways.business_gateway.business_rwg_service'
    )
    def test_get_business_details_error(self, mock_rwg_service):
        mock_rwg_service.get_business_rwg_data.side_effect = ValueError("Test error")

        result = self.gateway.get_business_details(self.business_id)

        self.assertIsNone(result)
        mock_rwg_service.get_business_rwg_data.assert_called_once_with(self.business_id)

    @patch(
        'webapps.google_business_profile.infrastructure.gateways.business_gateway.business_rwg_service'
    )
    def test_get_business_primary_category_success(self, mock_rwg_service):

        mock_category_data = MagicMock()
        mock_category_data.primary_category_id = 1
        mock_category_data.primary_category_name = "Hair Salon"
        mock_category_data.primary_category_slug = "hair-salon"
        mock_category_data.primary_category_report_name = "Hair salons"

        mock_rwg_service.get_business_category_rwg_data.return_value = mock_category_data
        result = self.gateway.get_business_primary_category(self.business_id)

        self.assertIsInstance(result, BusinessCategory)
        self.assertEqual(result.category_id, 1)
        self.assertEqual(result.name, "Hair Salon")
        self.assertEqual(result.slug, "hair-salon")
        self.assertEqual(result.report_name, "Hair salons")

        mock_rwg_service.get_business_category_rwg_data.assert_called_once_with(self.business_id)

    @patch(
        'webapps.google_business_profile.infrastructure.gateways.business_gateway.business_rwg_service'
    )
    def test_get_business_primary_category_error(self, mock_rwg_service):
        mock_rwg_service.get_business_category_rwg_data.side_effect = ValueError("Test error")

        result = self.gateway.get_business_primary_category(self.business_id)

        self.assertIsNone(result)
        mock_rwg_service.get_business_category_rwg_data.assert_called_once_with(self.business_id)

    @patch('webapps.google_business_profile.infrastructure.gateways.business_gateway.logger')
    def test_map_to_google_category(self, mock_logger):
        category = BusinessCategory(
            category_id=1,
            name="Hair Salon",
            slug="hair-salon",
            report_name="Hair salons",
        )

        result = self.gateway.map_to_google_category(category)
        location_category = LocationCategory.from_name(
            name="categories/gcid:hair_salon", display_name="Hair salon"
        )

        self.assertEqual(result, location_category)

        mock_logger.info.assert_called_once()
