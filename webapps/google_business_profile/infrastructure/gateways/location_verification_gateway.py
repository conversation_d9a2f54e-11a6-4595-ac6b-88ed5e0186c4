# pylint: disable=line-too-long
from http import HTTPStatus

from settings import logging
from webapps.google_business_profile.domain.dtos import VerificationOptionsDTO

from webapps.google_business_profile.domain.interfaces.location_verification_gateway import (
    GoogleLocationVerificationGateway,
)
from webapps.google_business_profile.infrastructure.converters.location_verification_options_converter import (
    LocationVerificationOptionsMapper,
)
from webapps.google_business_profile.infrastructure.gateways.api_error_handler import (
    ApiErrorHandler,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.location_verification_client import (
    GoogleLocationVerificationClient,
)
from webapps.google_business_profile.shared import LocationId, LanguageCode

logger = logging.getLogger('booksy.google_business_profile')


class GoogleLocationVerificationApiGateway(GoogleLocationVerificationGateway):
    """Gateway implementation for Location Verification API"""

    def __init__(self, api_client: GoogleLocationVerificationClient):
        self._error_handler = ApiErrorHandler()
        self._api_client = api_client
        self._location_verification_options_mapper = LocationVerificationOptionsMapper()

    def fetch_verification_options(self, language_code: LanguageCode) -> VerificationOptionsDTO:
        verification_options_response = self._api_client.fetch_verification_options(language_code)
        if verification_options_response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(verification_options_response)

        return self._location_verification_options_mapper.to_domain_dto(
            verification_options_response.data
        )

    def verify_location(self, language_code: LanguageCode, location_id: LocationId) -> None:
        pass

    def complete_location_verification(self) -> None:
        pass
