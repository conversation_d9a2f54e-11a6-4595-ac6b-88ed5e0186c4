from http import HTTPStatus

from webapps.google_business_profile.domain.interfaces.address_validation_gateway import (
    GoogleAddressValidationGateway,
)
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    AddressCheckParams,
)
from webapps.google_business_profile.domain.models import GoogleMapsAddress
from webapps.google_business_profile.infrastructure.converters.address_validator_converter import (
    AddressValidatorConverter,
)
from webapps.google_business_profile.infrastructure.gateways.api_error_handler import (
    ApiErrorHandler,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.address_validation_client import (  # pylint: disable=line-too-long
    GoogleAddressValidationClient,
)


class GoogleAddressValidationApiGateway(GoogleAddressValidationGateway):
    def __init__(self, api_client: GoogleAddressValidationClient):
        self._api_client = api_client
        self._error_handler = ApiErrorHandler()
        self._mapper = AddressValidatorConverter()

    def check_address(self, address: AddressCheckParams) -> GoogleMapsAddress:
        response = self._api_client.check_address(address)

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return self._mapper.to_domain_aggregate(response.data)
