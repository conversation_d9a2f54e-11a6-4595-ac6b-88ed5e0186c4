import logging
from http import HTTPStatus

from webapps.google_business_profile.domain.models import (
    GoogleAccount,
    GoogleBusinessProfile,
)
from webapps.google_business_profile.domain.interfaces.gbp_gateway import (
    GoogleBusinessProfileGateway,
)
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    CategoryQueryParams,
    UpdateLocationParams,
    CreateLocationParams,
)
from webapps.google_business_profile.domain.value_objects import (
    LocationCategory,
    LocationStatus,
)
from webapps.google_business_profile.infrastructure.converters.account_converter import (
    AccountMapper,
)
from webapps.google_business_profile.infrastructure.converters.category_converter import (
    CategoryMapper,
)
from webapps.google_business_profile.infrastructure.converters.location_converter import (
    LocationMapper,
)
from webapps.google_business_profile.infrastructure.converters.location_status_converter import (
    LocationStatusMapper,
)
from webapps.google_business_profile.infrastructure.gateways.api_error_handler import (
    ApiErrorHandler,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.client import (
    GoogleBusinessProfileClient,
)

logger = logging.getLogger('booksy.google_business_profile')


class GoogleBusinessProfileApiGateway(GoogleBusinessProfileGateway):
    """Gateway implementation for Google Business Profile API"""

    def __init__(self, api_client: GoogleBusinessProfileClient):
        self._api_client = api_client
        self._error_handler = ApiErrorHandler()
        self._account_mapper = AccountMapper()
        self._location_mapper = LocationMapper()
        self._location_status_mapper = LocationStatusMapper()
        self._category_mapper = CategoryMapper()

    def get_accounts(self) -> list[GoogleAccount]:
        response = self._api_client.get_gbp_accounts()

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return [
            self._account_mapper.to_domain_entity(account_data)
            for account_data in response.data.get('accounts', [])
        ]

    def get_locations(self) -> list[GoogleBusinessProfile]:
        response = self._api_client.get_gbp_locations()

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return [
            self._location_mapper.to_domain_entity(location_data)
            for location_data in response.data.get('locations', [])
        ]

    def get_location(self) -> GoogleBusinessProfile | None:
        response = self._api_client.get_particular_location()

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return self._location_mapper.to_domain_entity(response.data)

    def create_location(self, location_data: CreateLocationParams) -> GoogleBusinessProfile:
        response = self._api_client.create_gbp_location(location_data)

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return self._location_mapper.to_domain_entity(response.data)

    def update_location(self, location_data: UpdateLocationParams) -> GoogleBusinessProfile:
        response = self._api_client.update_gbp_location(location_data)

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return self._location_mapper.to_domain_entity(response.data)

    def get_location_status(self) -> LocationStatus:
        location_status_response = self._api_client.get_gbp_location_status()
        if location_status_response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(location_status_response)

        location_verifications_response = self._api_client.get_gbp_location_verifications()
        if location_verifications_response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(location_verifications_response)

        return self._location_status_mapper.to_domain_entity(
            location_status_response.data, location_verifications_response.data
        )

    def get_categories(self, category_data: CategoryQueryParams) -> list[LocationCategory]:
        response = self._api_client.get_categories(category_data)

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)

        return [
            self._category_mapper.to_domain_entity(category_data)
            for category_data in response.data.get('categories', [])
        ]

    def delete_location(self) -> None:
        response = self._api_client.delete_gbp_location()

        if response.status_code != HTTPStatus.OK:
            self._error_handler.handle_error(response)
