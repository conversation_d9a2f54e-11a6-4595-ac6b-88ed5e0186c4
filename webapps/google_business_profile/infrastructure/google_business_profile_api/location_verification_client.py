# pylint: disable=line-too-long
from typing import Any

from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.domain.interfaces.location_verification_client import (
    GoogleLocationVerificationAbstractClient,
)

from webapps.google_business_profile.infrastructure.dtos.location_verification_client_dto import (
    LocationVerificationClientDTO,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.http.http_client import (
    HttpClient,
)
from webapps.google_business_profile.shared import LanguageCode

LOCATION_VERIFICATION_API = 'https://mybusinessverifications.googleapis.com/v1'


class GoogleLocationVerificationClient(GoogleLocationVerificationAbstractClient):
    def __init__(self, client_data: LocationVerificationClientDTO, http_client: HttpClient = None):
        self._oauth_token = client_data.oauth_token
        self.location_id = client_data.location_id
        self.verification_id = client_data.verification_id
        self._http_client = http_client or HttpClient()

    def _get_auth_headers(self) -> dict[str, str]:
        return {
            "Authorization": f"Bearer {self._oauth_token}",
            "Content-Type": "application/json",
        }

    @staticmethod
    def _build_request_data(language_code: LanguageCode) -> dict[str, Any]:
        return {'languageCode': str(language_code)}

    def fetch_verification_options(self, language_code: LanguageCode) -> ApiResponse:
        url = f'{LOCATION_VERIFICATION_API}/locations/{self.location_id}:fetchVerificationOptions'
        request_data = self._build_request_data(language_code)
        response = self._http_client.post(url, headers=self._get_auth_headers(), data=request_data)
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def verify_location(self) -> ApiResponse:
        pass

    def complete_location_verification(self) -> ApiResponse:
        pass
