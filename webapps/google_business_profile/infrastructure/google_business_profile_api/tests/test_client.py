# pylint: disable=line-too-long, redefined-outer-name
from unittest.mock import Mock, patch

import pytest

from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    CategoryQueryParams,
    UpdateLocationParams,
    CreateLocationParams,
)
from webapps.google_business_profile.domain.value_objects import (
    LocationAddress,
    LocationCategories,
    LocationCategory,
    LocationPhoneNumbers,
)
from webapps.google_business_profile.infrastructure.dtos.gbp_dto import GBPClientData
from webapps.google_business_profile.infrastructure.google_business_profile_api.client import (
    GoogleBusinessProfileClient,
    LOCATION_FIELDS,
    UPDATE_LOCATION_FIELDS,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.http.http_client import (
    HttpClient,
)
from webapps.google_business_profile.shared import (
    RegionCode,
    LanguageCode,
    PostalCode,
    OAuthToken,
    AccountId,
    LocationId,
    WebsiteUri,
)


@pytest.fixture
def mock_http_client():
    return Mock(spec=HttpClient)


@pytest.fixture
def client_data():
    return GBPClientData(
        oauth_token=OAuthToken("test_token"),
        account_id=AccountId(12345),
        location_id=LocationId(67890),
    )


@pytest.fixture
def client(client_data, mock_http_client):
    return GoogleBusinessProfileClient(client_data=client_data, http_client=mock_http_client)


@pytest.fixture
def location_address():
    return LocationAddress(
        region_code=RegionCode("US"),
        language_code=LanguageCode("en-US"),
        postal_code=PostalCode("12345"),
        administrative_area="CA",
        locality="San Francisco",
        address_lines=["123 Main St"],
    )


@pytest.fixture
def location_category():
    return LocationCategory(name="gcid:beauty_salon", display_name="Beauty Salon")


@pytest.fixture
def location_categories(location_category):
    return LocationCategories(primary_category=location_category)


@pytest.fixture
def location_phone_numbers():
    return LocationPhoneNumbers(primary_phone="+***********", additional_phones=["+***********"])


@pytest.fixture
def create_location_params(location_address, location_categories, location_phone_numbers):
    return CreateLocationParams(
        title="Test Business",
        storefront_address=location_address,
        categories=location_categories,
        phone_numbers=location_phone_numbers,
        language_code=LanguageCode("en-US"),
        website_uri=WebsiteUri("https://example.com"),
        validate_only=False,
    )


@pytest.fixture
def update_location_params(location_address, location_categories):
    return UpdateLocationParams(
        title="Updated Business",
        categories=location_categories,
        storefront_address=location_address,
        validate_only=False,
    )


@pytest.fixture
def category_query_params():
    return CategoryQueryParams(
        region_code=RegionCode("US"),
        language_code=LanguageCode("en-US"),
        filter="salon",
        page_size=20,
        view=1,
    )


@patch.object(GoogleBusinessProfileClient, '_get_auth_headers')
class TestGoogleBusinessProfileClient:
    def test_get_gbp_accounts(self, mock_get_auth_headers, client, mock_http_client):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.get.return_value = {
            'status_code': 200,
            'data': {'accounts': [{'name': 'accounts/12345', 'displayName': 'Test Account'}]},
        }

        response = client.get_gbp_accounts()

        mock_http_client.get.assert_called_once_with(
            'https://mybusinessbusinessinformation.googleapis.com/v1/accounts',
            headers=mock_get_auth_headers.return_value,
        )

        assert response.status_code == 200
        assert response.data == {
            'accounts': [{'name': 'accounts/12345', 'displayName': 'Test Account'}]
        }

    @pytest.mark.random_failure
    def test_get_gbp_locations(self, mock_get_auth_headers, client, mock_http_client):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.get.return_value = {
            'status_code': 200,
            'data': {'locations': [{'name': 'locations/67890', 'title': 'Test Location'}]},
        }

        response = client.get_gbp_locations()

        mock_http_client.get.assert_called_once_with(
            f'https://mybusinessbusinessinformation.googleapis.com/v1/accounts/12345/locations?readMask={LOCATION_FIELDS}',
            headers=mock_get_auth_headers.return_value,
        )

        assert response.status_code == 200
        assert response.data == {
            'locations': [{'name': 'locations/67890', 'title': 'Test Location'}]
        }

    @patch.object(GoogleBusinessProfileClient, '_build_location_create_request')
    def test_create_gbp_location(
        self,
        mock_build_request,
        mock_get_auth_headers,
        client,
        mock_http_client,
        create_location_params,
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        expected_request_data = {"title": "Test Business"}
        mock_build_request.return_value = expected_request_data
        mock_http_client.post.return_value = {
            'status_code': 200,
            'data': {'name': 'locations/67890', 'title': 'Test Business'},
        }

        response = client.create_gbp_location(create_location_params)

        mock_build_request.assert_called_once_with(create_location_params)
        mock_http_client.post.assert_called_once_with(
            'https://mybusiness.googleapis.com/v1/accounts/12345/locations/',
            headers=mock_get_auth_headers.return_value,
            data=expected_request_data,
        )

        assert response.status_code == 200
        assert response.data == {'name': 'locations/67890', 'title': 'Test Business'}

    def test_get_particular_location(self, mock_get_auth_headers, client, mock_http_client):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.get.return_value = {
            'status_code': 200,
            'data': {'name': 'locations/67890', 'title': 'Test Location'},
        }

        response = client.get_particular_location()

        mock_http_client.get.assert_called_once_with(
            f'https://mybusinessbusinessinformation.googleapis.com/v1/locations/67890?readMask={LOCATION_FIELDS}',
            headers=mock_get_auth_headers.return_value,
        )

        assert response.status_code == 200
        assert response.data == {'name': 'locations/67890', 'title': 'Test Location'}

    @patch.object(GoogleBusinessProfileClient, '_build_location_update_request')
    def test_update_gbp_location(
        self,
        mock_build_request,
        mock_get_auth_headers,
        client,
        mock_http_client,
        update_location_params,
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        expected_request_data = {"title": "Updated Business"}
        mock_build_request.return_value = expected_request_data
        mock_http_client.patch.return_value = {
            'status_code': 200,
            'data': {'name': 'locations/67890', 'title': 'Updated Business'},
        }

        response = client.update_gbp_location(update_location_params)

        mock_build_request.assert_called_once_with(update_location_params)
        mock_http_client.patch.assert_called_once_with(
            f'https://mybusinessbusinessinformation.googleapis.com/v1/locations/67890?validateOnly=False&updateMask={UPDATE_LOCATION_FIELDS}',
            headers=mock_get_auth_headers.return_value,
            data=expected_request_data,
        )

        assert response.status_code == 200
        assert response.data == {'name': 'locations/67890', 'title': 'Updated Business'}

    @patch.object(GoogleBusinessProfileClient, '_build_location_update_request')
    def test_update_gbp_location_validate_only(
        self,
        mock_build_request,
        mock_get_auth_headers,
        client,
        mock_http_client,
        update_location_params,
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        update_location_params_validate = UpdateLocationParams(
            title=update_location_params.title,
            categories=update_location_params.categories,
            storefront_address=update_location_params.storefront_address,
            validate_only=True,
        )
        expected_request_data = {"title": "Updated Business"}
        mock_build_request.return_value = expected_request_data
        mock_http_client.patch.return_value = {
            'status_code': 200,
            'data': {'name': 'locations/67890', 'title': 'Updated Business'},
        }

        response = client.update_gbp_location(update_location_params_validate)

        mock_build_request.assert_called_once_with(update_location_params_validate)
        mock_http_client.patch.assert_called_once_with(
            f'https://mybusinessbusinessinformation.googleapis.com/v1/locations/67890?validateOnly=True&updateMask={UPDATE_LOCATION_FIELDS}',
            headers=mock_get_auth_headers.return_value,
            data=expected_request_data,
        )

        assert response.status_code == 200
        assert response.data == {'name': 'locations/67890', 'title': 'Updated Business'}

    def test_get_gbp_location_status(self, mock_get_auth_headers, client, mock_http_client):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.get.return_value = {
            'status_code': 200,
            'data': {'hasBusinessAuthority': True, 'hasVoiceOfMerchant': True},
        }

        response = client.get_gbp_location_status()

        mock_http_client.get.assert_called_once_with(
            'https://mybusinessbusinessinformation.googleapis.com/v1/locations/67890/VoiceOfMerchantState',
            headers=mock_get_auth_headers.return_value,
        )

        assert response.status_code == 200
        assert response.data == {'hasBusinessAuthority': True, 'hasVoiceOfMerchant': True}

    def test_get_categories(
        self, mock_get_auth_headers, client, mock_http_client, category_query_params
    ):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.get.return_value = {
            'status_code': 200,
            'data': {'categories': [{'name': 'gcid:beauty_salon', 'displayName': 'Beauty Salon'}]},
        }

        response = client.get_categories(category_query_params)

        mock_http_client.get.assert_called_once_with(
            'https://mybusiness.googleapis.com/v1/categories?regionCode=US&languageCode=en-US&pageSize=20&view=1&filter=displayName=salon',
            headers=mock_get_auth_headers.return_value,
        )

        assert response.status_code == 200
        assert response.data == {
            'categories': [{'name': 'gcid:beauty_salon', 'displayName': 'Beauty Salon'}]
        }

    def test_delete_gbp_location(self, mock_get_auth_headers, client, mock_http_client):
        mock_get_auth_headers.return_value = {
            "Authorization": "Bearer test_token",
            "Content-Type": "application/json",
        }
        mock_http_client.delete.return_value = {
            'status_code': 200,
            'data': {},
        }

        response = client.delete_gbp_location()

        mock_http_client.delete.assert_called_once_with(
            'https://mybusinessbusinessinformation.googleapis.com/v1/locations/67890',
            headers=mock_get_auth_headers.return_value,
        )

        assert response.status_code == 200
        assert response.data == {}

    def test_build_location_update_request(  # pylint: disable=protected-access
        self, mock_get_auth_headers, client, update_location_params
    ):
        with (
            patch.object(GoogleBusinessProfileClient, '_build_categories') as mock_build_categories,
            patch.object(
                GoogleBusinessProfileClient, '_build_storefront_address'
            ) as mock_build_storefront_address,
        ):
            mock_build_categories.return_value = {
                'primaryCategory': {
                    'name': 'gcid:beauty_salon',
                    'displayName': 'Beauty Salon',
                }
            }
            mock_build_storefront_address.return_value = {
                'regionCode': 'US',
                'languageCode': 'en-US',
                'postalCode': '12345',
                'administrativeArea': 'CA',
                'locality': 'San Francisco',
                'addressLines': ['123 Main St'],
            }

            request_data = client._build_location_update_request(update_location_params)

            mock_build_categories.assert_called_once_with(update_location_params.categories)
            if update_location_params.storefront_address:
                mock_build_storefront_address.assert_called_once_with(
                    update_location_params.storefront_address
                )

            expected_data = {
                'title': 'Updated Business',
                'categories': mock_build_categories.return_value,
                'storefrontAddress': mock_build_storefront_address.return_value,
            }

            assert request_data == expected_data

    def test_build_location_create_request(  # pylint: disable=protected-access
        self, mock_get_auth_headers, client, create_location_params
    ):
        with (
            patch.object(GoogleBusinessProfileClient, '_build_categories') as mock_build_categories,
            patch.object(
                GoogleBusinessProfileClient, '_build_storefront_address'
            ) as mock_build_storefront_address,
        ):
            mock_build_categories.return_value = {
                'primaryCategory': {
                    'name': 'gcid:beauty_salon',
                    'displayName': 'Beauty Salon',
                }
            }
            mock_build_storefront_address.return_value = {
                'regionCode': 'US',
                'languageCode': 'en-US',
                'postalCode': '12345',
                'administrativeArea': 'CA',
                'locality': 'San Francisco',
                'addressLines': ['123 Main St'],
            }

            request_data = client._build_location_create_request(create_location_params)

            mock_build_categories.assert_called_once_with(create_location_params.categories)
            mock_build_storefront_address.assert_called_once_with(
                create_location_params.storefront_address
            )

            expected_data = {
                'title': 'Test Business',
                'languageCode': 'en-US',
                'storefrontAddress': mock_build_storefront_address.return_value,
                'categories': mock_build_categories.return_value,
                'phoneNumbers': {
                    'primaryPhone': '+***********',
                    'additionalPhones': ['+***********'],
                },
                'websiteUri': 'https://example.com',
            }

            assert request_data == expected_data

    def test_build_location_create_request_minimal(  # pylint: disable=protected-access
        self,
        mock_get_auth_headers,
        client,
        location_address,
        location_categories,
        location_phone_numbers,
    ):
        minimal_params = CreateLocationParams(
            title="Test Business",
            storefront_address=location_address,
            categories=LocationCategories(
                primary_category=LocationCategory(
                    name="gcid:beauty_salon",
                )
            ),
            phone_numbers=LocationPhoneNumbers(
                primary_phone="+***********",
            ),
            language_code=LanguageCode("en-US"),
            validate_only=False,
        )

        with (
            patch.object(GoogleBusinessProfileClient, '_build_categories') as mock_build_categories,
            patch.object(
                GoogleBusinessProfileClient, '_build_storefront_address'
            ) as mock_build_storefront_address,
        ):
            mock_build_categories.return_value = {
                'primaryCategory': {
                    'name': 'gcid:beauty_salon',
                }
            }
            mock_build_storefront_address.return_value = {
                'regionCode': 'US',
                'languageCode': 'en-US',
                'postalCode': '12345',
                'administrativeArea': 'CA',
                'locality': 'San Francisco',
                'addressLines': ['123 Main St'],
            }

            request_data = client._build_location_create_request(minimal_params)

            mock_build_categories.assert_called_once_with(minimal_params.categories)
            mock_build_storefront_address.assert_called_once_with(minimal_params.storefront_address)

            expected_data = {
                'title': 'Test Business',
                'languageCode': 'en-US',
                'storefrontAddress': mock_build_storefront_address.return_value,
                'categories': mock_build_categories.return_value,
                'phoneNumbers': {
                    'primaryPhone': '+***********',
                },
            }

            assert request_data == expected_data

    def test_build_storefront_address(
        self, mock_get_auth_headers, location_address
    ):  # pylint: disable=protected-access
        address_dict = GoogleBusinessProfileClient._build_storefront_address(location_address)
        expected_data = {
            'regionCode': 'US',
            'languageCode': 'en-US',
            'postalCode': '12345',
            'administrativeArea': 'CA',
            'locality': 'San Francisco',
            'addressLines': ['123 Main St'],
        }

        assert address_dict == expected_data

    def test_build_categories_with_display_name(
        self, mock_get_auth_headers, location_categories
    ):  # pylint: disable=protected-access
        categories_dict = GoogleBusinessProfileClient._build_categories(location_categories)
        expected_data = {
            'primaryCategory': {
                'name': 'gcid:beauty_salon',
                'displayName': 'Beauty Salon',
            }
        }

        assert categories_dict == expected_data

    def test_build_categories_without_display_name(
        self, mock_get_auth_headers
    ):  # pylint: disable=protected-access
        category = LocationCategory(name="gcid:beauty_salon")
        categories = LocationCategories(primary_category=category)

        categories_dict = GoogleBusinessProfileClient._build_categories(categories)
        expected_data = {
            'primaryCategory': {
                'name': 'gcid:beauty_salon',
            }
        }

        assert categories_dict == expected_data
