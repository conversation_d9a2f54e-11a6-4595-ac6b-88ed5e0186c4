from webapps.google_business_profile.application.converters.location_verification_converter import (
    VerificationOptionsDTOConverter,
)
from webapps.google_business_profile.application.dtos.location_verification_options_dto import (
    VerificationOptionsPresentationDTO,
)
from webapps.google_business_profile.domain.interfaces.location_verification_gateway import (
    GoogleLocationVerificationGateway,
)
from webapps.google_business_profile.shared import LanguageCode


class LocationVerificationService:
    def __init__(self, gateway: GoogleLocationVerificationGateway):
        self.gateway = gateway
        self.verification_options_mapper = VerificationOptionsDTOConverter()

    def get_verification_options(
        self, language_code: LanguageCode
    ) -> VerificationOptionsPresentationDTO:
        verification_options = self.gateway.fetch_verification_options(language_code)

        presentation_data = self.verification_options_mapper.to_presentation_dto(
            verification_options
        )

        return presentation_data
