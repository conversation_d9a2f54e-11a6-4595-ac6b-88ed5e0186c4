# pylint: disable=protected-access
import unittest
from unittest.mock import Mock

from webapps.google_business_profile.application.converters.location_converter import (
    LocationDTOConverter,
)
from webapps.google_business_profile.application.dtos.location_dto import (
    LocationDTO,
    SimpleLocationDTO,
)
from webapps.google_business_profile.domain.models import GoogleBusinessProfile
from webapps.google_business_profile.domain.value_objects import (
    GoogleLocationResource,
    GoogleLocationDetails,
    GoogleLocationLinkage,
    LocationPhoneNumbers,
    LocationCategories,
    LocationAddress,
    OpenInfo,
)
from webapps.google_business_profile.shared import LocationId, WebsiteUri


class TestLocationDTOConverter(unittest.TestCase):
    def setUp(self):
        self.converter = LocationDTOConverter()

    def test_to_presentation_dto(self):
        mock_resource = Mock(spec=GoogleLocationResource)
        mock_resource.id = LocationId(987654321)
        mock_resource.name = "locations/987654321"

        mock_details = Mock(spec=GoogleLocationDetails)
        mock_details.title = "Test Business"
        mock_details.website_uri = Mock(spec=WebsiteUri)
        mock_details.open_info = Mock(spec=OpenInfo)

        mock_linkage = Mock(spec=GoogleLocationLinkage)

        mock_phone_numbers = Mock(spec=LocationPhoneNumbers)
        mock_categories = Mock(spec=LocationCategories)
        mock_address = Mock(spec=LocationAddress)

        mock_location = Mock(spec=GoogleBusinessProfile)
        mock_location._resource = mock_resource
        mock_location._details = mock_details
        mock_location._linkage = mock_linkage
        mock_location._phone_numbers = mock_phone_numbers
        mock_location._categories = mock_categories
        mock_location._storefront_address = mock_address

        mock_location.name = mock_resource.name
        mock_location.id = mock_resource.id
        mock_location.title = mock_details.title
        mock_location.website_uri = mock_details.website_uri
        mock_location.open_info = mock_details.open_info
        mock_location.phone_numbers = mock_phone_numbers
        mock_location.categories = mock_categories
        mock_location.storefront_address = mock_address

        result = self.converter.to_presentation_dto(mock_location)

        self.assertIsInstance(result, LocationDTO)
        self.assertEqual(result.name, "locations/987654321")
        self.assertEqual(result.location_id, LocationId(987654321))
        self.assertEqual(result.title, "Test Business")
        self.assertEqual(result.phone_numbers, mock_phone_numbers)
        self.assertEqual(result.categories, mock_categories)
        self.assertEqual(result.storefront_address, mock_address)
        self.assertEqual(result.website_uri, mock_details.website_uri)
        self.assertEqual(result.open_info, mock_details.open_info)

    def test_to_presentation_dto_with_formated_address(self):
        mock_resource = Mock(spec=GoogleLocationResource)
        mock_resource.id = LocationId(987654321)
        mock_resource.name = "locations/987654321"

        mock_details = Mock(spec=GoogleLocationDetails)
        mock_details.title = "Test Business"

        mock_linkage = Mock(spec=GoogleLocationLinkage)

        mock_address = Mock(spec=LocationAddress)
        mock_address.address_lines = ["123 Main St"]
        mock_address.locality = "City"
        mock_address.postal_code = "12345"
        mock_address.administrative_area = "State"

        mock_location = Mock(spec=GoogleBusinessProfile)
        mock_location._resource = mock_resource
        mock_location._details = mock_details
        mock_location._linkage = mock_linkage
        mock_location._storefront_address = mock_address

        mock_location.id = mock_resource.id
        mock_location.title = mock_details.title
        mock_location.storefront_address = mock_address

        result = self.converter.to_presentation_dto_with_formated_address(mock_location)

        self.assertIsInstance(result, SimpleLocationDTO)
        self.assertEqual(result.location_id, LocationId(987654321))
        self.assertEqual(result.title, "Test Business")
        self.assertEqual(result.address, "123 Main St, City, 12345, State")
