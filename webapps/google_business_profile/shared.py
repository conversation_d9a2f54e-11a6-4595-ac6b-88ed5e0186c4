from typing import NewType

BusinessId = NewType('BusinessId', int)
AccountId = NewType('AccountId', str)
LocationId = NewType('LocationId', str)
UserId = NewType('UserId', int)
VerificationId = NewType('VerificationId', str)

PhoneNumber = NewType('PhoneNumber', str)
WebsiteUri = NewType('WebsiteUri', str)
OAuthToken = NewType('OAuthToken', str)
CountryCode = NewType('CountryCode', str)
RegionCode = NewType('RegionCode', str)
LanguageCode = NewType('LanguageCode', str)
PostalCode = NewType('PostalCode', str)

BusinessStatus = NewType('BusinessStatus', str)
BookingMode = NewType('BookingMode', str)
