from django.urls import path

from webapps.google_business_profile.presentation.views.views import (
    AccountsView,
    LocationView,
    LocationsView,
    LocationStatusView,
    CategoriesView,
    BooksyProfileView,
    CheckAddressView,
    VerificationOptionsView,
)

urlpatterns = [
    path('gbp_accounts/', AccountsView.as_view(), name='google_business_profiles'),
    path('gbp_locations/', LocationsView.as_view(), name='google_business_locations'),
    path('gbp_location/', LocationView.as_view(), name='google_business_location'),
    path(
        'gbp_location_status/',
        LocationStatusView.as_view(),
        name='google_business_location_status',
    ),
    path('gbp_categories/', CategoriesView.as_view(), name='google_business_categories'),
    path('booksy_profile/', BooksyProfileView.as_view(), name='booksy_profile'),
    path('check_address/', CheckAddressView.as_view(), name='check_address'),
    path(
        'verification_options/',
        VerificationOptionsView.as_view(),
        name='location_verification_options',
    ),
]
