from rest_framework import serializers


class ServiceTypeSerializer(serializers.Serializer):
    service_type_id = serializers.CharField()
    display_name = serializers.CharField()


class MoreHoursTypeSerializer(serializers.Serializer):
    hours_type_id = serializers.CharField()
    display_name = serializers.CharField()
    localized_display_name = serializers.CharField()


class PrimaryCategorySerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    display_name = serializers.CharField(required=False)
    # serviceTypes = ServiceTypeSerializer(many=True, required=False)
    # moreHoursTypes = MoreHoursTypeSerializer(many=True, required=False)


class CategoriesSerializer(serializers.Serializer):
    primary_category = PrimaryCategorySerializer(required=True)


class PhoneNumbersSerializer(serializers.Serializer):
    primary_phone = serializers.CharField(required=False)


class StorefrontAddressSerializer(serializers.Serializer):
    region_code = serializers.Char<PERSON><PERSON>(required=False)
    language_code = serializers.CharField(required=False)
    postal_code = serializers.CharField(required=False)
    administrative_area = serializers.CharField(required=False)
    locality = serializers.CharField(required=False)
    address_lines = serializers.ListField(
        child=serializers.CharField(required=False), required=False
    )


class OpenInfoSerializer(serializers.Serializer):
    status = serializers.CharField(required=False)


class LocationSerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    title = serializers.CharField(required=True)
    phone_numbers = PhoneNumbersSerializer()
    categories = CategoriesSerializer()
    storefront_address = StorefrontAddressSerializer(required=False)
    website_uri = serializers.CharField(required=False)
    open_info = OpenInfoSerializer(required=False)


class SimpleLocationPresentationSerializer(serializers.Serializer):
    location_id = serializers.CharField(required=False)
    title = serializers.CharField(required=False)
    address = serializers.CharField(required=False)


class CreateLocationResponseSerializer(serializers.Serializer):
    location_id = serializers.CharField()


class LocationsSerializer(serializers.Serializer):
    locations = SimpleLocationPresentationSerializer(many=True)


class LocationPresentationSerializer(serializers.Serializer):
    title = serializers.CharField(required=False)
    category = serializers.CharField(required=False)
    address = serializers.CharField(required=False)


class LocationCompareSerializer(serializers.Serializer):
    title = serializers.BooleanField(required=False)
    category = serializers.BooleanField(required=False)
    address = serializers.CharField(required=False)


class CreateLocationSerializer(LocationSerializer):
    oauth_token = serializers.CharField(required=True)
    account_id = serializers.IntegerField(required=True)
    name = serializers.CharField(required=False)
    storefront_address = StorefrontAddressSerializer(required=True)
    language_code = serializers.CharField(required=True)
    poc = serializers.BooleanField(required=False, default=False)

    class Meta:
        exclude = ['location_id']


class SimpleCreateLocationSerializer(serializers.Serializer):
    oauth_token = serializers.CharField(required=True)
    account_id = serializers.CharField(required=True)


class ParticularLocationPhoneNumbersSerializer(serializers.Serializer):
    primary_phone = serializers.CharField(required=False)


class ParticularLocationCategoriesSerializer(serializers.Serializer):
    primary_category = PrimaryCategorySerializer(required=False)


class ParticularLocationStorefrontAddressSerializer(serializers.Serializer):
    region_code = serializers.CharField(required=False)
    language_code = serializers.CharField(required=False)
    postal_code = serializers.CharField(required=False)
    administrative_area = serializers.CharField(required=False)
    locality = serializers.CharField(required=False)
    address_lines = serializers.ListField(child=serializers.CharField(required=False))


class ParticularLocationSerializer(serializers.Serializer):
    name = serializers.CharField(required=False)
    title = serializers.CharField(required=False)
    phone_numbers = ParticularLocationPhoneNumbersSerializer(required=False)
    categories = ParticularLocationCategoriesSerializer(required=False)
    storefront_address = ParticularLocationStorefrontAddressSerializer(required=False)
    website_uri = serializers.CharField(required=False)
    open_info = OpenInfoSerializer(required=False)


class UpdateLocationSerializer(serializers.Serializer):
    oauth_token = serializers.CharField(required=True)
    location_id = serializers.IntegerField(required=True)
    validate_only = serializers.BooleanField(required=False, default=False)
    title = serializers.CharField(required=True)
    categories = ParticularLocationCategoriesSerializer(required=True)
    storefront_address = ParticularLocationStorefrontAddressSerializer(required=False)


class SimpleLocationSerializer(serializers.Serializer):
    oauth_token = serializers.CharField(required=True)
    location_id = serializers.CharField(required=True)


class LocationVerificationStatusSerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    create_time = serializers.CharField(required=True)
    state = serializers.CharField(required=True)
    method = serializers.CharField(required=False)


class LocationStatusSerializer(serializers.Serializer):
    status = serializers.CharField(required=True)


class AllLocationsPresentation(serializers.Serializer):
    location = LocationPresentationSerializer()
    business = LocationPresentationSerializer()
    compare = LocationCompareSerializer()
