# pylint: disable=line-too-long
import datetime
import uuid
from unittest.mock import patch, MagicMock

from django.test import TestCase
from model_bakery import baker

from lib.payment_gateway.enums import (
    WalletOwnerType,
    BalanceTransactionStatus,
    FeeType,
    PaymentStatus,
    BalanceTransactionType,
    DisputeType,
    PaymentMethodType,
    TransferFundOrigin,
)
from lib.payments.enums import PaymentProviderCode
from lib.payments.enums import PayoutType, PayoutStatus, PayoutError, PaymentError
from lib.point_of_sale.enums import BasketPaymentSource
from webapps.payment_gateway.messages.transaction_created_or_updated import (
    construct_transaction_created_or_updated_event_payload,
    TransactionCreatedOrUpdatedEvent,
    PaymentDetails,
    PayoutDetails,
    FeeDetails,
    TransferFundDetails,
    DisputeDetails,
    RefundDetails,
    Wallet as WalletEventData,
)
from webapps.payment_gateway.models import (
    BalanceTransaction,
    Fee,
    Refund,
    Payment,
    Payout,
    Dispute,
    Wallet,
    TransferFund,
)


class TestBalanceTransactionBooksyWalletEvent(TestCase):

    def setUp(self) -> None:
        super().setUp()
        self.biz_wallet = baker.make(Wallet, owner_type=WalletOwnerType.BUSINESS, business_id=321)
        self.cus_wallet = baker.make(Wallet, owner_type=WalletOwnerType.CUSTOMER, user_id=123)
        self.bt = baker.make(
            BalanceTransaction,
            status=BalanceTransactionStatus.SUCCESS,
            sender=self.cus_wallet,
            receiver=self.biz_wallet,
            fee_amount=123,
            amount=1230,
            payment_method=PaymentMethodType.CARD,
            parent_balance_transaction=baker.make(BalanceTransaction),
            payment_provider_code=PaymentProviderCode.STRIPE,
            transaction_type=BalanceTransactionType.PAYMENT,
        )

    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.RelatedBasketItemPort.get_appointment_id_for_basket',
        return_value=13,
    )
    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.BasketPaymentPort.get_basket_payment_entity',
        return_value=MagicMock(
            source=BasketPaymentSource.PAYMENT,
            operator_id=9,
        ),
    )
    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.TransactionPort.get_transaction',
        return_value=MagicMock(id=2137),
    )
    def test_event__common_balance_transaction_event_fields(
        self,
        get_transaction_mock,
        get_basket_payment_entity_mock,
        get_appointment_id_for_basket_mock,
    ):
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(
            event_data,
            TransactionCreatedOrUpdatedEvent(
                id=self.bt.id,
                created=self.bt.created,
                updated=self.bt.updated,
                receiver=WalletEventData(
                    statement_name=self.biz_wallet.statement_name,
                    owner_type=WalletOwnerType.BUSINESS,
                    business_id=321,
                    user_id=None,
                ),
                sender=WalletEventData(
                    statement_name=self.cus_wallet.statement_name,
                    owner_type=WalletOwnerType.CUSTOMER,
                    business_id=None,
                    user_id=123,
                ),
                amount=1230,
                fee_amount=123,
                status=BalanceTransactionStatus.SUCCESS,
                payment_method=PaymentMethodType.CARD,
                payment_provider_code=PaymentProviderCode.STRIPE,
                transaction_type=BalanceTransactionType.PAYMENT,
                parent_balance_transaction_id=self.bt.parent_balance_transaction.id,
                payment=None,
                refund=None,
                dispute=None,
                payout=None,
                fee=None,
                transfer_fund=None,
                source=BasketPaymentSource.PAYMENT,
                operator_id=9,
                transaction_id=2137,
                appointment_id=13,
            ),
        )

    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.RelatedBasketItemPort.get_appointment_id_for_basket',
        return_value=13,
    )
    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.BasketPaymentPort.get_basket_payment_entity',
        return_value=MagicMock(source=BasketPaymentSource.PAYMENT, operator_id=9, id=uuid.uuid4()),
    )
    def test_event__no_related_transaction(
        self,
        get_basket_payment_entity_mock,
        get_appointment_id_for_basket_mock,
    ):
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)
        self.assertIsNone(event_data.transaction_id)

    def test_event__payment(self):
        baker.make(
            Payment,
            balance_transaction=self.bt,
            status=PaymentStatus.CAPTURE_FAILED,
            error_code=PaymentError.CONNECTION_ERROR,
            capture_date=datetime.datetime(2025, 5, 13, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(
            event_data.payment,
            PaymentDetails(
                status=PaymentStatus.CAPTURE_FAILED,
                error_code=PaymentError.CONNECTION_ERROR,
                capture_date=datetime.datetime(2025, 5, 13, 0, 0, 0, tzinfo=datetime.timezone.utc),
            ),
        )

    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.RelatedBasketItemPort.get_appointment_id_for_basket',
        return_value=13,
    )
    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.BasketPaymentPort.get_basket_payment_entity',
        return_value=MagicMock(
            source=BasketPaymentSource.PAYMENT,
            operator_id=9,
        ),
    )
    @patch(
        'webapps.payment_gateway.messages.transaction_created_or_updated.TransactionPort.get_transaction',
        return_value=MagicMock(id=2137),
    )
    def test_event__payout(
        self,
        get_transaction_mock,
        get_basket_payment_entity_mock,
        get_appointment_id_for_basket_mock,
    ):
        baker.make(
            Payout,
            payout_type=PayoutType.FAST,
            status=PayoutStatus.IN_TRANSIT,
            error_code=PayoutError.GENERIC_ERROR,
            expected_arrival_date=datetime.datetime(
                2025, 5, 13, 0, 0, 0, tzinfo=datetime.timezone.utc
            ),
            balance_transaction=self.bt,
        )
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(
            event_data.payout,
            PayoutDetails(
                payout_type=PayoutType.FAST,
                status=PayoutStatus.IN_TRANSIT,
                expected_arrival_date=datetime.datetime(
                    2025, 5, 13, 0, 0, 0, tzinfo=datetime.timezone.utc
                ),
                error_code=PayoutError.GENERIC_ERROR,
            ),
        )

    def test_event__dispute(self):
        baker.make(
            Dispute,
            type=DisputeType.CHARGEBACK,
            balance_transaction=self.bt,
        )
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(event_data.dispute, DisputeDetails(type=DisputeType.CHARGEBACK))

    def test_event__fee(self):
        baker.make(
            Fee,
            fee_type=FeeType.PAYMENT_REVERSAL,
            balance_transaction=self.bt,
        )
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(event_data.fee, FeeDetails(fee_type=FeeType.PAYMENT_REVERSAL))

    def test_event__refund(self):
        baker.make(
            Refund,
            reason='some stupid reason',
            balance_transaction=self.bt,
        )
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(event_data.refund, RefundDetails(reason='some stupid reason'))

    def test_message__transfer_fund(self):
        baker.make(
            TransferFund,
            origin=TransferFundOrigin.BOOKSY_GIFT_CARDS,
            balance_transaction=self.bt,
        )
        event_data = construct_transaction_created_or_updated_event_payload(self.bt.id)

        self.assertEqual(
            event_data.transfer_fund,
            TransferFundDetails(
                origin=TransferFundOrigin.BOOKSY_GIFT_CARDS,
            ),
        )
