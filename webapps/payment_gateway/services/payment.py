from dataclasses import asdict
from typing import Optional

from django.db import transaction
from django.utils import timezone

from lib.db import PAYMENTS_DB
from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    RefundSplitsEntity,
    PaymentSplitsEntity,
)
from lib.payment_gateway.enums import (
    NON_CLIENT_SECRET_PAYMENT_METHODS,
    SERVER_SIDE_PAYMENT_METHODS,
    BalanceTransactionStatus,
    BalanceTransactionType,
    PaymentMethodType,
    PaymentStatus,
    WalletOwnerType,
)
from lib.payment_gateway.events import (
    payment_gateway_balance_transaction_created_event,
    payment_gateway_balance_transaction_updated_event,
)
from lib.payment_providers.entities import (
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
)
from lib.payments.enums import PaymentError, PaymentProviderCode
from lib.smartlock import SmartLock
from webapps.payment_gateway.adapters import PaymentProvidersAdapter
from webapps.payment_gateway.consts import (
    MINIMAL_PAYMENT_AMOUNT,
    MINIMAL_PAYMENT_FEE_AMOUNT,
)
from webapps.payment_gateway.exceptions import (
    InvalidAmount,
    InvalidOperation,
    InvalidSenderReceiver,
    OperationNotAllowed,
)
from webapps.payment_gateway.models import BalanceTransaction, Payment, Wallet
from webapps.payment_gateway.services.balance_transaction import BalanceTransactionService
from webapps.payment_gateway.tasks import update_payment_fee_amount_task


class PaymentService:
    @staticmethod
    def _map_payment_status_to_bt_status(payment_status: PaymentStatus) -> BalanceTransactionStatus:
        return {
            PaymentStatus.NEW: BalanceTransactionStatus.PROCESSING,
            PaymentStatus.SENT_FOR_AUTHORIZATION: BalanceTransactionStatus.PROCESSING,
            PaymentStatus.ACTION_REQUIRED: BalanceTransactionStatus.PROCESSING,
            PaymentStatus.AUTHORIZED: BalanceTransactionStatus.PROCESSING,
            PaymentStatus.AUTHORIZATION_FAILED: BalanceTransactionStatus.FAILED,
            PaymentStatus.SENT_FOR_CAPTURE: BalanceTransactionStatus.PROCESSING,
            PaymentStatus.CAPTURED: BalanceTransactionStatus.SUCCESS,
            PaymentStatus.CAPTURE_FAILED: BalanceTransactionStatus.FAILED,
            PaymentStatus.CANCELED: BalanceTransactionStatus.CANCELED,
        }.get(payment_status)

    @staticmethod
    def _change_payment_status(
        payment: Payment,
        new_status: PaymentStatus,
    ):
        """
        checks if such transition is possible and changes attribute on payment,
        it does not save it to the database

        :raises: OperationNotAllowed if such status transition is not allowed
        """
        allowed_new_statuses_map = {
            # current status: allowed new statuses
            PaymentStatus.NEW: [
                PaymentStatus.NEW,
                PaymentStatus.SENT_FOR_AUTHORIZATION,
                PaymentStatus.AUTHORIZED,  # Stripe BCR flow
                PaymentStatus.AUTHORIZATION_FAILED,  # Stripe BCR flow
                PaymentStatus.CANCELED,
            ],
            PaymentStatus.SENT_FOR_AUTHORIZATION: [
                PaymentStatus.SENT_FOR_AUTHORIZATION,
                PaymentStatus.ACTION_REQUIRED,
                PaymentStatus.AUTHORIZED,
                PaymentStatus.AUTHORIZATION_FAILED,
                PaymentStatus.CANCELED,
            ],
            PaymentStatus.ACTION_REQUIRED: [
                PaymentStatus.ACTION_REQUIRED,
                PaymentStatus.SENT_FOR_AUTHORIZATION,
                PaymentStatus.AUTHORIZED,
                PaymentStatus.AUTHORIZATION_FAILED,
                PaymentStatus.CANCELED,
            ],
            PaymentStatus.AUTHORIZED: [
                PaymentStatus.AUTHORIZED,
                PaymentStatus.SENT_FOR_CAPTURE,
                PaymentStatus.CAPTURED,
                PaymentStatus.CAPTURE_FAILED,
                PaymentStatus.CANCELED,
            ],
            PaymentStatus.AUTHORIZATION_FAILED: [PaymentStatus.AUTHORIZATION_FAILED],
            PaymentStatus.SENT_FOR_CAPTURE: [
                PaymentStatus.SENT_FOR_CAPTURE,
                PaymentStatus.CAPTURED,
                PaymentStatus.CAPTURE_FAILED,
                PaymentStatus.CANCELED,
            ],
            PaymentStatus.CAPTURED: [PaymentStatus.CAPTURED],
            PaymentStatus.CAPTURE_FAILED: [PaymentStatus.CAPTURE_FAILED],
            PaymentStatus.CANCELED: [PaymentStatus.CANCELED],
        }
        if payment.balance_transaction.payment_method in SERVER_SIDE_PAYMENT_METHODS:
            allowed_new_statuses_map[PaymentStatus.SENT_FOR_AUTHORIZATION].append(
                # for stripe auto capture flow
                PaymentStatus.CAPTURED
            )
            allowed_new_statuses_map[PaymentStatus.ACTION_REQUIRED].append(
                # for stripe auto capture flow with 3ds
                PaymentStatus.CAPTURED
            )
        if payment.balance_transaction.payment_method == PaymentMethodType.KEYED_IN_PAYMENT:
            allowed_new_statuses_map[PaymentStatus.NEW].append(PaymentStatus.CAPTURED)
            allowed_new_statuses_map[PaymentStatus.AUTHORIZATION_FAILED].append(
                PaymentStatus.SENT_FOR_AUTHORIZATION
            )
            allowed_new_statuses_map[PaymentStatus.SENT_FOR_AUTHORIZATION].append(
                PaymentStatus.CAPTURED
            )
        if payment.balance_transaction.payment_method == PaymentMethodType.APPLE_PAY:
            allowed_new_statuses_map[PaymentStatus.NEW].append(PaymentStatus.CAPTURED)
        if new_status not in allowed_new_statuses_map.get(payment.status):
            raise OperationNotAllowed(
                f"Invalid status transition Payment [{payment.status}] -> [{new_status}]"
            )

        payment.status = new_status
        return payment

    @staticmethod
    def update_payment(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        payment: Payment,
        new_status: PaymentStatus,
        new_amount: int = None,
        error_code: PaymentError = None,
        action_required_details: dict = None,
        metadata: dict = None,
        auto_capture: bool = None,
    ):
        """
        Updates Payment object and related BalanceTransaction
        """
        balance_transaction = payment.balance_transaction
        # pylint: disable=too-many-boolean-expressions
        if (
            new_status == payment.status
            and (new_amount is None or new_amount == balance_transaction.amount)
            and (error_code is None or error_code == payment.error_code)
            and (
                action_required_details is None
                or action_required_details == payment.action_required_details
            )
            and (metadata is None or metadata == payment.metadata)
        ):
            return

        with SmartLock(key=str(balance_transaction.id)):
            new_fee_amount = None
            with transaction.atomic(using=PAYMENTS_DB):
                fields_to_update = ['status']
                balance_transaction.refresh_from_db()
                payment = PaymentService._change_payment_status(
                    payment=payment, new_status=new_status
                )
                if new_status == PaymentStatus.CAPTURED and not payment.capture_date:
                    payment.capture_date = timezone.now()
                payment.error_code = error_code if error_code else payment.error_code
                payment.action_required_details = (
                    action_required_details
                    if action_required_details
                    else payment.action_required_details
                )
                payment.metadata = metadata if metadata is not None else payment.metadata
                payment.save(
                    update_fields=[
                        'status',
                        'capture_date',
                        'error_code',
                        'action_required_details',
                        'metadata',
                    ]
                )
                new_balance_transaction_status = PaymentService._map_payment_status_to_bt_status(
                    payment_status=payment.status,
                )
                balance_transaction = BalanceTransactionService.change_bt_status(
                    balance_transaction=balance_transaction,
                    new_status=new_balance_transaction_status,
                )

                if new_amount is not None and balance_transaction.amount != new_amount:
                    balance_transaction.amount = new_amount
                    fields_to_update.append('amount')
                    # amount has changed, fee_amount has to be recalculated
                    new_fee_amount = payment.payment_splits_entity.calculate_fee(new_amount)

                balance_transaction.save(update_fields=fields_to_update)
                BalanceTransactionService.save_to_history(balance_transaction)
            payment_gateway_balance_transaction_updated_event.send(
                asdict(balance_transaction.entity)
            )
            if new_fee_amount is not None:
                capture: bool = auto_capture is False  # capture if auto capture was deactivated
                update_payment_fee_amount_task.run(
                    balance_transaction.id,
                    new_fee_amount,
                    capture,
                )
            return balance_transaction

    @staticmethod
    def _validate_payment_sender_and_receiver(sender: Wallet, receiver: Wallet):
        """
        checks if such sender-receiver combination is valid
        (only customer/walk-in can send and only business, and Booksy can receive)
        """
        valid_receivers = {
            # sender: [list of possible receivers]
            WalletOwnerType.CUSTOMER: [WalletOwnerType.BUSINESS, WalletOwnerType.BOOKSY],
            WalletOwnerType.ANONYMOUS: [WalletOwnerType.BUSINESS],
        }.get(sender.owner_type, [])
        if receiver.owner_type not in valid_receivers:
            raise InvalidSenderReceiver

    @staticmethod
    def _validate_payment_amount(amount: int):
        if amount < MINIMAL_PAYMENT_AMOUNT:
            raise InvalidAmount(f"Payment amount {amount} is less than {MINIMAL_PAYMENT_AMOUNT}")

    @staticmethod
    def _validate_payment_fee_amount(amount: int):
        if amount < MINIMAL_PAYMENT_FEE_AMOUNT:
            raise InvalidAmount(
                f"Payment fee amount {amount} is less than {MINIMAL_PAYMENT_FEE_AMOUNT}"
            )

    # pylint: disable=too-many-arguments,too-many-positional-arguments
    @staticmethod
    def initialize_payment(
        amount: int,
        payment_splits: PaymentSplitsEntity,
        refund_splits: RefundSplitsEntity,
        dispute_splits: DisputeSplitsEntity,
        sender: Wallet,
        receiver: Wallet,
        payment_method: PaymentMethodType,
        payment_provider_code: PaymentProviderCode,
        capture_automatically: bool,
        payment_token: str = None,
        additional_data: dict = None,
        metadata: dict = None,
    ) -> BalanceTransaction:
        """
        An entrypoint in the payment flow.

        :param amount: amount to be paid
        :param payment_splits: fee for Booksy
        :param refund_splits: info about the fees that will be charged
        when a refund will occur in the future
        :param dispute_splits: info about the fees that will be charged
        when a chargeback will occur in the future
        :param sender: sending wallet
        :param receiver: receiving wallet
        :param payment_method: Payment method
        :param payment_provider_code: Payment provider
        :param capture_automatically: if true, payment will be automatically captured
        when the funds are capturable; if false, payment will hang in the status authorized,
        and will have to be manually captured later.
        :param payment_token: payment token
        :param additional_data: additional data related to the payment
        :param metadata: metadata related to the payment
        """
        PaymentService._validate_payment_sender_and_receiver(sender=sender, receiver=receiver)
        PaymentService._validate_payment_amount(amount=amount)

        payment_fee_amount = payment_splits.calculate_fee(amount)
        if receiver.owner_type == WalletOwnerType.BOOKSY:
            payment_fee_amount = 0
        PaymentService._validate_payment_fee_amount(amount=payment_fee_amount)

        external_id = PaymentProvidersAdapter.initialize_payment(
            receiver_wallet=receiver,
            amount=amount,
            fee_amount=payment_fee_amount,
            payment_method=payment_method,
            provider_code=payment_provider_code,
            payment_token=payment_token,
            capture_automatically=capture_automatically,
            sender_wallet=sender,
            additional_data=additional_data,
            metadata=metadata,
        ).id
        with transaction.atomic(using=PAYMENTS_DB):
            balance_transaction = BalanceTransaction(
                receiver=receiver,
                sender=sender,
                amount=amount,
                fee_amount=payment_fee_amount,
                status=BalanceTransactionStatus.PROCESSING,
                payment_method=payment_method,
                payment_provider_code=payment_provider_code,
                transaction_type=BalanceTransactionType.PAYMENT,
                external_id=external_id,
                payout=None,
                parent_balance_transaction=None,
                metadata=metadata or {},
            )
            balance_transaction.save()

            payment = Payment(
                balance_transaction=balance_transaction,
                status=PaymentStatus.NEW,
                payment_splits=payment_splits.as_dict(),
                refund_splits=refund_splits.as_dict(),
                dispute_splits=dispute_splits.as_dict(),
                metadata=metadata or {},
            )
            payment.save()
            BalanceTransactionService.save_to_history(balance_transaction)

        payment_gateway_balance_transaction_created_event.send(asdict(balance_transaction.entity))
        return balance_transaction

    @staticmethod
    def _validate_balance_transaction_type(balance_transaction: BalanceTransaction):
        """
        Make sure that we are operating on a payment
        """
        if balance_transaction.transaction_type != BalanceTransactionType.PAYMENT:
            raise OperationNotAllowed

    @staticmethod
    def _validate_server_payment_method(balance_transaction: BalanceTransaction):
        if balance_transaction.payment_method == PaymentMethodType.KEYED_IN_PAYMENT:
            return
        if balance_transaction.payment_method not in SERVER_SIDE_PAYMENT_METHODS:
            raise InvalidOperation(
                f'Payment method {balance_transaction.payment_method} is not recognized.'
            )

    @staticmethod
    def authorize_payment(
        balance_transaction: BalanceTransaction,
        additional_data: AuthAdditionalDataEntity | None,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        off_session: bool | None = None,
    ):
        """
        only for server-side payment methods, it will use receivers default tokenized payment
        method to pay for this payment
        """
        PaymentService._validate_balance_transaction_type(balance_transaction)
        PaymentService._validate_server_payment_method(balance_transaction)

        with SmartLock(key=str(balance_transaction.id)):
            balance_transaction.refresh_from_db()

            PaymentProvidersAdapter.authorize_payment(
                payment_id=balance_transaction.external_id,
                payment_method_data=payment_method_data,
                additional_data=additional_data,
                off_session=off_session,
            )
            # capture is not needed because auto capture
            # should be enabled in the beginning of the transaction

    @staticmethod
    def capture_payment(
        balance_transaction: BalanceTransaction,
    ) -> None:
        """
        Executes the capture process for a BalanceTransaction.

        During this operation, additional validation checks are performed.

        :param balance_transaction: BalanceTransaction instance.
        """
        PaymentService._validate_balance_transaction_type(balance_transaction)
        PaymentService._validate_server_payment_method(balance_transaction)

        # Initiate the error process if the 'balance_transaction' lacks an 'external_id'.
        # This typically happens when attempting to capture a migrated BalanceTransaction.
        if not balance_transaction.external_id:
            return PaymentService.update_payment(
                payment=balance_transaction.payment,
                new_status=PaymentStatus.CAPTURE_FAILED,
                auto_capture=None,
                new_amount=None,
                error_code=PaymentError.NOT_PERMITTED,
                action_required_details=None,
                metadata=None,
            )

        PaymentProvidersAdapter.capture_payment(
            payment_id=balance_transaction.external_id,
        )

    @staticmethod
    def update_fee_amount(
        balance_transaction: BalanceTransaction,
        payment_fee_amount: int,
    ):
        PaymentService._validate_balance_transaction_type(balance_transaction)
        with SmartLock(key=str(balance_transaction.id)):
            balance_transaction.refresh_from_db()

            if balance_transaction.fee_amount == payment_fee_amount:
                return
            # if fee amount has actually changed, alter it in the provider system too
            # provider's system too
            PaymentProvidersAdapter.modify_payment(
                payment_id=balance_transaction.external_id,
                fee_amount=payment_fee_amount,
            )

            balance_transaction.fee_amount = payment_fee_amount
            balance_transaction.save(update_fields=['fee_amount'])
            BalanceTransactionService.save_to_history(balance_transaction)

        payment_gateway_balance_transaction_updated_event.send(asdict(balance_transaction.entity))

    @staticmethod
    def get_payment_client_token(balance_transaction: BalanceTransaction) -> str:
        """
        only for client-side payment methods.
        """
        PaymentService._validate_balance_transaction_type(balance_transaction)

        if balance_transaction.payment_method in NON_CLIENT_SECRET_PAYMENT_METHODS:
            raise InvalidOperation

        return PaymentProvidersAdapter.get_payment_client_token(
            payment_id=balance_transaction.external_id
        ).token

    @staticmethod
    def mark_payment_as_failed(
        balance_transaction: BalanceTransaction,
        error_code: Optional[str] = None,
    ):
        """
        Only for client-side payment methods
        """
        PaymentService._validate_balance_transaction_type(balance_transaction)

        if balance_transaction.payment_method in SERVER_SIDE_PAYMENT_METHODS:
            raise InvalidOperation

        PaymentProvidersAdapter.mark_payment_as_failed(
            payment_id=balance_transaction.external_id,
            error_code=error_code,
        )
