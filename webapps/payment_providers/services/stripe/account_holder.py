import datetime
import logging
import traceback

import stripe
from django.conf import settings

from country_config import Country
from lib.enums import StrEnum
from lib.payment_providers.entities import (
    AccountStatusResponse,
    CreateStripeAccountHolderAdditionalData,
    PayoutMethodDetails,
    ProviderAccountDetails,
    ProviderAccountStatus,
    StripeAccountDetails,
    StripeAccountHolderEntity,
    StripeAccountHolderExternalSourceAdditionalData,
    StripeAccountHolderUserInputKYCAdditionalData,
    StripeAccountHolderModifyData,
    StripeAccountHolderSettingsEntity,
    UserInputKYC,
)
from lib.payment_providers.enums import (
    AccountHolderNotCreatedReason,
    ProviderAccountHolderStatus,
    StripeAccountType,
)
from lib.feature_flag.feature.payment import EffortlessKYCAfterEkranZBaba
from lib.payments.enums import PaymentProviderCode, PayoutType
from lib.tools import tznow, sget, sget_v2
from webapps.business_consents.enums import ConsentCode
from webapps.business_consents.ports import BusinessConsentsPort
from webapps.payment_providers.adapters.stripe_integration import StripeIntegrationAdapter
from webapps.payment_providers.consts.stripe import StripeMigrationSource
from webapps.payment_providers.exceptions.common import (
    BusinessKYCDataNotFoundException,
)
from webapps.payment_providers.exceptions.stripe import StripeActionNotImplemented
from webapps.payment_providers.models import (
    AccountHolder,
    StripeAccountHolder,
    StripeAccountHolderHistory,
    StripeAccountHolderSettings,
)
from webapps.payment_providers.providers.kyc_data_providers.pappers_fr import PappersFRProvider

from webapps.payment_providers.providers.kyc_data_providers.ceidg_pl import CeidgPLProvider
from webapps.payment_providers.providers.kyc_data_providers.subscription_buyer_pl import (
    SubscriptionBuyerPLProvider,
)
from webapps.payment_providers.providers.kyc_data_providers.rejestrio_pl import RejestrIOPLProvider
from webapps.payment_providers.providers.stripe import StripeProvider
from webapps.payment_providers.serializers.stripe import StripeAccountHolderHistorySerializer
from webapps.payment_providers.services.base import BaseAccountHolderServices
from webapps.stripe_integration.tools import synchronize_stripe_account_with_stripe_account_holder


logger = logging.getLogger('booksy.payment_providers.tasks')


class StripeBusinessType(StrEnum):
    INDIVIDUAL = 'individual'
    COMPANY = 'company'


class StripeAccountHolderServices(BaseAccountHolderServices):
    @staticmethod
    def create_account_holder(
        account_holder: AccountHolder,
        additional_data: CreateStripeAccountHolderAdditionalData,
    ) -> StripeAccountHolder:
        """
        Creates instance of StripeAccountHolder object

        :param account_holder: AccountHolder object
        :param additional_data: CreateStripeAccountHolderAdditionalData object
        """
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            stripe_account_holder = StripeAccountHolder(
                account_holder=account_holder, metadata={'business_id': additional_data.business_id}
            )
            stripe_response = StripeProvider.create_account_holder(
                stripe_account_holder,
            )
            if not stripe_response:
                return None
            stripe_account_holder.external_id = stripe_response.id
            stripe_account_holder.save()

        return stripe_account_holder

    @staticmethod
    # pylint: disable=too-many-return-statements,too-many-branches,too-many-statements
    def create_account_holder_from_external_source(
        account_holder: AccountHolder,
        additional_data: StripeAccountHolderExternalSourceAdditionalData,
    ) -> tuple[bool, AccountHolderNotCreatedReason | None]:
        """
        Creates StripeAccountHolder object (with stripe.Account and stripe.Person)
        from external data source. In other words migrate AccountHolder to Stripe.

        :param account_holder: AccountHolder object
        :param additional_data: additional data - needed to create AccountHolder
            and to fetch data from external source
        """
        sah = StripeAccountHolder.objects.filter(account_holder=account_holder).first()
        external_source_data = getattr(
            additional_data.external_source_data,
            settings.API_COUNTRY,
        )
        if settings.API_COUNTRY == Country.FR:
            if sah:
                # updating stripe account not supported in FR
                return False, AccountHolderNotCreatedReason.ALREADY_CREATED
            external_data_provider = PappersFRProvider
            business_type = StripeBusinessType.COMPANY
            try:
                external_source_obj = external_data_provider.get_account_holder_data(
                    tax_id=external_source_data.SIREN,
                )
            # pylint: disable=broad-exception-caught
            except BusinessKYCDataNotFoundException:
                return False, AccountHolderNotCreatedReason.WRONG_SIREN_NUMBER
            except RuntimeError:
                return False, AccountHolderNotCreatedReason.RUNTIME_ERROR

            migration_source = StripeMigrationSource.FR_SIREN
            # we do not migrate business with more than one owner
            if len(sget(external_source_obj, ['beneficiaires_effectifs'], [])) != 1:
                return False, AccountHolderNotCreatedReason.MORE_THAN_ONE_OWNER
        elif settings.API_COUNTRY == Country.PL:
            business_type = StripeBusinessType.INDIVIDUAL  # always migrate JDG's as individual's
            if external_source_data.NIP is None:  # PESEL
                external_data_provider = SubscriptionBuyerPLProvider
                try:
                    external_source_obj = external_data_provider.get_account_holder_data(
                        business_id=additional_data.provider_data.business_id,
                    )
                except BusinessKYCDataNotFoundException:
                    return (
                        False,
                        AccountHolderNotCreatedReason.NO_SUBSCRIPTIONBUYER_DATA,
                    )

                migration_source = StripeMigrationSource.PL_PESEL
            else:
                external_data_provider = CeidgPLProvider
                try:
                    if external_source_data.date_of_birth is None:
                        # don't check in CEIDG, its a company (spółka)
                        raise BusinessKYCDataNotFoundException
                    external_source_obj = external_data_provider.get_account_holder_data(
                        tax_id=external_source_data.NIP,
                    )
                except BusinessKYCDataNotFoundException:
                    # check if this NIP is not sp.zoo
                    external_data_provider = RejestrIOPLProvider
                    business_type = StripeBusinessType.COMPANY
                    try:
                        external_source_obj = external_data_provider.get_account_holder_data(
                            tax_id=external_source_data.NIP,
                        )
                    except BusinessKYCDataNotFoundException:
                        return (
                            False,
                            AccountHolderNotCreatedReason.INVALID_NIP,
                        )
                except RuntimeError as e:  # pylint: disable=broad-exception-caught
                    logger.warning(
                        "create_account_holder_from_external_source Runtime error: %s",
                        e,
                    )
                    return False, AccountHolderNotCreatedReason.RUNTIME_ERROR

                migration_source = StripeMigrationSource.PL_NIP
        else:
            raise StripeActionNotImplemented(
                f'create_account_holder_from_external_source is not implemented '
                f'on {settings.API_COUNTRY} country',
            )

        #  Account
        if business_type == StripeBusinessType.COMPANY:
            account_token_data = external_data_provider.get_company_account_token_data(
                external_source_obj=external_source_obj,
            )
            # Stripe is accepting at most 100 chars
            name_chars_limited = account_token_data['account']['company']['name'][:100]
            account_token_data['account']['company']['name'] = name_chars_limited
        else:
            account_token_data = external_data_provider.get_individual_account_token_data(
                external_source_obj=external_source_obj,
                additional_data=additional_data,
            )
        account_token = StripeProvider.create_account_holder_token(
            data=account_token_data,
        )
        stripe_account_request_params = {
            'type': 'custom',
            'business_profile': {
                'url': 'www.booksy.com',
            },
            'capabilities': {
                "transfers": {
                    "requested": True,
                },
            },
        }
        if not sah:
            StripeIntegrationAdapter.get_or_create_stripe_account(
                account_holder=account_holder,
                account_token=account_token.id,
                request_params=stripe_account_request_params,
                force_synchronize_with_new_structure=True,
                account_type=StripeAccountType.CUSTOM,
            )
        else:
            stripe_account_request_params['account_token'] = account_token.id
            StripeProvider.update_account_holder_with_external_params(
                stripe_account_holder=sah,
                stripe_account_request_params=stripe_account_request_params,
                business_id=additional_data.provider_data.business_id,
            )

        if business_type == StripeBusinessType.COMPANY:
            # create company persons only if no person existed before
            persons_already_created = bool(
                StripeProvider.list_persons(stripe_account_holder=sah) if sah else None
            )
            if not persons_already_created:
                # Person(s)
                persons_token_data = external_data_provider.get_company_persons_token_data(
                    external_source_obj=external_source_obj,
                    additional_data=additional_data,
                )
                for person_token_data in persons_token_data:
                    person_token = StripeProvider.create_person_token(
                        data=person_token_data,
                    )
                    StripeProvider.create_person(
                        stripe_account_holder=account_holder.stripe_account_holder,
                        token=person_token.id,
                    )

                # Update Account
                account_token = StripeProvider.create_account_holder_token(
                    data={
                        'api_key': settings.STRIPE_PUBLIC_API_KEY,
                        'account': {
                            'company': {
                                'directors_provided': True,
                                'executives_provided': True,
                                'owners_provided': True,
                            },
                        },
                    }
                )
                StripeProvider.update_account_holder_with_token(
                    stripe_account_holder=account_holder.stripe_account_holder,
                    account_token=account_token.id,
                )
        stripe_account_holder = account_holder.stripe_account_holder
        stripe_account_holder.metadata['migration_source'] = migration_source
        stripe_account_holder.save(update_fields=['metadata'])
        return True, None

    @staticmethod
    # pylint: disable=too-many-return-statements,too-many-branches,too-many-statements
    def create_account_holder_with_user_input_kyc(
        account_holder: AccountHolder,
        additional_data: StripeAccountHolderUserInputKYCAdditionalData,
    ) -> tuple[bool, AccountHolderNotCreatedReason | None]:
        """
        Creates StripeAccountHolder object (with stripe.Account)
        with data from BusinessConsent (Effortless KYC)

        :param account_holder: AccountHolder object
        :param additional_data: additional data - needed to create AccountHolder
        """

        user_input_kyc_data = getattr(
            additional_data.user_input_kyc_data,
            settings.API_COUNTRY,
        )
        if user_input_kyc_data.type == StripeBusinessType.COMPANY:
            entity_data = {
                'business_type': 'company',
                'company': {
                    # Stripe is accepting at most 100 chars
                    'name': user_input_kyc_data.company_name[:100]
                },
            }
        else:
            entity_data = {
                'business_type': 'individual',
                'individual': {
                    'first_name': user_input_kyc_data.first_name,
                    'last_name': user_input_kyc_data.last_name,
                },
            }

        stripe_account_request_params = {
            'type': 'custom',
            'business_profile': {
                'url': 'www.booksy.com',
            },
            'tos_acceptance': {
                'date': int(datetime.datetime.timestamp(user_input_kyc_data.filling_date)),
                'ip': user_input_kyc_data.filling_ip,
            },
            'capabilities': {
                "transfers": {
                    "requested": True,
                },
            },
            **entity_data,
        }

        sah = StripeAccountHolder.objects.filter(account_holder=account_holder).first()
        if not sah:
            external_id = StripeIntegrationAdapter.get_or_create_stripe_account(
                account_holder=account_holder,
                request_params=stripe_account_request_params,
                force_synchronize_with_new_structure=True,
                account_type=StripeAccountType.CUSTOM,
            )
            # fake refresh account metadata to trigger account.updated event from Stripe
            # to update/setup statuses, payment types etc. in our app
            StripeAccountHolderServices.force_account_notification(
                business_id=additional_data.provider_data.business_id, external_id=external_id
            )
        else:
            StripeProvider.update_account_holder_with_external_params(
                stripe_account_holder=sah,
                stripe_account_request_params=stripe_account_request_params,
                business_id=additional_data.provider_data.business_id,
            )

        return True, None

    @staticmethod
    def force_account_notification(business_id: int, external_id: str):
        old_metadata = StripeProvider.generate_account_metadada(business_id)
        stripe.Account.modify(
            external_id,
            metadata={**old_metadata, "test_metadata": "metadata_refresh"},
        )
        stripe.Account.modify(
            external_id,
            metadata={**old_metadata, "test_metadata": ""},
        )

    @staticmethod
    def get_account_holder_info(account_holder: AccountHolder) -> StripeAccountHolderEntity:
        """
        Returns StripeAccountHolder entity

        :param account_holder: AccountHolder object
        """
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None
        return stripe_account_holder.entity

    @staticmethod
    def get_provider_account_online_balance(account_holder: AccountHolder):
        balance_object = StripeProvider.get_balance(
            stripe_account_holder=account_holder.stripe_account_holder
        )
        stripe_balance_types = ['available', 'pending', 'instant_available']
        result = {balance_type: 0 for balance_type in stripe_balance_types}

        for balance_type in stripe_balance_types:
            if balance := sget_v2(balance_object, [balance_type]):
                result[balance_type] = next(
                    (
                        currency_balance.amount
                        for currency_balance in balance
                        if currency_balance['currency'].lower() == settings.CURRENCY_CODE.lower()
                    ),
                    0,
                )

        result['available_for_fast_payout'] = result.pop('instant_available')
        result['total'] = result['available'] + result['pending']

        return result

    @staticmethod
    def get_account_holder_settings(
        account_holder: AccountHolder,
    ) -> StripeAccountHolderSettingsEntity:
        """
        Returns StripeAccountHolderSettingsEntity entity

        :param account_holder: AccountHolder object
        """
        stripe_account_holder_settings, _ = StripeAccountHolderSettings.objects.get_or_create(
            account_holder=account_holder,
        )
        return stripe_account_holder_settings.entity

    @staticmethod
    def set_account_holder_settings(
        account_holder: AccountHolder,
        account_holder_settings_entity: StripeAccountHolderSettingsEntity,
    ) -> StripeAccountHolderSettingsEntity:
        """
        Set StripeAccountHolderSettingsEntity entity

        :param account_holder: AccountHolder object
        :param account_holder_settings_entity: StripeAccountHolderSettingsEntity
        """
        stripe_account_holder_settings, _ = StripeAccountHolderSettings.objects.get_or_create(
            account_holder=account_holder,
        )
        fields = {
            'pba_fees_accepted': account_holder_settings_entity.pba_fees_accepted,
            'bcr_fees_accepted': account_holder_settings_entity.bcr_fees_accepted,
            'tap_to_pay_fees_accepted': account_holder_settings_entity.tap_to_pay_fees_accepted,
            # pylint: disable=line-too-long
            'tap_to_pay_celebration_screen_shown': account_holder_settings_entity.tap_to_pay_celebration_screen_shown,
        }
        if (
            # If changes from False to True
            not stripe_account_holder_settings.tap_to_pay_fees_accepted
            and account_holder_settings_entity.tap_to_pay_fees_accepted
        ) and not stripe_account_holder_settings.tap_to_pay_fees_accepted_at:  # prevent reset
            fields['tap_to_pay_fees_accepted_at'] = (
                account_holder_settings_entity.tap_to_pay_fees_accepted_at
            )

        fields = {k: v for k, v in fields.items() if v is not None}
        for key, value in fields.items():
            setattr(stripe_account_holder_settings, key, value)
        stripe_account_holder_settings.save(update_fields=list(fields.keys()))
        return stripe_account_holder_settings.entity

    @staticmethod
    def _get_consent_if_exists(business_id: int):
        business_consent = None
        if settings.API_COUNTRY == Country.US:
            business_consent = BusinessConsentsPort.get_consent(
                consent_code=ConsentCode.US_USER_INPUT_KYC,
                business_id=business_id,
            )
        elif settings.API_COUNTRY == Country.PL:
            consents = (
                ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG,
                ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL,
                ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS,
            )
            for code in consents:
                business_consent = BusinessConsentsPort.get_consent(
                    consent_code=code,
                    business_id=business_id,
                )
                if business_consent:
                    break
        return business_consent

    @staticmethod
    def get_provider_account_details(
        account_holder: AccountHolder,
    ) -> ProviderAccountDetails | None:
        # pylint: disable=cyclic-import
        from webapps.payments.views.account_management import get_available_payout_method_types
        from webapps.payments.views.account_management import get_available_fast_payout_method_types
        from webapps.pos.enums.bank_account_number_form import BANK_ACCOUNT_NUMBER_FORM
        from webapps.payment_gateway.ports import PaymentGatewayPort

        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None

        details = StripeProvider.get_account_details(
            external_id=stripe_account_holder.external_id,
            default_payout_method_for_fast_payout=(
                stripe_account_holder.default_payout_method_for_fast_payout
            ),
        )
        bc = None
        if EffortlessKYCAfterEkranZBaba():
            business_id = PaymentGatewayPort.get_account_holder_wallet(
                account_holder_id=account_holder.id,
            ).business_id
            bc = StripeAccountHolderServices._get_consent_if_exists(business_id)

        # pylint: disable=duplicate-code
        return ProviderAccountDetails(
            payment_provider_code=PaymentProviderCode.STRIPE,
            status=ProviderAccountHolderStatus(stripe_account_holder.status),
            fast_payouts_status=stripe_account_holder.fast_payouts_status,
            payments_enabled=details['payments_enabled'],
            payouts_enabled=details['payouts_enabled'],
            kyc_errors=details['kyc_errors'],
            payout_methods=details['payout_methods'],
            company_details=details['company_details'],
            personal_details=details['personal_details'],
            provider_specific=StripeAccountDetails(
                account_type=details['account_type'],
                business_type=details['business_type'],
                kyc_required_threshold=3000 * 100,  # 3000$
            ),
            available_payout_method_types=get_available_payout_method_types(),
            available_fast_payout_method_types=get_available_fast_payout_method_types(
                stripe_account_holder.fast_payouts_status
            ),
            bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
            kyc_verified_at_least_once=stripe_account_holder.kyc_verified_at_least_once,
            external_id=stripe_account_holder.external_id,
            user_input_kyc=UserInputKYC(
                show=bool(bc), consent_code=bc.consent_code if bc else None
            ),
        )

    @staticmethod
    def get_provider_account_status(
        account_holder: AccountHolder,
    ) -> ProviderAccountStatus | None:
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None

        return ProviderAccountStatus(
            payment_provider_code=PaymentProviderCode.STRIPE,
            status=ProviderAccountHolderStatus(stripe_account_holder.status),
            payouts_enabled=stripe_account_holder.payouts_enabled,
            kyc_verified_at_least_once=stripe_account_holder.kyc_verified_at_least_once,
        )

    @staticmethod
    def add_provider_payout_method(
        account_holder: AccountHolder,
        token: str,
    ) -> [PayoutMethodDetails, None]:
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None

        external_account = StripeProvider.add_external_account(
            account_holder=stripe_account_holder,
            token=token,
        )
        if not stripe_account_holder.payout_method_first_time_attached:
            StripeAccountHolderServices.update_provider_account_holder(
                account_holder=account_holder,
                data=StripeAccountHolderModifyData(payout_method_first_time_attached=tznow()),
            )
        return external_account

    @staticmethod
    def remove_provider_payout_method(
        account_holder: AccountHolder,
        token: str,
    ) -> None:
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None

        StripeProvider.remove_external_account(
            account_holder=stripe_account_holder,
            token=token,
        )

    @staticmethod
    def set_provider_payout_method_as_default(
        account_holder: AccountHolder,
        payout_type: PayoutType,
        token: str,
    ) -> None:
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None

        if payout_type == PayoutType.FAST:
            stripe_account_holder.default_payout_method_for_fast_payout = token
            stripe_account_holder.save(update_fields=['default_payout_method_for_fast_payout'])
            synchronize_stripe_account_with_stripe_account_holder(stripe_account_holder)
            return

        StripeProvider.set_external_account_as_default(
            account_holder=stripe_account_holder,
            token=token,
        )

    @staticmethod
    def get_kyc_link(
        account_holder: AccountHolder,
        refresh_url: str,
        return_url: str,
    ) -> str:
        """
        Returns KYC link used in KYC process

        :param account_holder: AccountHolder object
        https://stripe.com/docs/api/account_links/create
        :param refresh_url: link above
        :param return_url: link above
        """
        stripe_account_holder = account_holder.stripe_account_holder
        account_link = StripeProvider.get_account_link(
            account_holder=stripe_account_holder,
            refresh_url=refresh_url,
            return_url=return_url,
        ).url
        if not stripe_account_holder.account_link_first_time_created:
            StripeAccountHolderServices.update_provider_account_holder(
                account_holder=account_holder,
                data=StripeAccountHolderModifyData(account_link_first_time_created=tznow()),
            )
        return account_link

    @staticmethod
    def update_account_holder(
        account_holder: AccountHolder,
    ) -> StripeAccountHolder:
        """
        Modifies existing StripeAccountHolder object (common AccountHolder was updated)

        :param account_holder: AccountHolder object
        """
        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None
        StripeProvider.update_account_holder(
            stripe_account_holder,
        )
        return stripe_account_holder

    @staticmethod
    def update_provider_account_holder(
        account_holder: AccountHolder,
        data: StripeAccountHolderModifyData,
    ):
        """
        Modifies existing StripeAccountHolder object

        :param account_holder: AccountHolder object
        :param data: StripeAccountHolderModifyData
        """
        stripe_account_holder = account_holder.stripe_account_holder
        fields = {
            'account_link_first_time_created': data.account_link_first_time_created,
            'payout_method_first_time_attached': data.payout_method_first_time_attached,
        }
        fields = {k: v for k, v in fields.items() if v is not None}
        for key, value in fields.items():
            setattr(stripe_account_holder, key, value)
        stripe_account_holder.save(update_fields=list(fields.keys()))
        return stripe_account_holder.entity

    @staticmethod
    def save_account_holder_to_history(
        stripe_account_holder: StripeAccountHolder,
    ) -> StripeAccountHolderHistory:
        serializer = StripeAccountHolderHistorySerializer(stripe_account_holder)
        history = StripeAccountHolderHistory(
            data=serializer.data,
            stripe_account_holder=stripe_account_holder,
            traceback=traceback.format_stack(),
        )
        history.save()

    @staticmethod
    def get_account_holder_status_info(
        account_holder: AccountHolder,
    ) -> AccountStatusResponse | None:
        # pylint: disable=cyclic-import

        try:
            stripe_account_holder = account_holder.stripe_account_holder
        except AccountHolder.stripe_account_holder.RelatedObjectDoesNotExist:
            return None

        return AccountStatusResponse(
            is_kyced_and_active=ProviderAccountHolderStatus(stripe_account_holder.status)
            == ProviderAccountHolderStatus.VERIFIED
            and not stripe_account_holder.blocked
        )
