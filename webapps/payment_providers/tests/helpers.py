from datetime import date

from django.conf import settings

from lib.payment_providers.entities import (
    ProviderAccountDetails,
    PayoutMethodDetails,
    BankAccount,
    PersonalDetails,
    StripeAccountDetails,
    UserInputKYC,
)
from lib.payment_providers.enums import (
    ProviderAccountHolderStatus,
    PayoutMethodType,
    PayoutMethodStatus,
    StripeAccountBusinessType,
    StripeAccountType,
)
from lib.payments.enums import PaymentProviderCode
from webapps.business_consents.enums import ConsentCode
from webapps.payments.views.account_management import (
    get_available_payout_method_types,
    get_available_fast_payout_method_types,
)
from webapps.pos.enums.bank_account_number_form import BANK_ACCOUNT_NUMBER_FORM
from webapps.stripe_integration.enums import FastPayoutStatus

# pylint: disable=duplicate-code
dummy_provider_account_details = ProviderAccountDetails(
    payment_provider_code=PaymentProviderCode.STRIPE,
    status=ProviderAccountHolderStatus.VERIFIED,
    fast_payouts_status=FastPayoutStatus.AVAILABLE,
    kyc_verified_at_least_once=True,
    payments_enabled=True,
    payouts_enabled=True,
    kyc_errors=[],
    payout_methods=[
        PayoutMethodDetails(
            method_type=PayoutMethodType.BANK_ACCOUNT,
            status=PayoutMethodStatus.ACTIVE,
            error_code=None,
            card=None,
            bank_account=BankAccount(
                last_digits="1234",
                account_holder_name="John Smith",
                bank_name="Chase",
                routing_number="*********",
                country='us',
            ),
            token="12345",
            is_default_for_regular_payouts=True,
            is_default_for_fast_payouts=False,
            has_fast_payout_capability=True,
        )
    ],
    personal_details=PersonalDetails(
        first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
    ),
    company_details=None,
    provider_specific=StripeAccountDetails(
        account_type=StripeAccountType.CUSTOM,
        business_type=StripeAccountBusinessType.INDIVIDUAL,
        kyc_required_threshold=3_000_00,
    ),
    available_payout_method_types=get_available_payout_method_types(),
    available_fast_payout_method_types=get_available_fast_payout_method_types(
        FastPayoutStatus.AVAILABLE
    ),
    bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
    external_id='acct_123',
    user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
)
