import logging

from lib.enums import StrEnum
from webapps import consts

log = logging.getLogger('booksy.rivers')

_RIVER_CLIENT = None
_RECIEVERS = []


class BooksyAppVersions(StrEnum):
    B30 = '3.0.0'
    UNKNOWN = 'UNKNOWN'


class TransactionTypeEnum(StrEnum):
    SALE = 'Sale'
    DEPOSIT = 'Deposit'


class BooksySourcesNamesEnum(StrEnum):
    FRONTDESK = consts.FRONTDESK
    FRONTDESK_ANDROID = consts.FRONTDESK_ANDROID
    FRONTDESK_IOS = consts.FRONTDESK_IOS


class KYCStatus(StrEnum):
    STARTED = 'STARTED'
    ACTIVE = 'ACTIVE'
    INACTIVE = 'INACTIVE'


class InviteImportSource(StrEnum):
    FILE = 'file'
    ADDRESS_BOOK = 'address_book'
    NO_IMPORT = 'no_import'


class InviteAction(StrEnum):
    IMPORT_AND_INVITE = 'import_and_invite'
    INVITE_MULTIPLE_CLIENTS = 'invite_multiple_clients'
    INVITE_TO_BOOK = 'invite_to_book'
    QUICK_INVITE = 'quick_invite'
    ADD_CC = 'add_cc'
    POSTPONED = 'postponed'


class DeviceTypeName(StrEnum):
    ANDROID = 'Android'
    IOS = 'iOS'
    DESKTOP = 'Desktop'
    TABLET = 'Tablet'
    UNKNOWN = 'UNKNOWN'


class AvailableForValue(StrEnum):
    MORNING = 'morning'
    AFTERNOON = 'afternoon'
    EVENING = 'evening'
    ANYTIME = 'anytime'

    @classmethod
    def _missing_(cls, value: object) -> 'AvailableForValue':
        return cls.ANYTIME

    @classmethod
    def get_for_daytime_string(cls, daytime_string=None) -> 'AvailableForValue':
        if daytime_string and 'T' in daytime_string:
            daytime_string = daytime_string.split('T')[1]
        return cls(daytime_string)


class AnalyticEventEnums(StrEnum):  # TODO refactor on: MAR-958.enums_analytic_events:
    BCR_STRIPE_KYC_PENDING_ACCOUNT = 'BCR_Stripe_KYC_Pending_Account'
    BCR_STRIPE_KYC_VERIFIED_ACCOUNT = 'BCR_Stripe_KYC_Verified_Account'
    BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT = 'BCR_Stripe_KYC_Not_Verified_Account'
    BCR_TERMINAL_ORDERED = 'BCR_Terminal_Ordered'
    BCR_ORDER_RECEIVED = 'BCR_Order_Received'
    BCR_RESET_ACCOUNT_VERIFY = 'BCR_Reset_Account_Verify'
    BUSINESS_RE_TRIAL_ELIGIBLE = 'Business_Re_Trial_Eligible'
    PAYMENT_LINK_RECEIVED_FIRST_TIME = 'Customer_Payment_Link_Received_1st_Time'
    KYC_SUCCESS = 'KYC_Success'
    FIFTH_CB_IN_FOURTEEN_DAYS = '5th_CB_In_14_Days'
    APPOINTMENT_CREATED = 'Appointment_Created'
    IG_INTEGRATION_ATTEMPT = 'Instagram_Integration_Attempt'
    CLIENT_CARD_UPDATE = 'Client_Card_Update'
    NUMBER_OF_GBP_LOCATIONS = 'Number_Of_GBP_Locations'
    GBP_LOCATION_STATUS = 'GBP_Location_Status'
    GBP_LOCATION_MISMATCH = 'GBP_Location_Mismatch'


class AppointmentTypeEnum(StrEnum):
    MULTIBOOKING = 'MultiBooking'
    SINGLE = 'Single'
