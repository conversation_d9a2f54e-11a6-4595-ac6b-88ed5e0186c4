from types import MappingProxyType

from lib.enums import StrEnum
from webapps.segment.enums import AnalyticEventEnums
from webapps.segment.typing import GeoSquare


class UserRoleEnum(StrEnum):
    CUSTOMER = 'Customer'
    OWNER = 'Owner'
    STAFFER = 'Staffer'


class BoostStatusEnum(StrEnum):
    ACTIVE = 'Active'
    INACTIVE = 'Inactive'


ANALYTICS_TASK_TO_KILLSWITCH_MAP = {
    'analytics_business_registration_started_task': ('business_registration_started'),
    'analytics_business_registration_completed_task': ('business_registration_completed'),
    'analytics_customer_search_query_task': ('customer_search_query'),
    'analytics_customer_registration_completed_task': ('customer_registration_completed'),
    'analytics_business_info_updated_task': ('business_info_updated'),
    'analytics_business_categories_updated_task': ('business_categories_updated'),
    'analytics_cb_created_for_business_task': ('cb_created_for_business'),
    'analytics_cb_created_count_for_business_gtm_task': ('cb_created_for_business'),
    'analytics_cb_created_count_for_business_segment_task': ('cb_created_for_business'),
    'analytics_cb_finished_for_business_task': ('cb_finished_for_business'),
    'analytics_cb_finished_for_customer_task': ('cb_finished_for_customer'),
    'analytics_cb_customer_info_updated_task': ('cb_customer_info_updated'),
    'analytics_staffer_created_task': ('staffer_created'),
    'analytics_business_user_language_set': ('business_user_language_set'),
    'analytics_customer_user_language_set': ('customer_user_language_set'),
    'analytics_1st_paid_status_achieved': ('paid_status_achieved'),
    'analytics_1st_paid_status_achieved_gtm_task': ('paid_status_achieved'),
    'analytics_paid_status_achieved_branchio_task': ('paid_status_achieved'),
    'analytics_review_completed_task': ('review_completed'),
    'analytics_cb_created_for_customer_task': 'cb_created_for_customer',
    'analytics_boost_on_off_gtm_task': 'boost_on_off',
    'analytics_boost_on_branchio_task': 'boost_on',
    'analytics_boost_off_branchio_task': 'boost_off',
    'analytics_business_pos_updated_task': 'business_pos_updated',
    'analytics_business_subscription_updated_task': ('business_subscription_updated'),
    'analytics_business_app_opened_task': 'business_app_opened',
    'analytics_customer_app_opened_task': 'customer_app_opened',
    'analytics_business_status_updated_task': 'business_status_updated',
    'analytics_contact_preferences_updated_task': ('contact_preferences_updated'),
    'analytics_business_contact_preferences_updated_task': ('business_contact_pref_upd'),
    'analytics_business_pba_enabled_task': ('business_pba_enabled'),
    'analytics_onboarding_delay_set_task': ('business_delay_set'),
    'analytics_gtm_onboarding_business_go_live_task': ('onboarding_business_go_live'),
    'analytics_onboarding_business_go_live_task': ('onboarding_business_go_live'),
    'analytics_invite_all_clicked_task': ('invite_all_clicked'),
    'analytics_invite_process_completed_task': ('invite_process_completed'),
    'analytics_invite_process_completed_branchio_task': 'invite_process_completed_branchio',
    'analytics_postponed_invites_sent': ('postponed_invites_sent'),
    'analytics_business_report_generated_to_email': ('business_report_generated_to_email'),
    'analytics_kyc_success_task': ('kyc_success'),
    'analytics_kyc_failure_task': ('kyc_failure'),
    'analytics_bb_no_show_for_business_task': ('bb_no_show_for_business'),
    'analytics_cb_no_show_for_business_task': ('cb_no_show_for_business'),
    'analytics_1st_no_show_for_business_task': ('1st_no_show_for_business'),
    'analytics_checkout_transaction_completed_task': ('checkout_transaction_completed'),
    'analytics_payment_transaction_completed_task': ('payment_transaction_completed'),
    'analytics_basket_payment_completed_task': ('basket_payment_completed'),
    'analytics_protection_service_enabled_task': ('protection_service_enabled'),
    'analytics_bcr_reset_account_verify_gtm_task': ('bcr_reset_account_verify'),
    'analytics_bcr_stripe_kyc_not_verified_account_gtm_task': (
        'bcr_stripe_kyc_not_verified_account'
    ),
    'analytics_bcr_stripe_kyc_pending_account_gtm_task': ('bcr_stripe_kyc_pending_account'),
    'analytics_bcr_stripe_kyc_verified_account_gtm_task': ('bcr_stripe_kyc_verified_account'),
    'analytics_bcr_terminal_ordered_gtm_task': ('bcr_terminal_ordered'),
    'analytics_bcr_order_received_gtm_task': ('bcr_order_received'),
    'analytics_view_item_list_gtm_task': 'view_item_list',
    'analytics_bcr_order_received_segment_task': ('bcr_order_received'),
    'analytics_bcr_reset_account_verify_segment_task': ('bcr_reset_account_verify'),
    'analytics_bcr_stripe_kyc_pending_account_segment_task': ('bcr_stripe_kyc_pending_account'),
    'analytics_bcr_stripe_kyc_verified_account_segment_task': ('bcr_stripe_kyc_verified_account'),
    'analytics_bcr_stripe_kyc_not_verified_account_segment_task': (
        'bcr_stripe_kyc_not_verified_account'
    ),
    'analytics_bcr_terminal_ordered_segment_task': ('bcr_terminal_ordered'),
    'analytics_cb_started_for_customer': ('CB_Started_For_Customer'),
    'analytics_business_re_trial_eligible_task': (AnalyticEventEnums.BUSINESS_RE_TRIAL_ELIGIBLE),
}

CUSTOMER_EVENTS_ALLOWED_LOCATIONS = MappingProxyType(
    {
        'Chicago': GeoSquare(
            lat_min=41.*************,
            lat_max=42.*************,
            lon_min=-87.*************,
            lon_max=-87.*************,
        ),
        'Orlando': GeoSquare(
            lat_min=28.*************,
            lat_max=28.*************,
            lon_min=-81.*************,
            lon_max=-81.*************,
        ),
        'Tampa': GeoSquare(
            lat_min=27.*************,
            lat_max=28.*************,
            lon_min=-82.*************,
            lon_max=-82.*************,
        ),
        'Miami': GeoSquare(
            lat_min=25.*************,
            lat_max=25.8566928263461,
            lon_min=-80.3204886290455,
            lon_max=-80.1183160711954,
        ),
    }
)
