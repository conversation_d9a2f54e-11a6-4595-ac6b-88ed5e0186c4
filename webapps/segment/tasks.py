import datetime
import logging
from collections import defaultdict
from decimal import Decimal
from time import time
from typing import Dict, List, Optional

from django.apps import apps
from django.conf import settings
from django.db.models import BooleanField, Case, Count, F, QuerySet, Value, When
from django.db.models.functions import Coalesce

from country_config import Country
from lib.celery_tools import (
    branchio_analytics_retry_post_transaction_task,
    celery_task,
    gtm_analytics_retry_post_transaction_task,
    post_transaction_task,
    segment_analytics_retry_post_transaction_task,
)
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.email.client import IterableAPIError, IterableClient
from lib.facebook.enums import EventName
from lib.facebook.utils import convert_booksy_gender_to_facebook_gender
from lib.feature_flag.feature import (
    BranchIOCustomerBookingTrackingFlag,
    ForgetUserEmailGDPRFlag,
)
from lib.feature_flag.feature.booking import (
    RecreateBackendAppsflyerEventsInFacebookConversionApi,
    Send1stCBCreatedForCustomerEventToBranch,
)
from lib.fields.phone_number import get_prep_value_form
from lib.segment_analytics import get_segment_api
from lib.segment_analytics.enums import EventType, InvitationSentBy, InvitationSource
from lib.tools import grouper, id_to_external_api, sget, tznow, year_ago_from_tznow
from webapps.booking.enums import AppointmentStatusChoices
from webapps.business.adapters import CancellationReasonAdapter
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.segment.consts import BoostStatusEnum, UserRoleEnum
from webapps.segment.enums import (
    AnalyticEventEnums,
    AppointmentTypeEnum,
    AvailableForValue,
    BooksyAppVersions,
    KYCStatus,
)
from webapps.segment.facade import SegmentFacade
from webapps.segment.models import AppUsedByUserByDate
from webapps.segment.serializers import ViewItemListParametersSerializer
from webapps.segment.utils import (
    get_booking_score_optimized,
    get_days_with_cb_in_week,
    get_device_type_name_from_api_key,
    get_user_properties,
    get_user_role_by_id,
    get_week,
)
from webapps.user.const import Gender

# disable some specific analytics task that would be called during every test
DISBALED_DURING_PYTESTS = bool(settings.PYTEST)


_logger = logging.getLogger('booksy.analytics')
_analytics_1st_paid_status_achieved_logger = logging.getLogger(
    'booksy.analytics_1st_paid_status_achieved'
)
onboarding_analytics_logger = logging.getLogger('booksy.onboarding_analytics_logger')


def _get_business_model():
    return apps.get_model('business', 'Business', require_ready=False)


def filter_poll_responses(responses: QuerySet, poll_name: str) -> list:
    return [
        resp['choice__internal_name']
        for resp in responses
        if resp['choice__poll__name'] == poll_name
    ]


def single_green_merchant_report(business, customer_bookings_count, business_bookings_count):
    is_green = False
    if customer_bookings_count >= 15:
        is_green = True
    elif customer_bookings_count >= 5 and business_bookings_count >= 5:
        is_green = True

    segment_api = get_segment_api(business)
    segment_api.identify_merchant_green(is_green)


def wam_status_task(business):
    cbs_num = get_days_with_cb_in_week(business, last_week=True)

    segment_api = get_segment_api(business)
    segment_api.identify_wam(cbs_num)


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def merchant_activity_bulk(data):
    for business_id, business_bookings_count, customer_bookings_count in data:
        Business = _get_business_model()
        business = Business.objects.filter(id=business_id).first()

        if not business:
            continue

        wam_status_task(business)
        single_green_merchant_report(business, customer_bookings_count, business_bookings_count)


@post_transaction_task
def merchant_activity_weekly_report_task():
    """
    Recheck merchant activity in last week to mark as green if merchant have
    lots of bookings, compute WAM status
    """
    from django.db.models import Case, IntegerField, Sum, When

    from webapps.booking.models import Appointment
    from webapps.business.tools import get_all_active_businesses_ids

    prev_mon, prev_sun = get_week(last_week=True)

    active_business_ids = get_all_active_businesses_ids()

    businesses_appointments_type_count_query = (
        Appointment.objects.filter(created__gte=prev_mon, created__lte=prev_sun)
        .values('business')
        .annotate(
            business_bookings_count=Sum(
                Case(When(type=Appointment.TYPE.BUSINESS, then=1), default=0),
                output_field=IntegerField(),
            ),
            customer_bookings_count=Sum(
                Case(When(type=Appointment.TYPE.CUSTOMER, then=1), default=0),
                output_field=IntegerField(),
            ),
        )
    )
    businesses_appointments_type_count = {
        elem['business']: (
            elem['business'],
            elem['business_bookings_count'],
            elem['customer_bookings_count'],
        )
        for elem in businesses_appointments_type_count_query
    }

    for batch in grouper(active_business_ids, 500):
        merchant_activity_bulk.delay(
            [
                businesses_appointments_type_count.get(business_id, (business_id, 0, 0))
                for business_id in batch
            ]
        )


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def segment_api_appointment_booked_task(booking_id: int) -> None:
    from webapps.booking.models import SubBooking

    booking = (
        SubBooking.objects.filter(
            id=booking_id,
        )
        .select_related(
            'appointment__business',
            'appointment__business__owner',
            'appointment__source',
        )
        .first()
    )
    if booking is None:
        return

    segment_api = get_segment_api(
        business=booking.appointment.business,
        source=booking.appointment.source,
    )
    segment_api.appointment_booked(booking)


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def segment_api_customer_invitation_sent(
    business_id,
    booking_source_id,
    data,
    invitation_type,
    sent_by=None,
    source=None,
):
    from webapps.booking.models import BookingSources
    from webapps.business.models import Business

    business = Business.objects.get(id=business_id)
    booking_source = None
    if booking_source_id:
        booking_source = BookingSources.objects.get(id=booking_source_id)
    segment_api = get_segment_api(business, source=booking_source)
    segment_api.customer_invitation_sent(
        data=data,
        invitation_type=invitation_type,
        sent_by=sent_by or InvitationSentBy.MERCHANT,
        source=source or InvitationSource.CLIENT_INVITED,
    )


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def segment_api_subscription_reactivated(business_id, timestamp):
    from webapps.business.models import Business
    from webapps.purchase.models import Subscription

    business = Business.objects.get(id=business_id)
    segment_api = get_segment_api(business)
    segment_api.subscription_reactivated(
        subscription=Subscription.objects.filter(
            business_id=business.id,
            start__lte=datetime.datetime.fromisoformat(timestamp),
        )
        .order_by('-start')
        .first(),
    )


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def segment_api_subscription_expired(business_id, paid_subscription_id):
    from webapps.business.models import Business
    from webapps.purchase.models import Subscription

    business = Business.objects.get(id=business_id)
    subscription = Subscription.objects.get(id=paid_subscription_id)
    segment_api = get_segment_api(business)
    segment_api.subscription_expired(subscription)


@post_transaction_task
@using_db_for_reads(READ_ONLY_DB)
def segment_api_overdue_status_started(business_id):
    from webapps.business.models import Business

    business = Business.objects.get(id=business_id)
    segment_api = get_segment_api(business)
    segment_api.overdue_status_started()


# NEW MARTECH TASKS


@segment_analytics_retry_post_transaction_task
def analytics_business_registration_started_task(
    analytics,
    business_id,
    booking_source_id,
) -> None:
    from webapps.booking.models import BookingSources
    from webapps.business.models import Business

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .only(
            'id',
            'name',
            'created',
            'phone',
            'owner__first_name',
            'owner__last_name',
            'owner__email',
            'package',
        )
        .select_related(
            'owner',
        )
        .first()
    )
    if not business:
        return
    booking_source = BookingSources.objects.filter(id=booking_source_id).first()
    owner = business.owner
    offer_type = _get_offer_type_from_package(business.package)

    event_params = {
        'user_id': id_to_external_api(owner.id),
        'user_role': UserRoleEnum.OWNER.value,
        'email': owner.email,
        'phone': business.owner.cell_phone_with_prefix,
        'business_phone': business.phone_with_prefix,
        'country': settings.API_COUNTRY,
        'owner_email': owner.email,
        'owner_name': owner.full_name,
        'business_id': id_to_external_api(business.id),
        'business_name': business.name,
        'business_admin_status': Business.Status(business.status).name,
        'created_at': business.created.date().isoformat(),
        'app_version': BooksyAppVersions.B30.value,
        'offer_type': offer_type,
    }

    analytics.track('Business_Registration_Started_Backend', event_params)
    analytics.identify(event_params)


@segment_analytics_retry_post_transaction_task
def analytics_business_registration_completed_task(
    analytics,
    business_id,
    booking_source_id,
) -> None:
    from webapps.booking.models import BookingSources
    from webapps.business.models import Business
    from webapps.user.models import UserProfile
    from webapps.survey.models import RegistrationPollResponse

    business = (
        Business.objects.filter(id=business_id)
        .select_related(
            'primary_category',
            'region',
        )
        .first()
    )
    booking_source = BookingSources.objects.filter(id=booking_source_id).first()
    if not business or not booking_source:
        return

    owner = business.owner
    zip_code = business.zip
    if business.primary_category:
        primary_category_internal_name = business.primary_category.internal_name
    else:
        primary_category_internal_name = ''

    if business.region:
        state = business.region.get_parent_name_by_type(settings.ES_ADM_1_LVL)
    else:
        state = ''

    responses = (
        RegistrationPollResponse.objects.filter(
            choice__poll__name__in=[
                'registration_number_of_staffers',
                'registration_goals',
                'registration_other_apps',
                'registration_number_of_appointments',
            ],
            business_id=business_id,
        )
        .select_related('choice')
        .values('choice__poll__name', 'choice__internal_name')
    )

    number_of_staffers = filter_poll_responses(responses, 'registration_number_of_staffers')
    registration_goals = filter_poll_responses(responses, 'registration_goals')
    registration_other_apps = filter_poll_responses(responses, 'registration_other_apps')

    event_required_params = {
        'country': settings.API_COUNTRY,
        'owner_email': owner.email,
        'owner_name': owner.full_name,
        'owner_phone': owner.cell_phone_with_prefix,
        'business_id': id_to_external_api(business.id),
        'business_name': business.name,
        'business_primary_category': primary_category_internal_name,
        'business_admin_status': Business.Status(business.status).name,
        'created_at': business.created.isoformat()[:10],
        'email': owner.email,
        'city': business.city_or_region_city,
        'state': state,
        'postalCode': zip_code,
        'categories': list(business.categories.values_list('internal_name', flat=True)),
        'user_id': id_to_external_api(business.owner.id),
        'user_role': UserRoleEnum.OWNER,
        **_get_geo_information(business),
    }

    analytics.track(
        'Business_Registration_Completed',
        {
            'goals': registration_goals if number_of_staffers else None,
            'number_of_staffers': number_of_staffers[0] if number_of_staffers else None,
            'other_app': registration_other_apps if number_of_staffers else None,
            **event_required_params,
        },
    )

    analytics.identify(
        {
            'timeZone': business.time_zone_name,
            **event_required_params,
        }
    )
    business_profile = UserProfile.objects.filter(
        user=owner,
        profile_type=UserProfile.Type.BUSINESS,
    ).first()
    if not business_profile:
        return
    analytics_business_user_language_set.delay(
        profile_id=business_profile.id,
        context={
            'business_id': business.id,
            'session_user_id': owner.id,
            'source_id': booking_source_id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_customer_search_query_task(
    analytics,
    user_id: int,
    query: str = None,
    region_id: int = None,
    category_id: int = None,
    available_for: str = None,
    latitude: float = None,
    longitude: float = None,
) -> None:
    from webapps.business.models.category import BusinessCategory
    from webapps.structure.models import Region
    from webapps.user.models import User

    user = (
        User.objects.filter(
            id=user_id,
        )
        .values('id', 'email')
        .first()
    )

    if not (user and query):
        return
    if category_id:
        category = BusinessCategory.objects.filter(id=category_id).values('name').first()
        category_name = category.get('name')
    else:
        category_name = 'All'

    event_parameters = {
        'userId': id_to_external_api(user.get('id')),
        'email': user.get('email'),
        'search_query': query,
        'search_business_category': category_name,
    }

    if region_id:
        if region := Region.objects.filter(
            id=region_id,
        ).first():
            if city_region := region.get_parent_by_type(settings.ES_CITY_LVL, with_me=True):
                event_parameters['search_city'] = city_region.name

    if available_for:
        event_parameters['search_time'] = AvailableForValue.get_for_daytime_string(
            daytime_string=available_for,
        ).value.capitalize()

    if latitude and longitude:
        event_parameters.update(
            {
                'last_search_latitude': latitude,
                'last_search_longitude': longitude,
            }
        )

    analytics.track('Customer_Search_Query', event_parameters)
    analytics.identify(event_parameters)


@gtm_analytics_retry_post_transaction_task
def analytics_location_entered_gtm_task(analytics, here_maps_type: str):
    event_data = {
        'event_action': 'location_entered',
        'screen_name': 'server',
        'country': settings.API_COUNTRY,
        'here_maps_type': here_maps_type,
    }
    analytics.push(
        event_name='Search_Action',
        event_data=event_data,
        request_params={},
    )


@segment_analytics_retry_post_transaction_task
def analytics_customer_registration_completed_task(
    analytics,
    user_id,
    invited_by_business_id=None,
) -> None:
    from webapps.user.models import User, UserProfile
    from webapps.business.models import Business

    user = User.objects.filter(id=user_id).select_related('agreement').first()
    if not user:
        return

    web_communication_agreement = sget(user, ['agreement', 'web_communication_agreement']) or False
    control_group = not bool(user_id % 100)

    customer_profile_id, app_language = UserProfile.objects.filter(
        user=user,
        profile_type=UserProfile.Type.CUSTOMER,
    ).values_list('id', 'language').first() or (None, None)
    if not customer_profile_id:
        return

    business = None
    if str(invited_by_business_id).isdigit():
        business = Business.objects.filter(id=invited_by_business_id).first()

    common_properties = {
        'country': settings.API_COUNTRY,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'phone': get_prep_value_form(user.cell_phone),
        'email': user.email,
        'created_at': user.created.isoformat()[:10],
        'web_communication_agreement': web_communication_agreement,
        'app_language': app_language,
        'gender_code': user.gender or Gender.Both,
        'user_role': get_user_role_by_id(user.id if user else None),
        'control_group': control_group,
        'user_id': id_to_external_api(user.id),
        'invited_by_business_id': id_to_external_api(invited_by_business_id),
    }
    extra_properties = {
        'invited_business_name': business and business.name,
        'invited_provider_url': business and business.get_marketplace_deeplink(),
    }
    common_properties.update(extra_properties)
    analytics.track('Customer_Registration_Completed', common_properties)
    common_properties.pop('user_id')
    common_properties.pop('app_language')
    analytics.identify({**common_properties, 'locale': app_language})
    analytics_customer_user_language_set.delay(
        profile_id=customer_profile_id,
        context={
            'session_user_id': user.id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_info_updated_task(
    analytics,
    business_id,
) -> None:
    from webapps.business.models import Business

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'owner',
            'region',
            'primary_category',
        )
        .prefetch_related(
            'categories',
        )
        .first()
    )
    if not business:
        return
    owner = business.owner

    common_properties = {
        'business_name': business.name,
        'business_phone': business.phone_with_prefix,
    }
    track_properties = {
        'owner_email': owner.email,
        'owner_name': owner.full_name,
        'address': business.get_formatted_address()['address'],
        'city': business.city_or_region_city,
        'postalCode': business.zip,
        **_get_geo_information(business),
        **common_properties,
    }
    analytics.track('Business_Info_Updated', track_properties)

    analytics.identify(
        {
            'business_name': business.name,
            'business_primary_category': sget(business, ['primary_category', 'internal_name']),
            'business_categories': list(
                business.categories.all().values_list('internal_name', flat=True)
            ),
            'business_owner_email': owner.email,
            'business_owner_name': owner.full_name,
            'business_owner_phone': owner.cell_phone_with_prefix,
            'business_address': business.get_formatted_address()['address'],
            'business_city': business.city_or_region_city,
            'business_postalCode': business.zip,
            'timeZone': business.time_zone_name,
            **_get_geo_information(business),
            **common_properties,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_categories_updated_task(
    analytics,
    business_id,
) -> None:
    from webapps.business.models import Business

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'owner',
            'primary_category',
        )
        .prefetch_related(
            'categories',
        )
        .first()
    )
    if not business:
        return
    owner = business.owner

    internal_names = [c.internal_name for c in business.categories.all()]
    internal_names.sort()

    track_properties = {
        'email': owner.email,
        'business_id': id_to_external_api(business.id),
        'business_name': business.name,
        'business_primary_category': sget(business, ['primary_category', 'internal_name']),
        'business_categories': internal_names,
    }
    analytics.track('Business_Categories_Updated', track_properties)

    identify_properties = {
        'country': settings.API_COUNTRY,
        'email': owner.email,
        'user_role': UserRoleEnum.OWNER.value,
        'business_id': id_to_external_api(business.id),
        'business_name': business.name,
        'business_primary_category': sget(business, ['primary_category', 'internal_name']),
        'business_categories': internal_names,
    }
    analytics.identify(identify_properties)


def send_analytics_1st_cb_created_for_customer(
    analytics,
    track_common_properties,
    email,
    user_role,
):
    """
    additional event 1st_CB_Created_For_Customer
     inside CB_Created_For_Customer
    """
    first_cb_created_track_properties = dict(track_common_properties)
    analytics.track('1st_CB_Created_For_Customer', first_cb_created_track_properties)
    analytics.identify(
        {
            'email': email,
            'user_role': user_role,
            '1st_CB_created': True,
        }
    )
    if Send1stCBCreatedForCustomerEventToBranch():
        send_analytics_1st_cb_created_for_customer_to_branchio.delay(
            track_properties=first_cb_created_track_properties
        )
    if RecreateBackendAppsflyerEventsInFacebookConversionApi():
        send_analytics_1st_cb_created_for_customer_to_facebook.delay(
            track_properties=first_cb_created_track_properties, event_time=int(time())
        )


@branchio_analytics_retry_post_transaction_task
def send_analytics_1st_cb_created_for_customer_to_branchio(analytics, track_properties):
    analytics.track('1st_CB_Created_For_Customer', track_properties)


@post_transaction_task
def send_analytics_1st_cb_created_for_customer_to_facebook(track_properties, event_time):
    from lib.facebook.service import FacebookEventService

    FacebookEventService.send_event_with_custom_data(
        event_name=EventName.FIRST_CB_CREATED_FOR_CUSTOMER,
        data=track_properties,
        event_time=event_time,
    )


def send_analytics_1st_cb_created_for_business(analytics, common_track_properties, appointment):
    from webapps.business.models import Business
    from webapps.business.models.category import BusinessCategory

    first_cb_created_for_business_properties = dict(common_track_properties)
    first_cb_created_for_business_properties['source'] = appointment.source.name
    analytics.track('1st_CB_Created_For_Business', first_cb_created_for_business_properties)

    internal_names = sorted(
        BusinessCategory.objects.filter(
            businesses__id=appointment.business_id,
        ).values_list(
            'internal_name',
            flat=True,
        )
    )
    biz = appointment.business
    analytics.identify(
        {
            'user_role': get_user_role_by_id(
                biz.owner.id,
            ),
            'country': settings.API_COUNTRY,
            'email': biz.owner.email,
            'business_owner_email': biz.owner.email,
            'business_owner_name': biz.owner.full_name,
            'business_owner_phone': biz.owner.cell_phone_with_prefix,
            'business_address': biz.get_formatted_address()['address'],
            'business_id': id_to_external_api(appointment.business_id),
            'business_name': biz.name,
            'business_primary_category': (
                biz.primary_category and biz.primary_category.internal_name
            ),
            'business_categories': internal_names,
            'business_admin_status': Business.Status(biz.status).name,
            'created_at': biz.created.date(),
            'business_plan': _get_plan_name(biz),
            'business_city': biz.city_or_region_city,
            'business_postalCode': biz.zip,
            'state': biz.region.get_parent_name_by_type(settings.ES_ADM_1_LVL),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_created_for_business_task(
    analytics,
    appointment_id=None,
) -> None:
    """
    contrary to its name this event triggers for a created Appointment calls not for a created
    subbooking.
    """
    from webapps.booking.models import Appointment, SubBooking

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
            deleted__isnull=True,
        )
        .select_related(
            'booked_for',
            'business',
            'business__region',
            'business__owner',
            'business__primary_category',
        )
        .annotate_family_and_friends_role()
        .first()
    )
    if not appointment:
        return

    cb_count = Appointment.objects.filter(
        business_id=appointment.business_id,
        type=Appointment.TYPE.CUSTOMER,
    ).count()
    subbookings_dct = (
        SubBooking.objects.filter(
            appointment_id=appointment_id,
            deleted__isnull=True,
        )
        .annotate_is_chargeable_for_analytics()
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
        )
        .values(
            'id',
            'booked_from',
            'service_variant__service_id',
            'service_variant__service__name',
            'service_variant__service__service_category__name',
            'service_variant__service__service_category_id',
            'booking_value',
            'appointment__booked_for',
            'appointment__business_id',
            'appointment__customer_email',
            'appointment__business__region__name',
            'appointment__business__region',
            'appointment__business__name',
            'appointment__source__name',
            'appointment__business__primary_category__internal_name',
            'appointment__booked_for_id',
            'appointment__booked_for__invited',
            'is_chargeable_for_analytics',
        )
    )

    business_region = appointment.business.region
    zip_code = _get_zip(
        appointment.business.zipcode,
        business_region.type if business_region else None,
        business_region.name if business_region else None,
    )
    geo_information = _get_geo_information(appointment.business, False)
    common_track_properties = {
        # 'currency':'' , #TODO
        'business_postal_code': zip_code,
        'booked_from': subbookings_dct[0]['booked_from'].isoformat(),
        'appointment_type': (
            AppointmentTypeEnum.MULTIBOOKING
            if appointment.is_multibooking()
            else AppointmentTypeEnum.SINGLE
        ),
        'appointment_id': id_to_external_api(appointment_id),
        'customer_id': id_to_external_api(appointment.booked_for.user_id),
        'is_xCB': appointment.is_crossing(),
        'is_xCB_xCategory': appointment.is_crossing_and_cross_category(),
        'energy_booking': subbookings_dct[0].get('is_chargeable_for_analytics'),
        'business_name': subbookings_dct[0].get('appointment__business__name'),
        'business_primary_category': subbookings_dct[0].get(
            'appointment__business__primary_category__internal_name'
        ),
        'family_and_friends_role': appointment.family_and_friends_role,
        'client_from_invite': subbookings_dct[0].get('appointment__booked_for__invited', False),
        **_get_cb_common_properties_optimized(subbookings_dct, appointment.business),
        **geo_information,
    }
    analytics.track('CB_Created_For_Business', common_track_properties)

    analytics.identify(
        {
            'user_role': UserRoleEnum.OWNER.value,
            'email': appointment.business.owner.email,
            'business_CB_count': cb_count,
        }
    )

    if not BranchIOCustomerBookingTrackingFlag():
        dct_annoted = (
            Appointment.objects.filter(
                id=appointment_id,
            )
            .annotate(
                is_10th_cb=Case(
                    When(
                        type=Appointment.TYPE.CUSTOMER,
                        then=Appointment.objects.filter(
                            business_id=appointment.business_id,
                        ).count()
                        == 10,  # so this would be 10th
                    ),
                    default=None,
                    output_field=BooleanField(),
                ),
            )
            .values(
                'is_10th_cb',
            )
            .first()
        )

        if appointment.is_first_cb_for_business():
            send_analytics_1st_cb_created_for_business(
                analytics=analytics,
                common_track_properties=common_track_properties,
                appointment=appointment,
            )
        if dct_annoted['is_10th_cb']:
            analytics.track(
                '10th_CB_Achieved',
                {
                    'email': appointment.business.owner.email,
                    'business_id': id_to_external_api(appointment.business_id),
                },
            )


@branchio_analytics_retry_post_transaction_task
def analytics_cb_created_count_for_business_branchio_task(
    analytics,
    appointment_id=None,
) -> None:
    from webapps.booking.models import Appointment, SubBooking

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
            deleted__isnull=True,
        )
        .select_related(
            'booked_for',
            'business',
            'business__region',
            'business__owner',
            'business__primary_category',
        )
        .annotate_family_and_friends_role()
        .first()
    )
    if not appointment:
        return

    match Appointment.objects.filter(
        type=Appointment.TYPE.CUSTOMER,
        business_id=appointment.business_id,
        created__lte=appointment.created,
    ).count():
        case 1:
            common_track_properties = _get_common_properties(appointment)

            analytics.track('1st_CB_Created_For_Business', common_track_properties)

        case 5:
            analytics.track(
                '5th_CB_Achieved',
                {
                    'email': appointment.business.owner.email,
                    'business_id': id_to_external_api(appointment.business_id),
                },
            )
        case 10:
            analytics.track(
                '10th_CB_Achieved',
                {
                    'email': appointment.business.owner.email,
                    'business_id': id_to_external_api(appointment.business_id),
                },
            )


@gtm_analytics_retry_post_transaction_task
def analytics_cb_created_count_in_days_for_business_gtm_task(
    analytics,
    **kwargs,
):
    analytics_cb_created_count_in_days_for_business_task(
        analytics.push, include_request_params=True, **kwargs
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_created_count_in_days_for_business_segment_task(analytics, **kwargs):
    analytics_cb_created_count_in_days_for_business_task(analytics.track, **kwargs)


def analytics_cb_created_count_in_days_for_business_task(
    analytics_func,
    appointment_id=None,
    appointment_cnt=5,
    event_name=AnalyticEventEnums.FIFTH_CB_IN_FOURTEEN_DAYS,
    days_from_business_active=14,
    include_request_params=False,
) -> None:
    from webapps.booking.models import Appointment

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
            deleted__isnull=True,
        )
        .select_related(
            'business',
            'business__owner',
        )
        .first()
    )
    if not appointment:
        return

    start_date = appointment.business.active_from or appointment.business.created
    end_date = start_date + datetime.timedelta(days_from_business_active)
    if appointment.created > end_date:
        return
    if (
        Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            business_id=appointment.business_id,
            created__lte=appointment.created,
        ).count()
        == appointment_cnt
    ):
        kwargs = {}
        if include_request_params:
            kwargs['request_params'] = {
                'user_id': id_to_external_api(appointment.business.owner.id),
            }
        analytics_func(
            event_name,
            {
                'country': settings.API_COUNTRY,
                'email': appointment.business.owner.email,
                'business_id': id_to_external_api(appointment.business_id),
                'user_id': id_to_external_api(appointment.business.owner.id),
            },
            **kwargs,
        )


@post_transaction_task
def send_analytics_1st_cb_created_for_business_to_facebook(appointment_id=None):
    from lib.facebook.service import FacebookEventService
    from webapps.booking.models import Appointment

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
            deleted__isnull=True,
        )
        .select_related(
            'booked_for',
            'business',
            'business__region',
            'business__owner',
            'business__primary_category',
        )
        .annotate_family_and_friends_role()
        .first()
    )
    if not appointment:
        return

    appointment_query = Appointment.objects.filter(
        type=Appointment.TYPE.CUSTOMER,
        business_id=appointment.business_id,
    ).count()

    match appointment_query:
        case 1:
            common_track_properties = _get_common_properties(appointment)
            user_id = appointment.booked_for.user_id
            user_email = appointment.booked_for.user.email if user_id is not None else None
            email = user_email or appointment.customer_email

            common_track_properties['email'] = email
            common_track_properties['booking_score'] = float(
                common_track_properties['booking_score']
            )
            common_track_properties['service_price'] = [
                float(s) for s in common_track_properties.get('service_price', [])
            ]

            user_data = None
            if user_id:
                user_data = get_user_properties(
                    user_id, include_user_role=False, include_locale=False
                )
                user_data['external_id'] = user_id
                user_data['phone'] = user_data.pop('cell_phone')
                user_data['gender'] = convert_booksy_gender_to_facebook_gender(
                    user_data.pop('gender')
                )

            _logger.info(f"Sending common_parameters: {common_track_properties}")
            _logger.info(f"Sending user_data: {user_data}")
            FacebookEventService.send_event_with_custom_data(
                data=common_track_properties,
                event_name=EventName.FIRST_CB_CREATED_FOR_BUSINESS,
                event_time=int(time()),
                user_data=user_data,
            )
        case 5:
            pass  # SOON
        case 10:
            pass  # SOON


@segment_analytics_retry_post_transaction_task
def analytics_cb_finished_for_business_task(
    analytics,
    appointment_id,
    is_first_finished,
) -> None:
    from webapps.booking.models import Appointment, SubBooking

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
        )
        .select_related(
            'business',
            'business__owner',
            'booked_for',
        )
        .annotate_family_and_friends_role()
        .first()
    )

    booking_dct = (
        SubBooking.objects.filter(
            deleted__isnull=True,
            appointment_id=appointment_id,
        )
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
        )
        .values(
            'id',
            'booked_from',
            'service_variant__service_id',
            'service_variant__service__name',
            'service_variant__service__service_category__name',
            'service_variant__service__service_category_id',
            'booking_value',
            'appointment__business__primary_category__internal_name',
            'appointment__booked_for',
            'appointment__business_id',
            'appointment__customer_email',
            'appointment__business__region__name',
            'appointment__business__region',
            'appointment__business__name',
            'appointment__source__name',
            'appointment__business__primary_category__internal_name',
            'appointment__booked_for_id',
        )
        .all()
    )

    business = appointment.business
    common_track_properties = {
        'business_name': business.name,
        'email': business.owner.email,
        'appointment_id': id_to_external_api(appointment_id),
        'customer_id': id_to_external_api(sget(appointment, ['booked_for', 'user_id'])),
        'booked_from': booking_dct[0].get('booked_from').isoformat(),
        'energy_booking': appointment.get_chargeable_for_analytics(),
        'business_primary_category': booking_dct[0].get(
            'appointment__business__primary_category__internal_name'
        ),
        'family_and_friends_role': appointment.family_and_friends_role,
        **_get_cb_common_properties_optimized(booking_dct, appointment.business),
        **_get_geo_information(business, False),
    }
    analytics.track('CB_Finished_For_Business', common_track_properties)

    year_ago = year_ago_from_tznow()
    analytics.identify(
        {
            'user_role': UserRoleEnum.OWNER.value,
            'email': business.owner.email,
            'business_CB_finished_count': business.appointments.filter(
                type=Appointment.TYPE.CUSTOMER,
            ).count(),
            'business_CB_finished_12m_count': business.appointments.filter(
                type=Appointment.TYPE.CUSTOMER,
                booked_from__gte=year_ago,
            ).count(),
        }
    )
    if is_first_finished:
        send_analytics_1st_cb_finished_for_business(
            analytics,
            common_track_properties=common_track_properties,
            business=business,
        )


def send_analytics_1st_cb_finished_for_business(
    analytics,
    common_track_properties,
    business,
):
    common_track_properties.pop('is_chargable', None)
    analytics.track('1st_CB_Finished_For_Business', common_track_properties)
    analytics.identify(
        {
            'user_role': get_user_role_by_id(business.owner_id),
            'email': business.owner.email,
            'business_1st_CB_finished': True,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_finished_for_customer_task(
    analytics,
    appointment_id,
    is_first_finished,
) -> None:
    from webapps.booking.models import Appointment, Business, SubBooking
    from webapps.family_and_friends.enums import FamilyAndFriendsRoleEnum
    from webapps.family_and_friends.helpers.appointment import family_and_friends_role_case_expr

    subbooking_qs = (
        SubBooking.objects.filter(
            deleted__isnull=True,
            appointment_id=appointment_id,
            appointment__type=Appointment.TYPE.CUSTOMER,
            appointment__status=AppointmentStatusChoices.FINISHED,
        )
        .annotate_is_chargeable_for_analytics()
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
            family_and_friends_role=family_and_friends_role_case_expr(prefix_path='appointment__'),
        )
    )

    booking_dct = subbooking_qs.values(
        'id',
        'booked_from',
        'service_variant__service_id',
        'service_variant__service__name',
        'service_variant__service__service_category__name',
        'service_variant__service__service_category_id',
        'booking_value',
        'appointment__business__primary_category__internal_name',
        'appointment__booked_for',
        'appointment__business_id',
        'appointment__customer_email',
        'appointment__business__region__name',
        'appointment__business__region',
        'appointment__business__name',
        'appointment__source__name',
        'appointment__business__primary_category__internal_name',
        'appointment__booked_for_id',
        'appointment__booked_for__user_id',
        'appointment__booked_for__user__email',
        'appointment__booked_for__client_type',
        'is_chargeable_for_analytics',
        'family_and_friends_role',
    ).all()

    if not booking_dct:
        return

    business = Business.objects.filter(id=booking_dct[0].get('appointment__business_id')).first()

    booked_for_id = booking_dct[0].get('appointment__booked_for_id')
    customer_email = booking_dct[0].get('appointment__customer_email')
    business_postal_code = booking_dct[0].get('appointment__business__region__name')
    family_and_friends_role = booking_dct[0].get('family_and_friends_role')

    common_track_properties = {
        'email': customer_email,
        'business_id': id_to_external_api(booking_dct[0].get('appointment__business_id')),
        'business_postal_code': business_postal_code,
        'business_name': booking_dct[0].get('appointment__business__name'),
        'appointment_id': id_to_external_api(appointment_id),
        'customer_id': id_to_external_api(booking_dct[0].get('appointment__booked_for__user_id')),
        'booked_from': booking_dct[0].get('booked_from').isoformat(),
        'energy_booking': booking_dct[0].get('is_chargeable_for_analytics'),
        'business_primary_category': booking_dct[0].get(
            'appointment__business__primary_category__internal_name'
        ),
        'family_and_friends_role': booking_dct[0].get('family_and_friends_role'),
        **_get_cb_common_properties_optimized(booking_dct, business),
        **_get_geo_information(business, False),
    }
    analytics.track('CB_Finished_For_Customer', common_track_properties)

    if not booked_for_id:
        return

    if family_and_friends_role == FamilyAndFriendsRoleEnum.CREATOR:
        user_id = analytics.context.session_user_id
    else:
        user_id = booking_dct[0].get('appointment__booked_for__user_id')

    cb_finished_count, cb_finished_12m_count = _get_cb_finished_counters(booked_for_id, user_id)
    last_booking_finished_category = booking_dct[0].get(
        'appointment__business__primary_category__internal_name'
    )
    analytics.identify(
        {
            'email': customer_email,
            'CB_finished_count': cb_finished_count,
            'CB_finished_12m_count': cb_finished_count,
            'user_role': get_user_role_by_id(user_id),
            'last_booking_finished_category': last_booking_finished_category,
            'last_CB_finished_urban_area': common_track_properties['urban_area'],
            **_get_cb_counters(user_id, booked_for_id),
        }
    )
    if is_first_finished:
        send_analytics_1st_cb_finished_for_customer(
            analytics,
            common_track_properties,
            booking_dct,
        )


def _get_cb_finished_counters(booked_for_id, user_id):
    from webapps.booking.models import Appointment
    from webapps.family_and_friends.models import MemberAppointment

    year_ago = year_ago_from_tznow()
    if user_id:
        users_bookings = Appointment.objects.filter(
            booked_for__user_id=user_id,
            status=Appointment.STATUS.FINISHED,
        )
        users_bookings_count = users_bookings.count()
        member_bookings = MemberAppointment.objects.filter(
            booked_by__user=user_id,
            booked_for__user__isnull=True,
            appointment__status=Appointment.STATUS.FINISHED,
        )
        users_bookings_count += member_bookings.count()
        users_bookings_12m_count = users_bookings.filter(
            booked_from__gte=year_ago,
        ).count()
        users_bookings_12m_count += member_bookings.filter(
            appointment__booked_from__gte=year_ago,
        ).count()
    else:
        users_bookings = Appointment.objects.filter(
            booked_for_id=booked_for_id,
            status=Appointment.STATUS.FINISHED,
        )
        users_bookings_count = users_bookings.count()
        users_bookings_12m_count = users_bookings.filter(
            booked_from__gte=year_ago,
        ).count()
    return users_bookings_count, users_bookings_12m_count


def send_analytics_1st_cb_finished_for_customer(
    analytics,
    common_track_properties,
    subbookings,
):
    if not subbookings:
        return
    analytics.track('1st_CB_Finished_For_Customer', common_track_properties)
    user_id = subbookings[0].get('appointment__booked_for__user_id')
    email = subbookings[0].get('appointment__booked_for__user__email')
    if not user_id:
        return
    analytics.identify(
        {
            'email': email,
            '1st_CB_finished': True,
            'user_role': get_user_role_by_id(user_id),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_contact_preferences_updated_task(
    analytics,
    business_id,
) -> None:
    from webapps.business.models import Business

    business_data = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'agreement',
            'owner',
        )
        .values(
            'owner__id',
            'owner__email',
            'agreement__marketing_agreement',
            'agreement__receiving_messages_consent',
        )
        .first()
    )

    common_properties = {
        'email': business_data.get('owner__email'),
        'business_receiving_messages_consent': business_data.get(
            'agreement__receiving_messages_consent'
        )
        or False,
    }

    # due to law in US if marketing_agreement in None the field is not sent with event
    bma = business_data.get('agreement__marketing_agreement')
    if not (bma is None and settings.API_COUNTRY == Country.US):
        common_properties['business_marketing_agreement'] = bma or False

    analytics.track('Business_Contact_Preferences_Updated', common_properties)
    analytics.identify(
        {
            'user_role': get_user_role_by_id(business_data.get('owner__id')),
            'country': settings.API_COUNTRY,
            **common_properties,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_contact_preferences_updated_task(
    analytics,
    user_id,
) -> None:
    from webapps.user.models import UnsubscribedEmail, User

    user_data = (
        User.objects.filter(id=user_id)
        .select_related('agreement')
        .values(
            'email',
            'agreement__marketing_agreement',
            'agreement__partner_marketing_agreement',
        )
        .first()
    )

    common_properties = {
        'email': user_data.get('email'),
        'user_unsubscribedemail': UnsubscribedEmail.can_send_email(user_data.get('email')),
        'marketing_agreement': user_data.get('agreement__marketing_agreement') or False,
        'partner_marketing_agreement': user_data.get('agreement__partner_marketing_agreement')
        or False,
    }

    analytics.track('Contact_Preferencess_Updated', common_properties)
    analytics.identify(
        {
            'user_role': get_user_role_by_id(user_id),
            **common_properties,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_customer_info_updated_task(
    analytics,
    user_id,
) -> None:
    common_properties = get_user_properties(user_id)
    common_properties['cell_phone'] = get_prep_value_form(common_properties['cell_phone'])
    common_properties['gender'] = common_properties['gender']
    common_properties['locale'] = common_properties['language']
    analytics.identify(common_properties)


@segment_analytics_retry_post_transaction_task
def analytics_staffer_created_task(
    analytics, business_id, resource_id, services_ids, invited
) -> None:
    from django.db.models import Count, Q

    from webapps.business.models import Business, Resource

    business_data = (
        Business.objects.filter(id=business_id)
        .values(
            'owner__email',
            'owner__id',
        )
        .annotate(
            staffers_count=Count(
                'resources',
                filter=Q(
                    resources__active=True,
                    resources__type=Resource.STAFF,
                ),
            ),
        )
        .order_by('owner_id')
        .first()
    )
    resource_data = (
        Resource.objects.filter(id=resource_id)
        .only(
            'name',
            'staff_cell_phone',
            'staff_email',
            'type',
        )
        .first()
    )
    if (
        not business_data
        or not resource_data
        or resource_data
        and resource_data.type != Resource.STAFF
    ):
        return

    analytics.track(
        'Staffer_Created',
        {
            'email': business_data.get('owner__email'),
            'business_id': id_to_external_api(business_id),
            'resource_id': resource_id,
            'staff_name': resource_data.name,
            'staff_phone': resource_data.staff_cell_phone_with_prefix,
            'staff_email': resource_data.staff_email,
            'has_been_invited': invited,
            'service_id': services_ids,
        },
    )
    analytics.identify(
        {
            'email': business_data.get('owner__email'),
            'user_role': get_user_role_by_id(business_data.get('owner__id')),
            'business_staffers_count': business_data.get('staffers_count'),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_user_language_set(analytics, profile_id) -> None:
    from webapps.user.models import UserProfile

    profile_data = (
        UserProfile.objects.filter(
            id=profile_id,
        )
        .values('user__email', 'language', 'user__id')
        .first()
    )
    if not profile_data:
        return

    analytics.identify(
        {
            'locale': profile_data.get('language'),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_customer_user_language_set(analytics, profile_id) -> None:
    from webapps.user.models import UserProfile

    profile_data = (
        UserProfile.objects.filter(
            id=profile_id,
        )
        .values('user__email', 'language', 'user__id')
        .first()
    )
    user_role = get_user_role_by_id(profile_data.get('user__id'))
    if not profile_data or user_role != UserRoleEnum.CUSTOMER:
        return
    analytics.identify(
        {
            'email': profile_data.get('user__email'),
            'locale': profile_data.get('language'),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_kyc_failure_task(
    analytics, account_holder_id, failure_code, failure_message
):
    from webapps.market_pay.models import AccountHolder

    account_holder_data = (
        AccountHolder.objects.filter(
            id=account_holder_id,
        )
        .values(
            'payout_allowed',
            'pos__business__owner__email',
            'pos__business_id',
            'pos__business__owner',
        )
        .first()
        or {}
    )
    business_id = account_holder_data.get('pos__business_id')
    owner_email = account_holder_data.get('pos__business__owner__email')
    if not all([business_id, owner_email]):
        return
    analytics.track(
        'Business_KYC_Failure',
        {
            'userId': account_holder_data.get('pos__business__owner'),
            'user_role': get_user_role_by_id(account_holder_data.get('pos__business__owner')),
            'business_kyc_status': KYCStatus.INACTIVE.value,  # constant in docs
            'email': owner_email,
            'business_id': id_to_external_api(business_id),
            'KYC_Failure_Code': failure_code,
            'KYC_Failure_Message': failure_message,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_1st_paid_status_achieved(analytics, business_id) -> None:
    from webapps.business.models import Business
    from webapps.purchase.models import Subscription

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'owner',
        )
        .first()
    )
    if business.status != Business.Status.PAID:
        return

    if business.has_new_billing:
        subscription_payment_method = Business.PaymentSource.BRAINTREE_BILLING.label
    else:
        subscription_data = (
            Subscription.objects.filter(
                business=business,
            )
            .values('source')
            .first()
        )
        if not subscription_data:
            return
        subscription_payment_method = Business.PaymentSource(subscription_data.get('source')).label

    analytics.track(
        '1st_Paid_Status_Achieved',
        {
            'email': business.owner.email,
            'business_id': id_to_external_api(business_id),
            'business_admin_status': Business.Status.PAID.name,
            'subscription_payment_method': subscription_payment_method,
        },
    )
    analytics.identify(
        {
            'email': business.owner.email,
            'user_role': UserRoleEnum.OWNER.value,
            'business_admin_status': Business.Status.PAID.name,
            'business_subscription_payment_method': subscription_payment_method,
        }
    )


@branchio_analytics_retry_post_transaction_task
def analytics_1st_paid_status_achieved_branchio_task(analytics, business_id) -> None:
    from webapps.business.models import Business
    from webapps.purchase.models import Subscription

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'owner',
        )
        .first()
    )
    if business.status != Business.Status.PAID:
        return

    if business.has_new_billing:
        subscription_payment_method = Business.PaymentSource.BRAINTREE_BILLING.label
    else:
        subscription_data = (
            Subscription.objects.filter(
                business=business,
            )
            .values('source')
            .first()
        )
        if not subscription_data:
            return
        subscription_payment_method = Business.PaymentSource(subscription_data.get('source')).label

    analytics.track(
        '1st_Paid_Status_Achieved',
        {
            'email': business.owner.email,
            'business_id': id_to_external_api(business_id),
            'business_admin_status': Business.Status.PAID.name,
            'subscription_payment_method': subscription_payment_method,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_subscription_payment_succeeded_task(
    analytics,
    transaction_id: int,
) -> None:
    from webapps.billing.enums import PaymentProcessorType, TransactionSource, TransactionStatus
    from webapps.billing.models.payment.transaction import BillingTransaction
    from webapps.business.models import Business

    subscription_data = (
        BillingTransaction.objects.filter(id=transaction_id)
        .values(
            'business__name',
            'business__owner_id',
            'business__status',
            'payment_processor',
            'amount',
            'currency',
            'subscription__date_start',
            'subscription_id',
        )
        .get()
    )

    if subscription_data.get('subscription_id') is None:
        return

    transactions_count = BillingTransaction.objects.filter(
        subscription_id=subscription_data.get('subscription_id'),
        status=TransactionStatus.CHARGED,
        transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
    ).count()

    analytics.track(
        'Subscription_Payment_Succeed',
        {
            'business_name': subscription_data.get('business__name'),
            'business_admin_status': Business.Status.PAID.name,
            'paid_from': subscription_data.get('subscription__date_start').strftime("%d.%m.%Y"),
            'subscription_payment_method': PaymentProcessorType.get_label(
                subscription_data.get('payment_processor')
            ),
            'is_first_payment': True if transactions_count == 1 else False,
        },
    )
    analytics.identify(
        {
            'subscription_billing_amount': str(subscription_data.get('amount')),
            'subscription_billing_currency': subscription_data.get('currency'),
            'user_role': get_user_role_by_id(subscription_data.get('business__owner_id')),
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_1st_paid_status_achieved_gtm_task(
    analytics,
    business_id,
) -> None:
    from webapps.business.models import Business
    from webapps.marketing.models import DelayedGTMEventAuthData
    from webapps.purchase.models import Subscription

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related('owner', 'primary_category')
        .first()
    )
    if not business:
        _analytics_1st_paid_status_achieved_logger.warning(
            ('Business with id: %s does not exists', business_id)
        )
        return
    if business.status != Business.Status.PAID:
        _analytics_1st_paid_status_achieved_logger.warning(
            ('Business with id: %s status %s is different then PAID', business_id, business.status)
        )
        return
    if business.has_new_billing:
        subscription_payment_method = Business.PaymentSource.BRAINTREE_BILLING.label
    else:
        subscription_data = (
            Subscription.objects.filter(
                business=business,
            )
            .values('source')
            .first()
        )
        if not subscription_data:
            _analytics_1st_paid_status_achieved_logger.warning(
                ('Business with id: %s has no subscription_data', business_id)
            )
            return
        subscription_payment_method = Business.PaymentSource(subscription_data.get('source')).label
    if business.primary_category:
        primary_category_internal_name = business.primary_category.internal_name
    else:
        primary_category_internal_name = ''
    event_data = {
        'country': settings.API_COUNTRY,
        'email': business.owner.email,
        'business_id': id_to_external_api(business_id),
        'business_admin_status': Business.Status.PAID.name,
        'business_subscription_payment_method': subscription_payment_method,
        'business_primary_category': primary_category_internal_name,
    }
    if gtm_auth_data := DelayedGTMEventAuthData.objects.filter(
        event_type=DelayedGTMEventAuthData.EventType.FIRST_PAID_STATUS_ACHIEVED,
        business_id=business_id,
        event_sent=False,
    ).first():
        _analytics_1st_paid_status_achieved_logger.warning(
            ('Business with id: %s , gtm_auth_data: %s', business_id, gtm_auth_data.auth_data)
        )
    else:
        _analytics_1st_paid_status_achieved_logger.warning(
            ('Business with id: %s , has no DelayedGTMEventAuthData', business_id)
        )
    analytics.push(
        event_name='1st_Paid_Status_Achieved',
        event_data=event_data,
        request_params={
            'user_id': id_to_external_api(business.owner.id),
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_pba_enabled_task(analytics, business_id) -> None:
    from webapps.pos.models import POS

    pos = (
        POS.objects.filter(
            business_id=business_id,
        )
        .select_related(
            'business',
        )
        .first()
    )
    if pos.pay_by_app_status != POS.PAY_BY_APP_ENABLED:
        return

    business = pos.business
    common_properties = {
        'email': business.owner.email,
        'user_role': get_user_role_by_id(business.owner.id),
        'user_id': id_to_external_api(business.owner.id),
        'business_id': id_to_external_api(business.id),
        'business_name': business.name,
        'pay_by_app_status': pos.pay_by_app_status,
    }
    analytics.track('Business_PBA_Enabled', common_properties)
    analytics.identify(common_properties)


@segment_analytics_retry_post_transaction_task
def analytics_review_completed_task(analytics, review_id) -> None:
    from webapps.reviews.models import Review

    review = (
        Review.objects.filter(id=review_id)
        .values(
            'business',
            'business__owner',
            'business__owner__email',
            'user',
            'user__email',
            'rank',
            'business__reviews_count',
            'business__reviews_rank_avg',
        )
        .annotate(number_of_photos=Count('review_photos'))
        .first()
    )
    if not review:
        return

    analytics.track(
        'Review_Completed',
        {
            'email': review['user__email'],
            'business_id': id_to_external_api(review['business']),
            'user_id': id_to_external_api(review['user']),
            'rating': review['rank'],
            'num_photos_uploaded': review['number_of_photos'],
        },
    )
    analytics.identify(
        {
            'email': review['business__owner__email'],
            'user_role': get_user_role_by_id(review['business__owner']),
            'business_reviews_count': review['business__reviews_count'],
            'business_reviews_rating': review['business__reviews_rank_avg'],
        }
    )


@branchio_analytics_retry_post_transaction_task
def analytics_energy_cb_created_for_customer_branchio_task(analytics, appointment_id=None) -> None:
    from webapps.booking.models import Appointment, SubBooking
    from webapps.family_and_friends.enums import FamilyAndFriendsRoleEnum

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
        )
        .select_related(
            'booked_for',
            'booked_for__user',
            'business',
        )
        .annotate_family_and_friends_role()
        .first()
    )
    if not appointment or not appointment.get_chargeable_for_analytics():
        return

    booked_for_id = appointment.booked_for_id
    if not booked_for_id:
        return

    user_id = appointment.booked_for.user_id
    user_email = appointment.booked_for.user.email if user_id is not None else None

    if appointment.family_and_friends_role == FamilyAndFriendsRoleEnum.CREATOR:
        user_id = analytics.context.session_user_id

    subbookings = SubBooking.objects.filter(
        appointment_id=appointment_id,
        deleted__isnull=True,
    ).values(
        'id',
        'appointment__business_id',
    )

    track_parameters = {
        'appointment_id': id_to_external_api(appointment_id),
        'appointment_type': (
            AppointmentTypeEnum.MULTIBOOKING
            if appointment.is_multibooking()
            else AppointmentTypeEnum.SINGLE
        ),
        'booking_id': id_to_external_api(subbookings[0].get('id')),
        'business_id': id_to_external_api(subbookings[0].get('appointment__business_id')),
        'country': settings.API_COUNTRY,
        'device_type': get_device_type_name_from_api_key(analytics.context.source.api_key),
        'email': user_email or appointment.customer_email,
        'user_id': id_to_external_api(user_id),
    }

    analytics.track(
        'Energy_CB_Created_For_Customer',
        track_parameters,
    )

    if RecreateBackendAppsflyerEventsInFacebookConversionApi():
        send_analytics_energy_cb_created_for_customer_to_facebook(
            data=track_parameters, event_time=int(time())
        )


@post_transaction_task
def send_analytics_energy_cb_created_for_customer_to_facebook(data: dict, event_time: int):
    from lib.facebook.service import FacebookEventService

    FacebookEventService.send_event_with_custom_data(
        event_name=EventName.ENERGY_CB_CREATED_FOR_CUSTOMER, data=data, event_time=event_time
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_created_for_customer_task(
    analytics,
    appointment_id=None,
) -> None:
    from webapps.booking.models import Appointment, SubBooking
    from webapps.family_and_friends.enums import FamilyAndFriendsRoleEnum
    from webapps.family_and_friends.models import MemberAppointment

    appointment = (
        Appointment.objects.filter(
            id=appointment_id,
            type=Appointment.TYPE.CUSTOMER,
        )
        .select_related(
            'booked_for',
            'booked_for__user',
            'business',
            'business__region',
        )
        .annotate_family_and_friends_role()
        .first()
    )
    if not appointment:
        return

    booked_for_id = appointment.booked_for_id
    if not booked_for_id:
        return

    user_id = appointment.booked_for.user_id
    user_email = appointment.booked_for.user.email if user_id is not None else None

    if appointment.family_and_friends_role == FamilyAndFriendsRoleEnum.CREATOR:
        user_id = analytics.context.session_user_id

    subbookings = (
        SubBooking.objects.filter(
            appointment_id=appointment_id,
            deleted__isnull=True,
        )
        .annotate_staffer_id()
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
        )
        .values(
            'id',
            'booked_from',
            'chargeable',
            'staffer_id',
            'appointment_id',
            'appointment__booked_for__client_type',
            'appointment__booked_for_id',
            'appointment__business__name',
            'appointment__business__primary_category__internal_name',
            'appointment__business_id',
            'appointment__customer_email',
            'appointment__source__chargeable',
            'appointment__source',
            'appointment__status',
            'appointment__type',
            'service_variant__service_id',
            'service_variant__service__name',
            'service_variant__service__service_category__name',
            'service_variant__service__service_category_id',
            'booking_value',
            'appointment__booked_for',
            'appointment__business__region__name',
            'appointment__business__region',
            'appointment__source__name',
        )
    )
    business_region = appointment.business.region
    zip_code = _get_zip(
        appointment.business.zipcode,
        business_region.type if business_region else None,
        business_region.name if business_region else None,
    )

    cb_counters = _get_cb_counters(user_id)
    user_role = get_user_role_by_id(user_id)
    if user_id:
        cb_count = Appointment.objects.filter(booked_for__user_id=user_id).count()
        cb_count += MemberAppointment.objects.filter(
            booked_by__user=user_id,
            booked_for__user__isnull=True,
        ).count()
    else:
        cb_count = Appointment.objects.filter(booked_for_id=appointment.booked_for_id).count()
    track_common_properties = {
        'business_id': id_to_external_api(subbookings[0].get('appointment__business_id')),
        'email': appointment.customer_email,
        'business_postal_code': zip_code,
        'business_name': subbookings[0].get('appointment__business__name'),
        'appointment_id': id_to_external_api(appointment_id),
        'booked_from': subbookings[0].get('booked_from').isoformat(),
        'source': analytics.context.source.name,
        'energy_booking': appointment.get_chargeable_for_analytics(),
        'business_primary_category': subbookings[0].get(
            'appointment__business__primary_category__internal_name'
        ),
        'last_booking_longitude': appointment.business.longitude,
        'last_booking_latitude': appointment.business.latitude,
        'family_and_friends_role': appointment.family_and_friends_role,
        **_get_cb_common_properties_optimized(subbookings, appointment.business),
    }

    track_cb_created_for_customer = {
        'appointment_type': (
            AppointmentTypeEnum.MULTIBOOKING
            if appointment.is_multibooking()
            else AppointmentTypeEnum.SINGLE
        ),
        'currency': '',
        **track_common_properties,
        **_get_geo_information(appointment.business, False),
    }
    analytics.track(
        'CB_Created_For_Customer',
        {
            'is_xCB': appointment.is_crossing(),
            'is_xCB_xCategory': (appointment.is_crossing_and_cross_category()),
            **track_cb_created_for_customer,
        },
    )
    email = user_email or appointment.customer_email
    analytics.identify(
        {
            'user_role': user_role,
            'email': email,
            'CB_count': cb_count,
            'last_booking_created_category': track_common_properties['category_name'],
            'last_booking_longitude': appointment.business.longitude,
            'last_booking_latitude': appointment.business.latitude,
            **cb_counters,
        }
    )

    if appointment.is_first_cb_for_customer() and user_id:  # 1st_CB_Created_For_Customer
        send_analytics_1st_cb_created_for_customer(
            analytics=analytics,
            track_common_properties=track_common_properties,
            email=email,
            user_role=user_role,
        )


@segment_analytics_retry_post_transaction_task
def analytics_boost_on_off_task(analytics):
    from webapps.business.models import Business

    business = analytics.context.business
    if not business:
        return
    boost_status = (
        BoostStatusEnum.ACTIVE
        if business.boost_status in Business.BoostStatus.active_statuses()
        else BoostStatusEnum.INACTIVE
    )
    owner_email = business.owner.email
    analytics.track(
        'Boost_On_Off',
        {
            'email': owner_email,
            'business_id': id_to_external_api(business.id),
            'business_boost_status': boost_status.name,
        },
    )
    analytics.identify(
        {
            'email': owner_email,
            'user_role': get_user_role_by_id(business.owner.id),
            'business_boost_status': boost_status.name,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_boost_on_off_gtm_task(analytics):
    from webapps.business.models import Business

    business = analytics.context.business
    booking_source = analytics.context.source
    if not business:
        return
    api_key = None
    if booking_source:
        api_key = booking_source.api_key
    boost_status = (
        BoostStatusEnum.ACTIVE
        if business.boost_status in Business.BoostStatus.active_statuses()
        else BoostStatusEnum.INACTIVE
    )
    event_data = {
        'email': business.owner.email,
        'business_id': id_to_external_api(business.id),
        'business_boost_status': boost_status.name,
        'device_type': get_device_type_name_from_api_key(api_key),
    }
    analytics.push(
        event_name='Boost_On_Off',
        event_data=event_data,
        request_params={
            'user_id': id_to_external_api(business.owner.id),
        },
    )


@branchio_analytics_retry_post_transaction_task
def analytics_boost_on_branchio_task(analytics):
    business = analytics.context.business
    if not business:
        return
    owner_email = business.owner.email
    analytics.track(
        'Boost_On',
        {
            'email': owner_email,
            'business_id': id_to_external_api(business.id),
            'business_boost_status': BoostStatusEnum.ACTIVE.name,
        },
    )


@branchio_analytics_retry_post_transaction_task
def analytics_boost_off_branchio_task(analytics):
    business = analytics.context.business
    if not business:
        return
    owner_email = business.owner.email
    analytics.track(
        'Boost_Off',
        {
            'email': owner_email,
            'business_id': id_to_external_api(business.id),
            'business_boost_status': BoostStatusEnum.INACTIVE.name,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_pos_updated_task(analytics, business_id):
    from webapps.business.models import Business

    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'pos',
            'owner',
        )
        .first()
    )
    if not business or not business.pos:
        return
    pos = business.pos
    owner_email = business.owner.email
    donations_enabled = pos.donations_enabled
    pay_by_app_status = pos.pay_by_app_status
    business_name = business.name
    analytics.track(
        'Business_POS_Updated',
        {
            'email': business.owner.email,
            'id': id_to_external_api(business_id),
            'name': business_name,
            'pay_by_app_status': pay_by_app_status,
            'donations_enabled': donations_enabled,
        },
    )
    analytics.identify(
        {
            'email': owner_email,
            'country': settings.API_COUNTRY,
            'user_role': UserRoleEnum.OWNER.value,
            'business_id': id_to_external_api(business_id),
            'business_name': business_name,
            'pay_by_app_status': pay_by_app_status,
            'donations_enabled': donations_enabled,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_subscription_updated_task(
    analytics,
    business_id,
):
    from webapps.business.models import Business
    from webapps.business.utils import business_paid_from
    from webapps.purchase.models import Subscription

    business = Business.objects.filter(id=business_id).select_related('owner').first()
    if not business:
        return

    business_owner = business.owner
    owner_email = business_owner.email
    business_name = business.name
    admin_status = Business.Status(business.status).name
    paid_from_date_isoformat = None
    if business.has_new_billing:
        subscription_payment_method = Business.PaymentSource.BRAINTREE_BILLING.label
        plan_name = subscription_payment_method
    else:
        subscription_data = (
            Subscription.objects.filter(
                business=business,
            )
            .values('product__name', 'source')
            .first()
        )
        if not subscription_data:
            return
        plan_name = subscription_data.get('product__name')
        subscription_payment_method = Business.PaymentSource(subscription_data.get('source')).label

    if business.boosted:
        marketplace_promotion_status = BoostStatusEnum.ACTIVE.value
    else:
        marketplace_promotion_status = BoostStatusEnum.INACTIVE.value

    paid_from_datetime = business_paid_from(business)
    if paid_from_datetime:
        paid_from_date_isoformat = paid_from_datetime.isoformat()[:10]

    analytics.track(
        'Business_Subscription_Updated',
        {
            'email': owner_email,
            'business_id': id_to_external_api(business_id),
            'business_name': business.name,
            'paid_from': paid_from_date_isoformat,
            'business_admin_status': admin_status,
            'plan': plan_name,
            'business_boost_status': marketplace_promotion_status,
            'subscription_payment_method': subscription_payment_method,
        },
    )
    analytics.identify(
        {
            'user_role': get_user_role_by_id(business_owner.id),
            'email': owner_email,
            'business_boost_status': marketplace_promotion_status,
            'business_owner_email': owner_email,
            'business_id': id_to_external_api(business_id),
            'business_name': business_name,
            'business_paid_from': paid_from_date_isoformat,
            'business_admin_status': admin_status,
            'business_plan': plan_name,
            'business_subscription_payment_method': subscription_payment_method,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_gtm_onboarding_business_go_live_task(analytics, business_id, was_delay_set):
    from webapps.business.models import Business

    business_data = (
        Business.objects.filter(id=business_id)
        .values(
            'owner__id',
            'owner__email',
        )
        .first()
    )

    if not business_data:
        return

    owner_id = business_data['owner__id']

    event_data = {
        'user_id': id_to_external_api(owner_id),
        'country': settings.API_COUNTRY,
        'email': business_data['owner__email'],
        'business_id': id_to_external_api(business_id),
        'was_delay_set': was_delay_set,
    }
    analytics.push(
        event_name='Onboarding_Go_Live',
        event_data=event_data,
        request_params={
            'user_id': id_to_external_api(owner_id),
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_import_completed_task(
    analytics,
    business_id,
):
    from django.db.models.query_utils import Q

    from webapps.booking.models import Appointment
    from webapps.business.models import Business
    from webapps.business.utils import business_paid_from
    from webapps.purchase.models import Subscription

    year_ago = year_ago_from_tznow()
    business = (
        Business.objects.filter(
            id=business_id,
        )
        .select_related(
            'owner',
            'region',
            'primary_category',
        )
        .prefetch_related(
            'categories',
        )
        .annotate(
            cb_count=Count(
                'appointments',
                filter=Q(
                    appointments__type=Appointment.TYPE.CUSTOMER,
                ),
            ),
            cb_finished_count=Count(
                'appointments',
                filter=Q(
                    appointments__type=Appointment.TYPE.CUSTOMER,
                    appointments__status=Appointment.STATUS.FINISHED,
                ),
            ),
            cb_finished_12m_count=Count(
                'appointments',
                filter=Q(
                    appointments__type=Appointment.TYPE.CUSTOMER,
                    appointments__status=Appointment.STATUS.FINISHED,
                    appointments__booked_from__gte=year_ago,
                ),
            ),
            bb_count=Count(
                'appointments',
                filter=Q(
                    appointments__type=Appointment.TYPE.BUSINESS,
                ),
            ),
        )
        .first()
    )
    if not business:
        return

    boost_status = BoostStatusEnum.ACTIVE if business.boosted else BoostStatusEnum.INACTIVE
    city = business.city_or_region_city
    created_at = business.created.isoformat()[:10]
    owner = business.owner
    owner_email = owner.email
    owner_name = owner.full_name
    business_phone = business.phone_with_prefix
    owner_id = id_to_external_api(business.owner.id)
    paid_from_datetime = business_paid_from(business)
    business_paid_from_isoformat = None
    if paid_from_datetime:
        business_paid_from_isoformat = paid_from_datetime.isoformat()[:10]

    plan_name = None
    subscription_payment_method = None
    if business.has_new_billing:
        subscription_payment_method = Business.PaymentSource.choices_map()[
            Business.PaymentSource.BRAINTREE_BILLING.value
        ]
        plan_name = subscription_payment_method
    else:
        subscription_data = (
            Subscription.objects.filter(
                business=business,
            )
            .values('product__name', 'source')
            .first()
        )
        if subscription_data:
            plan_name = subscription_data.get('product__name')
            subscription_payment_method = Business.PaymentSource.choices_map()[
                subscription_data.get('source')
            ]

    event_data = {
        'business_CB_count': business.cb_count,
        'business_BB_count': business.bb_count,
        'business_1st_CB_finished': bool(business.cb_finished_count),
        'business_CB_finished_12m_count': business.cb_finished_12m_count,
        'business_CB_finished_count': business.cb_finished_count,
        'app_version': BooksyAppVersions.B30.value,
        'business_address': business.get_formatted_address()['address'],
        'business_admin_status': Business.Status(business.status).name,
        'business_boost_status': boost_status,
        'business_boost_status_actual': boost_status,
        'business_categories': list(business.categories.values_list('internal_name', flat=True)),
        'city': city,
        'business_city': city,
        'created_at': created_at,
        'business_created_at': created_at,
        'business_id': id_to_external_api(business_id),
        'business_marketing_agreement': True,
        'business_receiving_messages_consent': True,
        'business_name': business.name,
        'owner_email': owner_email,
        'business_owner_email': owner_email,
        'owner_name': owner_name,
        'business_owner_name': owner_name,
        'business_owner_phone': owner.cell_phone_with_prefix,
        'phone': business_phone,
        'business_phone': business_phone,
        'business_primary_category': _get_business_primary_category(business),
        'business_reviews_count': business.reviews_count or 0,
        'business_reviews_rating': business.reviews_rank_avg or 0,
        'country': settings.API_COUNTRY,
        'email': business.owner.email,
        'offer_type': _get_offer_type_from_package(business.package),
        'user_id': owner_id,
        'userId': owner_id,
        'user_role': UserRoleEnum.OWNER.value,
        'import': business.integrations.get('importer'),
        'business_paid_from': business_paid_from_isoformat,
        'business_plan': plan_name,
        'business_subscription_payment_method': subscription_payment_method,
    }

    # fields we don't send if empty
    zip_code = business.region.name if business.region else None
    if zip_code:
        geo_information = _get_geo_information(business, with_state=True)
        event_data.update(
            {
                'business_zip': zip_code,
                'postalCode': zip_code,
                **geo_information,
            }
        )

    analytics.track('Business_Import_Completed', event_data)
    analytics.identify(event_data)


@segment_analytics_retry_post_transaction_task
def analytics_onboarding_business_go_live_task(analytics, business_id, was_delay_set):
    from webapps.business.models import Business

    business_data = (
        Business.objects.filter(id=business_id)
        .values(
            'owner__id',
            'owner__email',
        )
        .first()
    )

    if not business_data:
        return

    owner_id = business_data['owner__id']

    common_traits = {
        'country': settings.API_COUNTRY,
        'email': business_data['owner__email'],
    }

    analytics.track(
        'Onboarding_Go_Live',
        {
            'business_id': id_to_external_api(business_id),
            'was_delay_set': was_delay_set,
            **common_traits,
        },
    )

    analytics.identify(
        {
            'user_role': get_user_role_by_id(owner_id),
            'business_id': id_to_external_api(business_id),
            'was_delay_set': was_delay_set,
            **common_traits,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_business_app_opened_task(analytics, user_id, booking_source_id, business_id):
    from webapps.booking.models import BookingSources, Business
    from webapps.user.models import User

    user_email = (
        User.objects.filter(
            id=user_id,
        )
        .values_list('email', flat=True)
        .first()
    )
    business_package = (
        Business.objects.filter(
            id=business_id,
        )
        .values('package')
        .first()
    )
    booking_source = BookingSources.objects.filter(id=booking_source_id).first()
    if not user_email or not booking_source or not business_package:
        return
    business_package = business_package['package']
    app_version = BooksyAppVersions.B30.value
    offer_type = _get_offer_type_from_package(business_package)
    common_properties = {
        'app_version': app_version,
        'offer_type': offer_type,
    }
    user_role = get_user_role_by_id(user_id)
    if user_role != UserRoleEnum.CUSTOMER.value:
        common_properties['user_role'] = user_role
        # Because this event is sent in batches once a day or so, there is an edge case where deleted staffer will be assigned as a customer

    analytics.track('Business_App_Opened', common_properties)
    analytics.identify(
        {
            **common_properties,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_customer_app_opened_task(
    analytics, user_id: int, geo_location: Dict[str, float]
) -> None:
    from webapps.user.models import User

    user_email = (
        User.objects.filter(
            id=user_id,
        )
        .values_list('email', flat=True)
        .first()
    )

    if not user_email:
        return

    common_properties = {
        'email': user_email,
    }

    if geo_location:
        common_properties.update(
            {
                'last_known_latitude': geo_location.get('lat'),
                'last_known_longitude': geo_location.get('lon'),
            }
        )

    analytics.push(
        event_name='Customer_App_Opened',
        event_data=common_properties,
        request_params={
            'user_id': id_to_external_api(user_id),
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_invite_all_clicked_task(
    analytics,
    user_id,
    sms_count,
    email_count,
    logged_in_user_id=None,
    page_source=None,
    invite_action=None,
    push_count=None,
):
    if not push_count:
        push_count = 0

    all_invites = sum([sms_count, email_count, push_count])
    analytics.push(
        event_name='Invite_All_Clicked',
        event_data={
            'event_date': tznow().strftime('%Y%m%d'),
            'total_invites_sent': all_invites,
            'sms_invites_sent': sms_count,
            'email_invites_sent': email_count,
            'push_invites_sent': push_count,
            'page_source': page_source,
            'event_action': invite_action,
        },
        request_params={
            'user_id': id_to_external_api(user_id),
        },
        user_properties={
            'logged_in_user_id': id_to_external_api(logged_in_user_id),
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_invite_process_completed_task(
    analytics,
    sms_count,
    email_count,
    postponed,
    page_source=None,
    invite_action=None,
    push_count=None,
):
    if not push_count:
        push_count = 0
    all_invites = sum([sms_count, email_count, push_count])
    analytics.track(
        'Invite_Process_Completed',
        {
            'event_date': tznow().strftime('%Y%m%d'),
            'total_invites_sent': all_invites,
            'postponed': postponed,
            'page_source': page_source,
            'event_action': invite_action,
        },
    )


@branchio_analytics_retry_post_transaction_task
def analytics_invite_process_completed_branchio_task(
    analytics,
    sms_count,
    email_count,
    postponed,
    page_source=None,
    invite_action=None,
    push_count=None,
):
    if not push_count:
        push_count = 0
    all_invites = sum([sms_count, email_count, push_count])
    analytics.track(
        'Invite_Process_Completed',
        {
            'event_date': tznow().strftime('%Y%m%d'),
            'total_invites_sent': all_invites,
            'postponed': postponed,
            'page_source': page_source,
            'event_action': invite_action,
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_invite_process_completed_gtm_task(
    analytics,
    user_id,
    sms_count,
    email_count,
    postponed,
    logged_in_user_id=None,
    page_source=None,
    invite_action=None,
    import_source=None,
    push_count=None,
    # TODO: legacy param replaced with import_source, left temporarily for backward compatibility
    process_type=None,
):
    if not push_count:
        push_count = 0

    all_invites = sum([sms_count, email_count, push_count])
    analytics.push(
        event_name='Invite_Process_Completed',
        event_data={
            'event_date': tznow().strftime('%Y%m%d'),
            'total_invites_sent': all_invites,
            'sms_invites_sent': sms_count,
            'email_invites_sent': email_count,
            'push_invites_sent': push_count,
            'import_source': import_source if import_source is not None else process_type,
            'postponed': postponed,
            'page_source': page_source,
            'event_action': invite_action,
        },
        request_params={
            'user_id': id_to_external_api(user_id),
        },
        user_properties={
            'logged_in_user_id': id_to_external_api(logged_in_user_id),
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_postponed_invites_queued_gtm_task(
    analytics,
    user_id,
    logged_in_user_id,
    page_source,
    frontend_action,
    import_source,
):
    analytics.push(
        event_name='Clients_Action',
        event_data={
            'event_date': tznow().strftime('%Y%m%d'),
            'import_source': import_source,
            'page_source': page_source,
            'frontend_action': frontend_action,
            'event_action': 'postponed_invites_queued',
            'postponed': True,
            'screen_name': 'server',
        },
        request_params={
            'user_id': id_to_external_api(user_id),
        },
        user_properties={
            'logged_in_user_id': id_to_external_api(logged_in_user_id),
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_postponed_invites_sent(analytics, user_id, sms_count, email_count, push_count=None):
    if not push_count:
        push_count = 0

    all_invites = sum([sms_count, email_count, push_count])
    analytics.push(
        event_name='Postponed_Invites_Sent',
        event_data={
            'event_date': tznow().strftime('%Y%m%d'),
            'total_invites_sent': all_invites,
            'sms_invites_sent': sms_count,
            'email_invites_sent': email_count,
            'push_invites_sent': push_count,
        },
        request_params={
            'user_id': id_to_external_api(user_id),
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_quick_invite_sent_task(
    analytics,
    user_id,
    notification_type,
    recipient_address,
    logged_in_user_id=None,
):
    analytics.push(
        event_name='Quick_Invite_Sent',
        event_data={
            'event_date': tznow().strftime('%Y%m%d'),
            'notification_type': notification_type,
            'recipient_address': recipient_address,
        },
        request_params={
            'user_id': id_to_external_api(user_id),
        },
        user_properties={
            'logged_in_user_id': id_to_external_api(logged_in_user_id),
        },
    )


@celery_task(time_limit=31 * 60, soft_time_limit=30 * 60)
def save_used_app_version_task():  # task spawned by celery beat
    from webapps.kill_switch.models import KillSwitch

    if KillSwitch.killed(KillSwitch.MarTech.BUSINESS_APP_OPENED):
        return
    river_values = AppUsedByUserByDate.pop_river_values()
    while river_values:
        save_100_used_app_version_records_task.delay(river_values)
        river_values = AppUsedByUserByDate.pop_river_values()


@celery_task(time_limit=31 * 60, soft_time_limit=30 * 60)
def save_100_used_app_version_records_task(records):
    app_used_records = []
    for record in records:
        (
            user_id,
            booking_source_id,
            business_id,
            date,
        ) = AppUsedByUserByDate.decode_single_river_value(record)
        app_used_records.append(
            AppUsedByUserByDate(
                booking_source_id=booking_source_id,
                date_used=date,
                user_id=user_id,
            )
        )
        analytics_business_app_opened_task.delay(
            user_id=user_id,
            booking_source_id=booking_source_id,
            business_id=business_id,
            context={
                'event_type': EventType.USER,
                'session_user_id': user_id,
                'source_id': booking_source_id,
            },
        )
    AppUsedByUserByDate.objects.bulk_create(
        app_used_records, batch_size=1000, ignore_conflicts=True
    )


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def segment_status_change_task(business_id, trial_status=None):
    from lib.segment_analytics import get_segment_api
    from webapps.business.models import Business

    business = Business.objects.get(id=business_id)
    segment_api = get_segment_api(business)
    if trial_status == 'started':
        segment_api.trial_started()
    elif trial_status == 'countdown':
        segment_api.trial_countdown_started()
    elif trial_status == 'finished':
        segment_api.trial_finished()
    segment_api.identify_merchant_status(business.status)


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def segment_intercom_status_update(business_id):
    from lib.segment_analytics import get_segment_api
    from webapps.business.models import Business

    business = Business.objects.get(id=business_id)
    segment_api = get_segment_api(business)
    segment_api.update_intercom()


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def segment_intercom_status_update_businesses(businesses_ids=None):
    from lib.segment_analytics import get_segment_api
    from webapps.booking.adapters import get_internal_booking_source
    from webapps.business.models import Business
    from webapps.business.tools import get_all_active_businesses_ids

    source = get_internal_booking_source()
    if businesses_ids is not None:
        qs = Business.objects.filter(id__in=businesses_ids)
    else:
        active_business_ids = get_all_active_businesses_ids()
        qs = Business.objects.filter(id__in=active_business_ids)
    businesses = qs.only('id', 'active', 'status', 'integrations')

    for business in businesses:
        segment_api = get_segment_api(business, source=source)
        segment_api.update_intercom()


@celery_task
def create_delayed_gtm_event_auth_data_task(
    business_id,
    firebase_auth_dict,
    event_type,
):
    from webapps.marketing.models import DelayedGTMEventAuthData

    DelayedGTMEventAuthData.objects.get_or_create(
        business_id=business_id,
        event_type=event_type,
        **firebase_auth_dict,
    )


@segment_analytics_retry_post_transaction_task
def analytics_protection_service_enabled_task(analytics, business_id):
    from webapps.business.models import Business, ServiceVariant

    business = (
        Business.objects.filter(id=business_id)
        .only(
            'package',
            'owner__email',
            'owner__cell_phone',
        )
        .first()
    )
    if not business:
        return
    offer_type = _get_offer_type_from_package(business.package)
    analytics.track(
        'Protection_Service_Enabled',
        {
            'email': business.owner.email,
            'country': settings.API_COUNTRY,
            'business_id': id_to_external_api(business_id),
            'booking_protection_enabled': True,
        },
    )
    analytics.identify(
        {
            'country': settings.API_COUNTRY,
            'email': business.owner.email,
            'user_role': UserRoleEnum.OWNER.value,
            'phone': business.owner.cell_phone_with_prefix,
            'offer_type': offer_type,
            'business_id': id_to_external_api(business_id),
            'booking_protection_enabled': True,
            'services_with_protection': ServiceVariant.objects.filter(
                active=True,
                service__business_id=business_id,
                payment__isnull=False,
            ).count(),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_onboarding_delay_set_task(analytics, business_id, delay_in_days):
    from webapps.business.models import Business

    business_data = (
        Business.objects.filter(
            id=business_id,
        )
        .values(
            'owner__email',
            'package',
        )
        .first()
    )

    offer_type = _get_offer_type_from_package(business_data['package'])
    common_properties = {
        'email': business_data['owner__email'],
        'country': settings.API_COUNTRY,
        'offer_type': offer_type,
        'business_id': id_to_external_api(business_id),
    }
    # pylint: enable=no-value-for-parameter

    analytics.track('Onboarding_Delay_Set', {'days_of_delay': delay_in_days, **common_properties})
    analytics.identify(
        {
            'user_role': 'Owner',
            **common_properties,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_payment_transaction_completed_task(analytics, transaction_id, analytics_payment_code):
    from webapps.pos.models import Transaction
    from webapps.segment.utils import analytics_business_any_no_show_protection_on

    transaction_data = (
        Transaction.objects.filter(id=transaction_id)
        .values(
            'transaction_type',
            'latest_receipt__payment_type__code',
            'latest_receipt__status_code',
        )
        .first()
    )

    if not transaction_data:
        return

    business_id = analytics.context.business_id
    transaction_receipt_status = transaction_data.get('latest_receipt__status_code')
    mobile_payment_type = analytics_payment_code
    transaction_type = mobile_payment_type

    if transaction_type in {PaymentTypeEnum.PAY_BY_APP, PaymentTypeEnum.PREPAYMENT}:
        transaction_type = PaymentTypeEnum.PAY_BY_APP

    if transaction_receipt_status == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS:
        mobile_payment_type = 'cancellation_fee'
    elif mobile_payment_type == PaymentTypeEnum.PAY_BY_APP:
        mobile_payment_type = 'mobile_payment'

    track_params = {
        'business_id': id_to_external_api(business_id),
        'transaction_type': transaction_type,
    }
    if mobile_payment_type in {'cancellation_fee', 'mobile_payment', PaymentTypeEnum.PREPAYMENT}:
        track_params['mobile_payment_type'] = mobile_payment_type

    analytics.track('Payment_Transaction_Completed', track_params)
    analytics.identify(
        {
            'business_id': id_to_external_api(business_id),
            'booking_protection_enabled': analytics_business_any_no_show_protection_on(business_id),
            '1st_transaction_made': True,
            'transaction_count': Transaction.objects.filter(
                pos__business_id=business_id,
            ).count(),
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_basket_payment_completed_task(analytics, basket_payment_id):
    service = SegmentFacade(
        analytics,
        CancellationReasonAdapter(),
    )
    service.basket_payment_completed(basket_payment_id=basket_payment_id)


@segment_analytics_retry_post_transaction_task
def analytics_business_status_updated_task(analytics, business_id):
    from webapps.business.models import Business

    if DISBALED_DURING_PYTESTS:
        return
    business_data = (
        Business.objects.filter(
            id=business_id,
        )
        .values(
            'owner__email',
            'status',
        )
        .first()
    )
    if not business_data:
        return
    admin_status = Business.Status(business_data['status']).name
    business_owner = business_data['owner__email']
    analytics.track(
        'Business_Status_Updated',
        {
            'email': business_owner,
            'business_id': id_to_external_api(business_id),
            'business_admin_status': admin_status,
        },
    )
    analytics.identify(
        {
            'user_role': UserRoleEnum.OWNER.value,
            'business_email': business_owner,
            'business_id': id_to_external_api(business_id),
            'business_admin_status': admin_status,
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_continuous_discovery_business_created_task(
    analytics,
) -> None:
    business_id = analytics.context.business_id
    msg = f'running discovery task for business {business_id}'
    onboarding_analytics_logger.warning(msg)
    try:
        analytics.track('Continuous_Discovery_Business_Created', None)
    except:
        onboarding_analytics_logger.exception(
            'Failed to track "Continuous_Discovery_Business_Created"'
        )
    else:
        msg = f'Ran discovery task for business {business_id}'
        onboarding_analytics_logger.warning(msg)


@segment_analytics_retry_post_transaction_task
def analytics_1st_no_show_for_business_task(analytics, booking_id):
    if DISBALED_DURING_PYTESTS:
        return
    from webapps.kill_switch.models import KillSwitch

    if KillSwitch.killed(KillSwitch.MarTech.FIRST_NO_SHOW_FOR_BUSINESS):
        return
    from webapps.booking.models import SubBooking

    booking_data = (
        SubBooking.objects.filter(
            id=booking_id,
        )
        .select_related(
            'appointment',
            'appointment__business',
        )
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
        )
        .values(
            'appointment__id',
            'appointment__business__id',
            'appointment__business__owner',
            'appointment__business__owner__email',
            'appointment__business__primary_category__internal_name',
            'service_variant_id',
            'booking_value',
        )
        .first()
    )

    if not booking_data:
        return

    analytics.track(
        '1st_No_Show_For_Business',
        {
            'service_id': [booking_data.get("service_variant_id")],
            'business_id': id_to_external_api(booking_data.get('appointment__business__id')),
            'business_primary_category': booking_data.get(
                'appointment__business__primary_category__internal_name'
            ),
            'price': booking_data.get('booking_value', 0),
            'booking_id': id_to_external_api(booking_id),
            'email': booking_data.get('appointment__business__owner__email'),
            'user_role': get_user_role_by_id(booking_data.get('appointment__business__owner')),
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_bb_no_show_for_business_task(analytics, booking_id):
    from webapps.booking.models import Appointment
    from webapps.kill_switch.models import KillSwitch

    if KillSwitch.killed(KillSwitch.MarTech.BB_NO_SHOW_FOR_BUSINESS):
        return
    event_data = _get_no_show_for_business_data(booking_id, Appointment.TYPE.BUSINESS)
    if not event_data:
        return

    analytics.track(
        'BB_No_Show_For_Business',
        {
            'service_id': event_data['service_id'],
            'business_id': event_data['business_id'],
            'business_primary_category': event_data['business_primary_category'],
            'price': event_data['price'],
            'booking_id': id_to_external_api(booking_id),
        },
    )
    analytics.identify(
        {
            'business_BB_no_show_count': event_data['no_show_count'],
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_no_show_for_business_task(analytics, booking_id):
    from webapps.booking.models import Appointment
    from webapps.kill_switch.models import KillSwitch

    if KillSwitch.killed(KillSwitch.MarTech.CB_NO_SHOW_FOR_BUSINESS):
        return

    event_data = _get_no_show_for_business_data(booking_id, Appointment.TYPE.CUSTOMER)
    if not event_data:
        return

    analytics.track(
        'CB_No_Show_For_Business',
        {
            'service_id': event_data['service_id'],
            'business_id': event_data['business_id'],
            'business_primary_category': event_data['business_primary_category'],
            'price': event_data['price'],
            'booking_id': id_to_external_api(booking_id),
        },
    )
    analytics.identify(
        {
            'business_CB_no_show_count': event_data['no_show_count'],
        }
    )


@segment_analytics_retry_post_transaction_task
def analytics_cb_started_for_customer(
    analytics,
    business_id: int,
    gender_code: str,
    service_id: List[int],
    user_id: int,
    booking_source: Optional[str] = None,
):
    from webapps.business.models import Business, Service

    business = Business.objects.filter(
        id=business_id,
    ).first()

    # Here service_id is an array, so this qs can return many services
    services_data = Service.objects.filter(
        id__in=service_id,
    ).values(
        'id',
        'name',
        'business__name',
    )

    if not services_data:
        return

    service_id = []
    service_name = []
    for service in services_data:
        service_id.append(service['id'])
        service_name.append(service['name'])
    business_name = services_data[0].get('business__name')

    event_properties = {
        'last_dropped_business_name': business_name,
        'last_dropped_service_name': service_name,
        'user_id': id_to_external_api(user_id),
        'userId': id_to_external_api(user_id),
        'business_id': id_to_external_api(business_id),
        'booking_source': booking_source or '',
        'country': settings.API_COUNTRY,
        'gender_code': gender_code,
        'service_id': service_id,
        'last_dropped_booking_provider_deeplink': business.get_marketplace_deeplink(),
    }
    analytics.track('CB_Started_For_Customer', event_properties)
    analytics.identify(event_properties)


@segment_analytics_retry_post_transaction_task
def analytics_business_kyc_success_task(analytics, account_holder_id):
    from webapps.market_pay.models import AccountHolder
    from webapps.segment.utils import analytics_business_any_no_show_protection_on

    account_holder_data = (
        AccountHolder.objects.filter(
            id=account_holder_id,
        )
        .values(
            'payout_allowed',
            'pos__business__owner__email',
            'pos__business_id',
        )
        .first()
        or {}
    )
    payout_allowed = account_holder_data.get('payout_allowed')
    business_id = account_holder_data.get('pos__business_id')
    owner_email = account_holder_data.get('pos__business__owner__email')
    if not all([payout_allowed, business_id, owner_email]):
        return
    booking_protection_enabled = analytics_business_any_no_show_protection_on(business_id)

    common_properties = {
        'email': account_holder_data.get('pos__business__owner__email'),
        'business_id': id_to_external_api(business_id),
        'business_kyc_status': KYCStatus.ACTIVE.value,  # constant in docs
        'payouts_allowed': payout_allowed,
        'booking_protection_enabled': booking_protection_enabled,
    }
    analytics.track('Business_KYC_Success', common_properties)
    analytics.identify(
        {
            'user_role': UserRoleEnum.OWNER.value,
            **common_properties,
        }
    )


@branchio_analytics_retry_post_transaction_task
def analytics_business_adyen_kyc_success_branchio_task(analytics, account_holder_id):
    from webapps.segment.utils import analytics_business_any_no_show_protection_on
    from webapps.market_pay.models import AccountHolder

    account_holder_data = (
        AccountHolder.objects.filter(
            id=account_holder_id,
        )
        .values(
            'payout_allowed',
            'pos__business__owner__email',
            'pos__business__owner__id',
            'pos__business_id',
        )
        .first()
        or {}
    )
    payout_allowed = account_holder_data.get('payout_allowed')
    business_id = account_holder_data.get('pos__business_id')
    owner_email = account_holder_data.get('pos__business__owner__email')
    user_id = account_holder_data.get('pos__business__owner__id')
    if not all([payout_allowed, business_id, owner_email]):
        return
    booking_protection_enabled = analytics_business_any_no_show_protection_on(business_id)

    event_data = {
        'user_id': id_to_external_api(user_id),
        'email': owner_email,
        'country': settings.API_COUNTRY,
        'business_id': id_to_external_api(business_id),
        'business_kyc_status': KYCStatus.ACTIVE,  # constant in docs
        'payouts_allowed': payout_allowed,
        'booking_protection_enabled': booking_protection_enabled,
    }
    analytics.track(
        event_name=AnalyticEventEnums.KYC_SUCCESS,
        event_data=event_data,
    )


@gtm_analytics_retry_post_transaction_task
def analytics_business_report_generated_to_email(analytics, user_id, report_key):
    analytics.push(
        event_name='Business_Report_Generated_To_Email',
        event_data={
            'generated_report_type': report_key,
        },
        request_params={
            'user_id': id_to_external_api(user_id),
        },
        # TODO Remove user_properties after MR 5643 (task 81250) is merged
        user_properties={
            'app_version': BooksyAppVersions.B30,
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_bcr_stripe_kyc_pending_account_gtm_task(
    analytics,
    business_id,
):
    from webapps.stripe_integration.enums import StripeAccountStatus

    owner_id = _bcr_common_properties(business_id)
    if not owner_id:
        return

    analytics.push(
        event_name=AnalyticEventEnums.BCR_STRIPE_KYC_PENDING_ACCOUNT.value,
        event_data={
            'stripe_kyc_status': StripeAccountStatus.VERIFICATION_PENDING.value,
        },
        request_params={
            'user_id': owner_id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_bcr_stripe_kyc_pending_account_segment_task(
    analytics,
    business_id,
):
    from webapps.stripe_integration.enums import StripeAccountStatus

    user_id = _bcr_common_properties(business_id)
    if not user_id:
        return

    analytics.track(
        AnalyticEventEnums.BCR_STRIPE_KYC_PENDING_ACCOUNT.value,
        {
            'stripe_kyc_status': StripeAccountStatus.VERIFICATION_PENDING,
            'business_phone': analytics.context.business.phone_with_prefix,
        },
    )
    analytics.identify(
        {
            'stripe_kyc_status': StripeAccountStatus.VERIFICATION_PENDING,
            'business_phone': analytics.context.business.phone_with_prefix,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_bcr_stripe_kyc_verified_account_gtm_task(
    analytics,
    business_id,
):
    from webapps.stripe_integration.enums import StripeAccountStatus

    owner_id = _bcr_common_properties(business_id)
    if not owner_id:
        return

    analytics.push(
        event_name=AnalyticEventEnums.BCR_STRIPE_KYC_VERIFIED_ACCOUNT.value,
        event_data={
            'stripe_kyc_status': StripeAccountStatus.VERIFIED.value,
        },
        request_params={
            'user_id': owner_id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_bcr_stripe_kyc_verified_account_segment_task(
    analytics,
    business_id,
):
    from webapps.stripe_integration.enums import StripeAccountStatus

    user_id = _bcr_common_properties(business_id)
    if not user_id:
        return

    analytics.track(
        AnalyticEventEnums.BCR_STRIPE_KYC_VERIFIED_ACCOUNT.value,
        {
            'stripe_kyc_status': StripeAccountStatus.VERIFIED,
            'business_phone': analytics.context.business.phone_with_prefix,
        },
    )
    analytics.identify(
        {
            'stripe_kyc_status': StripeAccountStatus.VERIFIED,
            'business_phone': analytics.context.business.phone_with_prefix,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_bcr_stripe_kyc_not_verified_account_gtm_task(
    analytics,
    business_id,
):
    from webapps.stripe_integration.enums import StripeAccountStatus

    owner_id = _bcr_common_properties(business_id)
    if not owner_id:
        return

    analytics.push(
        event_name=AnalyticEventEnums.BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT.value,
        event_data={
            'stripe_kyc_status': StripeAccountStatus.NOT_VERIFIED.value,
        },
        request_params={
            'user_id': owner_id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_bcr_stripe_kyc_not_verified_account_segment_task(
    analytics,
    business_id,
):
    from webapps.stripe_integration.enums import StripeAccountStatus

    owner_id = _bcr_common_properties(business_id)
    if not owner_id:
        return

    analytics.track(
        AnalyticEventEnums.BCR_STRIPE_KYC_NOT_VERIFIED_ACCOUNT.value,
        {
            'stripe_kyc_status': StripeAccountStatus.NOT_VERIFIED,
            'business_phone': analytics.context.business.phone_with_prefix,
        },
    )
    analytics.identify(
        {
            'stripe_kyc_status': StripeAccountStatus.NOT_VERIFIED,
            'business_phone': analytics.context.business.phone_with_prefix,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_bcr_terminal_ordered_gtm_task(
    analytics,
    order_id,
):
    from collections import defaultdict

    from webapps.stripe_integration.enums import StripeAccountOnboardingStatus
    from webapps.stripe_terminal.models import Order

    order = Order.objects.filter(id=order_id).first()

    owner_id = _bcr_common_properties(order.business_id)
    if not owner_id:
        return

    cart_ordered_terminals = defaultdict(int)
    for order_item in order.items.select_related('hardware'):
        cart_ordered_terminals[order_item.hardware.product_type or order_item.hardware.name] += (
            order_item.quantity or 0
        )

    analytics.push(
        event_name=AnalyticEventEnums.BCR_TERMINAL_ORDERED,
        event_data={
            'stripe_bcr_onboarding_status': StripeAccountOnboardingStatus.TERMINAL_ORDERED,
            **dict(cart_ordered_terminals),
        },
        request_params={
            'user_id': owner_id,
        },
    )


@branchio_analytics_retry_post_transaction_task
def analytics_bcr_terminal_ordered_branchio_task(
    analytics,
    order_id,
):
    from webapps.stripe_integration.enums import StripeAccountOnboardingStatus
    from webapps.stripe_terminal.models import Order

    order = Order.objects.filter(id=order_id).first()

    owner_id = _bcr_common_properties(order.business_id)
    if not owner_id:
        return

    cart_ordered_terminals = defaultdict(int)
    for order_item in order.items.select_related('hardware'):
        cart_ordered_terminals[order_item.hardware.product_type or order_item.hardware.name] += (
            order_item.quantity or 0
        )
    event_data = {
        'stripe_bcr_onboarding_status': StripeAccountOnboardingStatus.TERMINAL_ORDERED,
        **dict(cart_ordered_terminals),
    }

    analytics.track(
        event_name=AnalyticEventEnums.BCR_TERMINAL_ORDERED,
        event_data=event_data,
    )


@segment_analytics_retry_post_transaction_task
def analytics_bcr_terminal_ordered_segment_task(
    analytics,
    order_id,
):
    from collections import defaultdict

    from webapps.stripe_integration.enums import StripeAccountOnboardingStatus
    from webapps.stripe_terminal.models import Order

    order = Order.objects.filter(id=order_id).first()
    owner_id = _bcr_common_properties(order.business_id)
    if not owner_id:
        return

    cart_ordered_terminals = defaultdict(int)
    for order_item in order.items.select_related('hardware'):
        cart_ordered_terminals[order_item.hardware.product_type or order_item.hardware.name] += (
            order_item.quantity or 0
        )

    analytics.track(
        AnalyticEventEnums.BCR_TERMINAL_ORDERED,
        {
            'business_phone': analytics.context.business.phone_with_prefix,
            'stripe_bcr_onboarding_status': StripeAccountOnboardingStatus.TERMINAL_ORDERED,
            'cart_ordered_terminals': {**dict(cart_ordered_terminals)},
        },
    )
    analytics.identify(
        {
            'stripe_bcr_onboarding_status': StripeAccountOnboardingStatus.TERMINAL_ORDERED,
            'cart_ordered_terminals': {**dict(cart_ordered_terminals)},
            'business_phone': analytics.context.business.phone_with_prefix,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_bcr_order_received_gtm_task(
    analytics,
    order_id,
):
    from webapps.stripe_terminal.enums import StripeStatusType
    from webapps.stripe_terminal.models import Order, OrderStatusHistory

    order = Order.objects.filter(id=order_id).first()
    if not order:
        return

    order_status_history = OrderStatusHistory.objects.filter(
        order_id=order.id,
        stripe_status=StripeStatusType.DELIVERED,
    ).first()
    if not order_status_history:
        return

    owner_id = _bcr_common_properties(order.business_id)
    analytics.push(
        event_name=AnalyticEventEnums.BCR_ORDER_RECEIVED.value,
        event_data={
            'order_status_change_date': order_status_history.created.date().isoformat(),
        },
        request_params={
            'user_id': owner_id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_bcr_order_received_segment_task(
    analytics,
    order_id,
):
    from webapps.stripe_terminal.enums import StripeStatusType
    from webapps.stripe_terminal.models import Order, OrderStatusHistory

    order = Order.objects.filter(id=order_id).first()
    if not order:
        return

    order_status_history = OrderStatusHistory.objects.filter(
        order_id=order.id,
        stripe_status=StripeStatusType.DELIVERED,
    ).first()
    if not order_status_history:
        return

    analytics.identify(
        {
            'business_phone': analytics.context.business.phone_with_prefix,
        }
    )
    analytics.track(
        AnalyticEventEnums.BCR_ORDER_RECEIVED.value,
        {
            'business_phone': analytics.context.business.phone_with_prefix,
            'order_status_change_date': order_status_history.created.date().isoformat(),
        },
    )


@gtm_analytics_retry_post_transaction_task
def analytics_bcr_reset_account_verify_gtm_task(
    analytics,
    business_id,
):
    owner_id = _bcr_common_properties(business_id)
    if not owner_id:
        return

    analytics.push(
        event_name=AnalyticEventEnums.BCR_RESET_ACCOUNT_VERIFY.value,
        event_data={'owner_id': owner_id},
        request_params={
            'user_id': owner_id,
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_bcr_reset_account_verify_segment_task(
    analytics,
    business_id,
):
    user_id = _bcr_common_properties(business_id)
    if not user_id:
        return

    analytics.track(
        AnalyticEventEnums.BCR_RESET_ACCOUNT_VERIFY.value,
        {
            'business_phone': analytics.context.business.phone_with_prefix,
        },
    )
    analytics.identify(
        {
            'business_phone': analytics.context.business.phone_with_prefix,
        }
    )


@gtm_analytics_retry_post_transaction_task
def analytics_view_item_list_gtm_task(analytics, event_parameters: dict, metadata: dict):
    parameters_serializer = ViewItemListParametersSerializer(data=event_parameters)
    parameters_serializer.is_valid(raise_exception=True)
    analytics.push(
        event_name='view_item_list',
        event_data=parameters_serializer.validated_data,
        request_params={},
        user_properties={
            'gender_code': metadata.get('gender_code'),
        },
    )


@segment_analytics_retry_post_transaction_task
def analytics_checkout_transaction_completed_task(
    analytics,
    transaction_id,
):
    from webapps.pos.models import Transaction

    transaction_data = (
        Transaction.objects.filter(id=transaction_id)
        .select_related(
            'latest_receipt',
        )
        .values(
            'subtotal_services',
            'subtotal_products',
            'latest_receipt__payment_type__code',
        )
        .first()
    )

    if not transaction_data:
        return

    payment_type = transaction_data.get('latest_receipt__payment_type__code')
    booking_amount = transaction_data.get('subtotal_services')
    product_amount = transaction_data.get('subtotal_products')
    properties = {
        'checkout_payment_method': payment_type,
        'appointment_amount': booking_amount,
        'product_amount': product_amount,
    }

    analytics.track('Checkout_Transaction_Completed', properties)


@segment_analytics_retry_post_transaction_task
def analytics_business_re_trial_eligible_task(
    analytics,
):
    analytics.track(AnalyticEventEnums.BUSINESS_RE_TRIAL_ELIGIBLE.value, {})


@segment_analytics_retry_post_transaction_task
def analytics_business_offline_migration_started_task(
    analytics,
):
    analytics.track('Business_Offline_Migration_Started', {})


@segment_analytics_retry_post_transaction_task
def analytics_business_special_offer_create_task(analytics, billing_business_offer_id):
    from webapps.billing.models.discounts import BillingDiscountCodeUsage
    from webapps.billing.models.offer import BillingBusinessOffer
    from webapps.billing.enums import ProductType

    billing_biz_offer = (
        BillingBusinessOffer.objects.filter(
            business_id=analytics.context.business_id, id=billing_business_offer_id
        )
        .select_related('offer')
        .first()
    )
    billing_biz_offer_data = {
        'name': billing_biz_offer.offer.name,
        'billing_business_offer_id': billing_business_offer_id,
        'end_date': billing_biz_offer.valid_to,
        'start_date': billing_biz_offer.valid_from,
        'subscription_duration': billing_biz_offer.offer.subscription_duration,
    }
    for billing_prd_offer_item in billing_biz_offer.offer.offer_items.filter(
        product__product_type__in=[ProductType.STAFFER_SAAS, ProductType.SAAS]
    ):
        prd_type = (
            billing_prd_offer_item.product.get_product_type_display().lower().replace(" ", "_")
        )
        billing_biz_offer_data.update(
            {
                f'product_{prd_type}_discount_duration': billing_prd_offer_item.discount_duration,
                f'product_{prd_type}_discount_amount': billing_prd_offer_item.discount_amount,
                f'product_{prd_type}_discount_frac': billing_prd_offer_item.discount_frac,
                f'product_{prd_type}_discount_type': billing_prd_offer_item.get_discount_type_display(),
            }
        )
    if discount_code := BillingDiscountCodeUsage.objects.filter(
        offer__id=billing_biz_offer.offer.id, business=analytics.context.business_id
    ).first():
        billing_biz_offer_data.update(
            {
                f'discount_code': discount_code.discount_code,
            }
        )
    analytics.track('Business_Special_Offer_Create', billing_biz_offer_data)


@segment_analytics_retry_post_transaction_task
def analytics_customer_payment_link_received_1st_time_task(
    analytics,
    appointment_id,
    customer_card_id,
):
    from lib.tools import tznow
    from webapps.booking.models import Appointment
    from webapps.business.models.bci import BusinessCustomerInfo

    app = Appointment.objects.filter(id=appointment_id).first()
    if not app:
        return

    bci = BusinessCustomerInfo.objects.filter(id=customer_card_id).first()
    if not bci:
        return

    if bci.user:
        payment_link_received = bci.user.business_customer_infos.filter(
            first_payment_link_received__isnull=False
        ).exists()
    else:
        payment_link_received = bci.first_payment_link_received

    if payment_link_received:
        return

    bci.first_payment_link_received = tznow()
    bci.save()

    params = {
        'appointment_id': id_to_external_api(appointment_id),
        'user_id': id_to_external_api(bci.user_id),
    }

    analytics.track(AnalyticEventEnums.PAYMENT_LINK_RECEIVED_FIRST_TIME.value, params)


@segment_analytics_retry_post_transaction_task
def analytics_churn_reason_updated_task(
    analytics,
    *,
    reason_id: int,
):
    service = SegmentFacade(
        analytics,
        CancellationReasonAdapter(),
    )
    service.update_churn_reason(reason_id=reason_id)


@branchio_analytics_retry_post_transaction_task
def analytics_paid_status_achieved_branchio_task(analytics, business_id: int) -> None:
    from webapps.business.models import Business
    from webapps.business.models.business_change import BusinessChange

    status_change_count = BusinessChange.objects.filter(
        business_id=business_id, data__regex=r'"business.status":.*"P"\n.*]'
    ).count()

    analytics.track(
        'Paid_Status_Achieved',
        {
            'business_admin_status': Business.Status.PAID.name,
            'paid_counter': status_change_count,
        },
    )


# <editor-fold desc="helper segment functions">
def _bcr_common_properties(business_id):
    from webapps.business.models import Business

    business_data = (
        Business.objects.filter(id=business_id)
        .values(
            'owner__id',
        )
        .first()
    )
    # TODO more params will be added in PAYM-606
    if not business_data:
        return None

    return id_to_external_api(business_data['owner__id'])


def _get_plan_name(business):
    from webapps.purchase.models import Subscription

    return (
        Subscription.objects.filter(
            business=business,
        )
        .values_list('product__name', flat=True)
        .first()
        or ''
    )


def _get_no_show_for_business_data(booking_id, booking_type):
    from webapps.booking.models import Appointment, SubBooking

    filter_values = [
        'appointment__business__id',
        'appointment__business__primary_category__internal_name',
        'service_variant_id',
        'booking_value',
        'appointment__business__owner__email',
        'appointment__business__owner__cell_phone',
        'appointment__business__owner__id',
    ]
    related = [
        'appointment',
        'appointment__business',
    ]
    if booking_type in Appointment.TYPE.BUSINESS:
        filter_values.extend(['appointment__source__name', 'appointment__source__api_key'])
        related.extend('appointment__source')
    booking_data = (
        SubBooking.objects.filter(
            id=booking_id,
        )
        .select_related(
            *related,
        )
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
        )
        .values(
            *filter_values,
        )
        .first()
    )

    if not booking_data:
        return None

    owner_id = booking_data.get('appointment__business__owner__id')
    phone = get_prep_value_form(booking_data.get('appointment__business__owner__cell_phone'))

    result = {
        'phone': phone,
        'email': booking_data.get('appointment__business__owner__email'),
        'business_id': id_to_external_api(booking_data.get('appointment__business__id')),
        'price': booking_data.get('booking_value', 0),
        'service_id': [booking_data.get('service_variant_id')],
        'business_primary_category': booking_data.get(
            'appointment__business__primary_category__internal_name'
        ),
        'owner_id': id_to_external_api(owner_id),
        'no_show_count': Appointment.objects.filter(
            business_id=booking_data.get('appointment__business__id'),
            type=booking_type,
            status=Appointment.STATUS.NOSHOW,
        ).count(),
    }
    if booking_type in Appointment.TYPE.BUSINESS:
        result.update(
            {
                'device_type': booking_data.get('appointment__source__name'),
                'app_version': BooksyAppVersions.B30.value,
            }
        )
    return result


def get_device_type_name_from_booking_source(booking_source):
    return get_device_type_name_from_api_key(booking_source.api_key)


def _get_cb_counters(user_id=None, bci_id=None):
    from webapps.booking.models import Appointment

    unique_merchants_ids = set()
    unique_merchants_ids_with_cb_finished = set()
    unique_categories_ids = set()
    unique_categories_ids_with_cb_finished = set()
    appointments = None
    if user_id:
        appointments = Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            booked_for__user_id=user_id,
        ).values(
            'business',
            'status',
            'business__primary_category',
        )
    elif bci_id:
        appointments = Appointment.objects.filter(
            type=Appointment.TYPE.CUSTOMER,
            booked_for_id=bci_id,
        )

    if appointments:
        appointments_data = appointments.values(
            'business',
            'status',
            'business__primary_category',
        )
        for appointment_data in appointments_data:
            business_id = appointment_data['business']
            category_id = appointment_data['business__primary_category']
            unique_merchants_ids.add(business_id)
            unique_categories_ids.add(category_id)
            if appointment_data['status'] == Appointment.STATUS.FINISHED:
                unique_merchants_ids_with_cb_finished.add(business_id)
                unique_categories_ids_with_cb_finished.add(category_id)
    return {
        'CB_created_merchants_count': len(unique_merchants_ids),
        'CB_finished_merchants_count': len(unique_merchants_ids_with_cb_finished),
        'CB_created_categories_count': len(unique_categories_ids),
        'CB_finished_categories_count': len(unique_categories_ids_with_cb_finished),
    }


def _get_cb_common_properties(booking, business, analytics):
    service_name = []
    category_name = []
    category_id = []
    service_id = []

    service_price = booking['service_variant_price'] or 0

    _append_if_value(service_name, sget(booking, ['service_variant', 'service', 'name']))
    _append_if_value(
        category_name,
        sget(
            booking,
            ['service_variant', 'service', 'service_category', 'name'],
        ),
    )
    _append_if_value(
        category_id,
        id_to_external_api(
            sget(
                booking,
                ['service_variant', 'service', 'service_category_id'],
            )
        ),
    )
    _append_if_value(
        service_id,
        id_to_external_api(
            sget(
                booking,
                ['service_variant', 'service_id'],
            )
        ),
    )

    return {
        'booked_from': booking.booked_from.isoformat(),
        'category_id': category_id,
        'category_name': category_name,
        'service_id': service_id,
        'service_name': service_name,
        'service_price': service_price,
        'is_chargeable': booking.appointment.get_chargeable_for_analytics(),
        'source': booking.appointment.source.name,
        'business_id': id_to_external_api(business.id),
        'business_name': business.name,
        'business_primary_category': _get_business_primary_category(business),
        'staff_id': id_to_external_api(booking.staffer_id),
    }


def _get_cb_for_business_events_cmn_properties(appointment, booking_dct, analytics):
    from webapps.booking.models import Resource

    service_name = _create_list(booking_dct['service_variant__service__name'])
    category_name = _create_list(
        booking_dct['service_variant__service__service_category__name'],
    )
    category_id = _create_list(
        id_to_external_api(booking_dct['service_variant__service__service_category_id'])
    )
    service_id = _create_list(
        id_to_external_api(booking_dct['service_variant__service_id']),
    )

    staffer_id = (
        Resource.objects.filter(
            subbookings__id=booking_dct['id'],
            type=Resource.STAFF,
        )
        .values_list('id', flat=True)
        .first()
    )
    biz = appointment.business
    return {
        'booked_from': booking_dct['booked_from'].isoformat(),
        'category_id': category_id,
        'category_name': category_name,
        'service_id': service_id,
        'service_name': service_name,
        'service_price': booking_dct.get('booking_value', 0),
        'is_chargeable': appointment.get_chargeable_for_analytics(),
        'source': appointment.source.name,
        'business_id': id_to_external_api(appointment.business_id),
        'business_name': biz.name,
        'business_primary_category': (biz.primary_category and biz.primary_category.internal_name),
        'staff_id': id_to_external_api(staffer_id),
    }


def _get_cb_common_properties_optimized(subbookings, business):
    from webapps.business.models import Resource

    booking_score = Decimal('0')
    service_name = []
    category_name = []
    category_id = []
    service_id = []
    staff_id = []
    service_price = []
    subbooking_id = []
    for subbooking in subbookings:
        _append_if_value(subbooking_id, subbooking.get('id'))
        _append_if_value(service_name, subbooking['service_variant__service__name'])
        _append_if_value(service_price, subbooking.get('booking_value', 0))
        _append_if_value(
            category_name,
            subbooking['service_variant__service__service_category__name'],
        )
        _append_if_value(
            category_id,
            id_to_external_api(
                subbooking['service_variant__service__service_category_id'],
            ),
        )
        _append_if_value(
            service_id,
            id_to_external_api(subbooking['service_variant__service_id']),
        )
        single_staff_id = (
            Resource.objects.filter(
                subbookings__id=subbooking['id'],
                type=Resource.STAFF,
            )
            .values_list('id', flat=True)
            .first()
        )
        _append_if_value(staff_id, id_to_external_api(single_staff_id))
        booking_score += get_booking_score_optimized(subbooking, business)

    return {
        'category_id': category_id,
        'category_name': category_name,
        'service_id': service_id,
        'service_name': service_name,
        'service_price': service_price,
        'source': subbookings[0].get('appointment__source__name'),
        'business_id': id_to_external_api(subbookings[0].get('appointment__business_id')),
        'business_name': subbookings[0].get('appointment__business__name'),
        'business_primary_category': subbookings[0].get(
            'appointment__business__primary_category__internal_name'
        ),
        'staff_id': staff_id,
        'booking_score': booking_score,
    }


def _get_business_primary_category(business_object):
    if not business_object or not business_object.primary_category:
        return
    return business_object.primary_category.internal_name


def _append_if_value(list_object, value):
    if value:
        list_object.append(value)


def _create_list(value):
    return [value] if value else []


def _extend_if_value(list_object, new_values_list):
    new_values_list = [value for value in new_values_list if value]
    list_object.extend(new_values_list)
    return list_object


def _get_geo_information(business, with_state=True):
    from webapps.segment.models import ZipCodesToUrbanAreasMapper

    zip_code = business.region.name if business.region else None
    mapping = (
        ZipCodesToUrbanAreasMapper.objects.filter(
            zip_code=zip_code,
        )
        .values(
            'urban_area',
            'urban_subarea',
            'focus_area',
        )
        .first()
    )
    mapping_info = mapping or {
        'urban_area': None,
        'urban_subarea': None,
        'focus_area': None,
    }
    if with_state:
        if business.region:
            mapping_info['state'] = business.region.get_parent_name_by_type(settings.ES_ADM_1_LVL)
        else:
            mapping_info['state'] = None

    return mapping_info


def _get_device_id_and_appsflyer_advertising_id(business_id):
    from webapps.business.models.external import AppsFlyer

    return (
        AppsFlyer.objects.filter(
            business_id=business_id,
        )
        .values(
            'advertising_id',
            'appsflyer_device_id',
        )
        .first()
        or {}
    )


def _get_zip(zipcode, region_type, region_name):
    from webapps.structure.models import Region

    if zipcode:
        return zipcode
    if region_type != Region.Type.ZIP:
        return None
    return region_name


def _get_offer_type_from_package(package):
    offer_type = None
    if package:
        from webapps.business.models import Business

        offer_type = str(Business.Package(package).label)
    return offer_type


@celery_task
def forget_email_gdpr(email: str) -> None:
    """Remove email from Iterable in compliance with gdpr and prevent future data collection
    :param email: email to forget
    :type email: str
    :return: None
    """
    if not ForgetUserEmailGDPRFlag():
        return

    try:
        IterableClient().forget_email_gdpr(email=email)
    except IterableAPIError as error:
        _logger.error('Unable to forget email %s: %s', email, error)


def _get_common_properties(appointment):
    from webapps.booking.models import SubBooking

    subbookings_dct = (
        SubBooking.objects.filter(
            appointment_id=appointment.id,
            deleted__isnull=True,
        )
        .annotate_is_chargeable_for_analytics()
        .annotate_service_variant_price()
        .annotate(
            booking_value=Coalesce(
                F('resolved_price'),
                F('service_variant_price'),
                Value(Decimal('0')),
            ),
        )
        .values(
            'id',
            'booked_from',
            'service_variant__service_id',
            'service_variant__service__name',
            'service_variant__service__service_category__name',
            'service_variant__service__service_category_id',
            'booking_value',
            'appointment__booked_for',
            'appointment__business_id',
            'appointment__customer_email',
            'appointment__business__region__name',
            'appointment__business__region',
            'appointment__business__name',
            'appointment__source__name',
            'appointment__business__primary_category__internal_name',
            'appointment__booked_for_id',
            'is_chargeable_for_analytics',
        )
    )

    business_region = appointment.business.region
    zip_code = _get_zip(
        appointment.business.zipcode,
        business_region.type if business_region else None,
        business_region.name if business_region else None,
    )
    geo_information = _get_geo_information(appointment.business, False)
    return {
        'business_postal_code': zip_code,
        'booked_from': subbookings_dct[0]['booked_from'].isoformat(),
        'appointment_type': (
            AppointmentTypeEnum.MULTIBOOKING
            if appointment.is_multibooking()
            else AppointmentTypeEnum.SINGLE
        ),
        'business_id': id_to_external_api(appointment.business_id),
        'appointment_id': id_to_external_api(appointment.id),
        'customer_id': id_to_external_api(appointment.booked_for.user_id),
        'is_xCB': appointment.is_crossing(),
        'is_xCB_xCategory': appointment.is_crossing_and_cross_category(),
        'energy_booking': subbookings_dct[0].get('is_chargeable_for_analytics'),
        'business_name': subbookings_dct[0].get('appointment__business__name'),
        'business_primary_category': subbookings_dct[0].get(
            'appointment__business__primary_category__internal_name'
        ),
        'family_and_friends_role': appointment.family_and_friends_role,
        'source': appointment.source.name,
        **_get_cb_common_properties_optimized(subbookings_dct, appointment.business),
        **geo_information,
    }


# </editor-fold>
