import abc
from dataclasses import dataclass
from enum import StrEnum

from country_config.enums import Country
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import PaymentMethodType, BasketPaymentSource
from webapps.business.enums import (
    AutoCancellationReasonType,
    CancellationReasonType,
)


class SegmentEvent(abc.ABC):
    # Protocol would be better but there's no type checker!
    event_name: str

    @abc.abstractmethod
    def payload(self) -> dict[str, str]: ...


@dataclass(frozen=True)
class GlobalBusinessId:
    country: Country
    business_id: int

    def __str__(self) -> str:
        return f"{self.country.value}-{self.business_id}"


@dataclass(frozen=True)
class ChurnReasonUpdated(SegmentEvent):
    class ChurnType(StrEnum):
        SYSTEM = "system"
        BUSINESS = "business"

    class ChurnReason:
        # A key from either a CancellationReasonType or AutoCancellationReasonType enums.
        # Their "values" can't be used as they
        #   - use one/two-letter abbreviations
        #   - come with a meaningful longer name for translation
        # This approach couples domain type with user interface.
        # The point of this enum is to break free of this coupling and limit to these only.
        # This is achieved by using keys from the above as values here
        def __init__(
            self,
            *,
            value: AutoCancellationReasonType | CancellationReasonType,
        ):
            # Without type checker, we need to be one...
            if not isinstance(value, (AutoCancellationReasonType, CancellationReasonType)):
                raise ValueError(f"unsupported reason: '{value}'")

            self._value: str = value.name

        def __str__(self) -> str:
            return self._value

    event_name = "Churn_Reason_Updated"

    business_id: GlobalBusinessId
    email: str
    cancellation_type: ChurnType
    cancellation_reason: ChurnReason

    def payload(self) -> dict[str, str]:
        return {
            "business_id": str(self.business_id),
            "email": self.email,
            "cancellation_type": str(self.cancellation_type),
            "cancellation_reason": str(self.cancellation_reason),
        }


@dataclass(frozen=True)
class BasketPaymentCompleted(SegmentEvent):
    event_name = "Basket_Payment_Completed"

    business_id: GlobalBusinessId
    payment_method: PaymentMethodType
    payment_provider: PaymentProviderCode
    source: BasketPaymentSource

    def payload(self) -> dict[str, str]:
        return {
            "business_id": str(self.business_id),
            "payment_method": self.payment_method,
            "payment_provider": self.payment_provider,
            "source": self.source,
        }
