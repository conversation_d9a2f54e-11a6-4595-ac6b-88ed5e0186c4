from typing import Any

from django.db.models.signals import post_save
from django.dispatch import receiver

from lib.events import async_event_receiver, event_receiver
from lib.segment_analytics.enums import EventType
from webapps.billing.enums import TransactionSource, TransactionStatus
from webapps.billing.events import business_churn_scheduled as billing_business_churn_scheduled
from webapps.billing.models import BillingTransaction
from webapps.business.events import (
    business_churn_scheduled,
    business_status_changed_event,
)
from webapps.business.models import Business
from webapps.pos.events import basket_payment_completed_event
from webapps.segment.tasks import (
    analytics_basket_payment_completed_task,
    analytics_business_status_updated_task,
    analytics_business_subscription_updated_task,
    analytics_churn_reason_updated_task,
    analytics_paid_status_achieved_branchio_task,
    analytics_subscription_payment_succeeded_task,
    segment_api_overdue_status_started,
)
from webapps.segment.utils import post_first_paid_status_action


@async_event_receiver(business_status_changed_event)
def segment_api_overdue_status_started_handler(
    business_id: int,
    new_status: Business.Status,
    old_status: Business.Status | None = None,
    **kwargs,
):
    if old_status == Business.Status.PAID and new_status == Business.Status.OVERDUE:
        segment_api_overdue_status_started.delay(
            business_id=business_id,
        )


@async_event_receiver(business_status_changed_event)
def business_and_subscription_updated_events_handler(
    business_id: int,
    **kwargs,
):
    business = Business.objects.get(id=business_id)
    if business.payment_source == Business.PaymentSource.BRAINTREE_BILLING:
        analytics_business_subscription_updated_task.delay(
            business_id=business_id,
            context={'business_id': business_id},
        )
        analytics_business_status_updated_task.delay(
            business_id=business_id,
            context={'business_id': business_id},
        )


@async_event_receiver(business_status_changed_event)
def post_first_paid_status_action_handler(
    business_id: int,
    new_status: Business.Status,
    old_status: Business.Status | None = None,
    **kwargs,
):
    if (
        new_status == Business.Status.PAID
        and old_status in Business.Status.analytics_never_paid_statuses()
    ):
        post_first_paid_status_action(business_id, {'business_id': business_id})


@receiver(post_save, sender=BillingTransaction)
def successful_transaction_event(
    instance,
    created,
    **kwargs,
):
    if (
        created
        and instance.transaction_source == TransactionSource.BILLING_SUBSCRIPTION
        and instance.status == TransactionStatus.CHARGED
    ):
        analytics_subscription_payment_succeeded_task.delay(
            transaction_id=instance.id,
            context={
                'event_type': EventType.BUSINESS,
                'business_id': instance.business_id,
            },
        )


def paid_status_achieved_action(
    business_id: int,
    new_status: Business.Status,
    old_status: Business.Status | None = None,
):
    if old_status == new_status or new_status != Business.Status.PAID:
        return

    analytics_paid_status_achieved_branchio_task.delay(
        business_id=business_id,
        context={
            'business_id': business_id,
        },
    )


@async_event_receiver(business_status_changed_event)
def paid_status_achieved_event_handler(
    business_id: int,
    new_status: Business.Status,
    old_status: Business.Status | None = None,
    **kwargs,
):
    paid_status_achieved_action(
        business_id=business_id,
        new_status=new_status,
        old_status=old_status,
    )


@event_receiver(business_churn_scheduled)
@event_receiver(billing_business_churn_scheduled)
def update_churn_reason_receiver(sender: Any, **kwargs: dict):  # pylint: disable=unused-argument
    reason_id = kwargs.pop("reason_id", None)
    if isinstance(reason_id, int):
        analytics_churn_reason_updated_task.delay(reason_id=reason_id)


@event_receiver(basket_payment_completed_event)
def basket_payment_completed_receiver(
    sender: Any, basket_payment_id: str, business_id: int, **kwargs
):  # pylint: disable=unused-argument
    analytics_basket_payment_completed_task.delay(
        basket_payment_id=basket_payment_id,
        context={
            'business_id': business_id,
        },
    )
