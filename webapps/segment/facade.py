import logging
from typing import assert_never

from django.conf import settings

from country_config.enums import Country
from lib.segment_analytics.api import SegmentAnalyticsWrapper
from webapps.business.enums import CancellationType
from webapps.business.ports.cancellation_reason import CancellationReasonPort
from webapps.segment.segment_events import (
    BasketPaymentCompleted,
    ChurnReasonUpdated,
    GlobalBusinessId,
)

log = logging.getLogger("booksy.segment")


class SegmentFacade:
    def __init__(
        self,
        analytics: SegmentAnalyticsWrapper,
        cancellation_reason_port: CancellationReasonPort,
    ):
        self._analytics = analytics
        self._cancellation_reason_port = cancellation_reason_port

    def update_churn_reason(self, *, reason_id: int):
        reason = self._cancellation_reason_port.get_reason_why(reason_id)

        # We don't want to send an event when there is no "why".
        if not reason.cancellation_why:
            log.info("cancellation reason '%d' lacks why", reason_id)
            return

        match reason.cancellation_type:
            case CancellationType.AUTOMATIC:
                cancellation_type = ChurnReasonUpdated.ChurnType.SYSTEM
            case CancellationType.MADE_BY_BUSINESS:
                cancellation_type = ChurnReasonUpdated.ChurnType.BUSINESS
            case _:
                assert_never(reason.type)

        event = ChurnReasonUpdated(
            business_id=GlobalBusinessId(
                country=Country(settings.API_COUNTRY),  # pylint: disable=no-value-for-parameter
                business_id=reason.business.id,
            ),
            email=reason.business.owner_email,
            cancellation_type=cancellation_type,
            cancellation_reason=ChurnReasonUpdated.ChurnReason(value=reason.cancellation_why),
        )
        self._analytics.track(event.event_name, event.payload())

    def basket_payment_completed(self, basket_payment_id: str):
        from webapps.point_of_sale.ports import BasketPaymentPort

        basket_payment = BasketPaymentPort.get_basket_payment_entity(basket_payment_id)
        if not basket_payment:
            return

        event = BasketPaymentCompleted(
            business_id=GlobalBusinessId(
                country=Country(settings.API_COUNTRY),  # pylint: disable=no-value-for-parameter
                business_id=self._analytics.context.business_id,
            ),
            payment_method=basket_payment.payment_method,
            payment_provider=basket_payment.payment_provider_code,
            source=basket_payment.source,
        )
        self._analytics.track(event.event_name, event.payload())
