from django.conf import settings

from country_config.enums import Country
from lib import jinja_renderer
from lib.celery_tools import celery_task
from lib.email import send_email
from lib.tools import format_currency
from webapps.business.ports.business_details import BusinessDetailsPort
from webapps.point_of_sale.ports import BasketPort
from webapps.pos.ports import TransactionPort


@celery_task
def send_basket_payment_details_via_email(basket_id: str, email: str):
    basket = BasketPort.get_basket(basket_id=basket_id)

    receipt_details = TransactionPort.get_receipt_details(basket_id=basket_id)
    business_details = BusinessDetailsPort.get_business_details(basket.business_id)

    template_args = {
        'receipt': {
            'id': receipt_details.id,
            'receipt_number': receipt_details.receipt_number,
            'assigned_number': receipt_details.assigned_number,
        },
        'business_details': {
            'business_name': business_details.name,
            'business_address': business_details.address,
        },
        'basket_items': [],
        'basket_payments': [],
        'tax_summary': [],
        'summaries': [],
        'created': basket.created,
        'show_fiscal_disclaimer': settings.API_COUNTRY in (Country.FR, Country.PL),
        'show_taxes_info': False,  # todo
        'receipt_footer_line_1': 'footer 1',  # todo
        'receipt_footer_line_2': 'footer 2',  # todo
        'format_currency': format_currency,
    }

    sjr = jinja_renderer.ScenariosJinjaRenderer()
    body = sjr.render(
        scenario_name='basket',
        template_name='basket_details',
        language='pl',  # todo from request?
        template_args=template_args,
        extension='html',
        default=(),
    )

    from_data = (
        business_details.name,
        settings.NO_REPLY_EMAIL,
    )

    send_email(
        to_addr=email,
        body=body,
        history_data={
            'sender': 'TODO',
            'business_id': basket.business_id,
            # 'customer_id': transaction.customer_id,
            # 'task_id': f'pos:receipt:transaction_id={transaction.id}',
            'meta_receipt_number': receipt_details.receipt_number,
        },
        from_data=from_data,
        # to_name=(transaction.customer and transaction.customer.get_full_name()),  # todo
    )
