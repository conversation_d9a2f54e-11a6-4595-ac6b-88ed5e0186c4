from dataclasses import asdict

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.business.validators.access import get_business_view_validator
from lib.payment_gateway.entities import WalletEntity
from lib.payment_providers.entities import UserRequestData
from lib.payments.enums import PaymentProviderCode, PayoutType
from service.exceptions import ServiceError
from service.mixins.throttling import get_django_user_ip
from service.mixins.validation import validate_serializer
from webapps.business.enums import StaffAccessLevels
from webapps.business.models import Resource
from webapps.otp.views import OTPCodeMixin
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.exceptions.common import (
    DefaultPayoutMethodDeleteAttempt,
    PayoutMethodAddingException,
    PayoutMethodNotFound,
)

# pylint: disable=cyclic-import
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort
from webapps.payments.serializers.account_holder import StripeAccountHolderViewSerializer
from webapps.payments.serializers.account_management import PayoutMethodTokenSerializer
from webapps.pos.models import POS
from webapps.stripe_integration.enums import FastPayoutStatus
from webapps.stripe_integration.exceptions import StripeMultipleAccountsAttempt


class BaseAccountAPIView(BaseBooksySessionAPIView):
    permission_classes = (IsAuthenticated,)

    def validate_business(
        self,
        required_minimum_access_level: StaffAccessLevels = Resource.STAFF_ACCESS_LEVEL_OWNER,
    ):
        business_id = self.kwargs['business_pk']
        validator = get_business_view_validator(
            business=business_id,
            request=self.request,
            required_minimum_access_level=required_minimum_access_level,
            user=self.request.user,
        )
        validator.validate()


def get_available_payout_method_types() -> list:
    return settings.STRIPE_AVAILABLE_PAYOUT_METHOD_TYPES


def get_available_fast_payout_method_types(account_fast_payout_status: FastPayoutStatus) -> list:
    if account_fast_payout_status in [
        FastPayoutStatus.HIDDEN,
        FastPayoutStatus.BLOCKED,
        FastPayoutStatus.MERCHANT_DISABLED,
    ]:
        return []
    return settings.STRIPE_AVAILABLE_FAST_PAYOUT_METHOD_TYPES


class CreateProviderAccountAPIView(BaseAccountAPIView, GenericAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    serializer_class = StripeAccountHolderViewSerializer

    def post(self, request, business_pk, provider_code):
        self.validate_business()
        if provider_code not in [PaymentProviderCode.STRIPE]:
            return Response(data={}, status=status.HTTP_404_NOT_FOUND)

        pos = POS.objects.get(business_id=business_pk)
        serializer = self.get_serializer(
            data=request.data,
        )
        validated_data = validate_serializer(serializer)
        try:
            from webapps.stripe_integration.provider import StripeProvider

            # should be rewritten in the future
            _old_stripe_account, _created = StripeProvider.get_or_create_stripe_account(
                pos=pos,
                account_token=validated_data.get('account_token'),
                force_synchronize_with_new_structure=True,
            )
        except StripeMultipleAccountsAttempt:
            return Response(
                data={'error': _('Please try again later')},
                status=status.HTTP_400_BAD_REQUEST,
            )

        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=business_pk,
        )
        details = PaymentProvidersAccountHolderPort.get_provider_account_details(
            account_holder_id=wallet_entity.account_holder_id,
            payment_provider_code=PaymentProviderCode.STRIPE,
        ).entity
        return Response(asdict(details), status=status.HTTP_200_OK)


class ProviderAccountDetailsAPIView(BaseAccountAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    def get(self, request, business_pk, provider_code):
        self.validate_business(required_minimum_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF)
        if provider_code not in [PaymentProviderCode.STRIPE]:
            return Response(data={}, status=status.HTTP_404_NOT_FOUND)

        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=business_pk,
        )
        details = PaymentProvidersAccountHolderPort.get_provider_account_details(
            account_holder_id=wallet_entity.account_holder_id,
            payment_provider_code=PaymentProviderCode.STRIPE,
        ).entity
        if not details:
            return Response(data={}, status=status.HTTP_404_NOT_FOUND)

        return Response(asdict(details), status=status.HTTP_200_OK)


class CreatePayoutMethodAPIView(OTPCodeMixin, BaseAccountAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    def post(self, request, business_pk, provider_code):
        self.validate_business()
        self.validate_otp_code(biz_id=business_pk)
        if provider_code not in [PaymentProviderCode.STRIPE]:
            return Response(data={}, status=status.HTTP_404_NOT_FOUND)

        pos = POS.objects.get(business_id=business_pk)
        if not pos.stripe_kyc_enabled():
            return Response(data={}, status=status.HTTP_400_BAD_REQUEST)

        if not pos.is_payout_method_change_allowed:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'not_allowed_exception',
                        'description': (
                            "This action is not allowed, please contact customer support."
                        ),
                    }
                ],
            )

        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=business_pk,
        )

        serializer = PayoutMethodTokenSerializer(data=request.data)
        validated_data = validate_serializer(serializer)

        token = validated_data['token']
        try:
            payout_method = PaymentProvidersAccountHolderPort.add_provider_payout_method(
                account_holder_id=wallet_entity.account_holder_id,
                payment_provider_code=provider_code,
                token=token,
                user_data=UserRequestData(
                    user_id=request.user.id,
                    ip_address=get_django_user_ip(request),
                ),
            ).entity
            return Response(data=asdict(payout_method), status=status.HTTP_201_CREATED)
        except PayoutMethodAddingException as e:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'invalid_payout_method',
                        'description': str(e),
                    }
                ],
            ) from e


class DeletePayoutMethodAPIView(OTPCodeMixin, BaseAccountAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    def delete(self, request, business_pk, provider_code, payout_method_token):
        self.validate_business()
        self.validate_otp_code(biz_id=business_pk)
        if provider_code not in [PaymentProviderCode.STRIPE]:
            return Response(data={}, status=status.HTTP_404_NOT_FOUND)

        pos = POS.objects.get(business_id=business_pk)
        if not pos.stripe_kyc_enabled():
            return Response(data={}, status=status.HTTP_400_BAD_REQUEST)

        if not pos.is_payout_method_change_allowed:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'not_allowed_exception',
                        'description': (
                            "This action is not allowed, please contact customer support."
                        ),
                    }
                ],
            )

        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=business_pk,
        )

        try:
            PaymentProvidersAccountHolderPort.remove_provider_payout_method(
                account_holder_id=wallet_entity.account_holder_id,
                payment_provider_code=provider_code,
                token=payout_method_token,
                user_data=UserRequestData(
                    user_id=request.user.id,
                    ip_address=get_django_user_ip(request),
                ),
            )
            return Response(status=status.HTTP_204_NO_CONTENT)
        except DefaultPayoutMethodDeleteAttempt as e:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'default_payout_method_delete_attempt',
                        'description': "Can't delete a default payout method",
                    }
                ],
            ) from e


class SetPayoutMethodAsDefaultAPIView(OTPCodeMixin, BaseAccountAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    def post(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        request,
        business_pk,
        provider_code,
        payout_method_token,
        payout_type,
    ):
        self.validate_business()
        self.validate_otp_code(biz_id=business_pk)
        if provider_code not in [PaymentProviderCode.STRIPE]:
            return Response(data={}, status=status.HTTP_404_NOT_FOUND)

        pos = POS.objects.get(business_id=business_pk)
        if not pos.stripe_kyc_enabled():
            return Response(data={}, status=status.HTTP_400_BAD_REQUEST)

        if not pos.is_payout_method_change_allowed:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'not_allowed_exception',
                        'description': (
                            "This action is not allowed, please contact customer support."
                        ),
                    }
                ],
            )

        if payout_type not in PayoutType.values():
            raise ValueError

        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=business_pk,
        )

        try:
            PaymentProvidersAccountHolderPort.set_provider_payout_method_as_default(
                account_holder_id=wallet_entity.account_holder_id,
                payment_provider_code=provider_code,
                payout_type=payout_type,
                token=payout_method_token,
                user_data=UserRequestData(
                    user_id=request.user.id,
                    ip_address=get_django_user_ip(request),
                ),
            )
            return Response(data={}, status=status.HTTP_200_OK)
        except PayoutMethodNotFound:
            return Response(
                data={},
                status=status.HTTP_404_NOT_FOUND,
            )
