from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.user.serializer import EmptySerializer
from webapps.payments.tasks import send_basket_payment_details_via_email
from webapps.payments.serializers.payment import (
    CustomerBasketSerializer,
    CustomerBasketQueryParamsSerializer,
)
from webapps.point_of_sale.ports import BasketPort


class CustomerListBasketsView(
    BaseBooksySessionAPIView,
    GenericAPIView,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = CustomerBasketSerializer

    def get(self, request):
        query_params = CustomerBasketQueryParamsSerializer(data=self.request.query_params.dict())
        query_params.is_valid(raise_exception=True)

        user = self.request.user
        business_customer_info_ids = list(user.business_customer_infos.values_list("id", flat=True))

        results = BasketPort.get_baskets(
            customer_card_ids=business_customer_info_ids,
            page=query_params.validated_data['page'],
            per_page=query_params.validated_data['per_page'],
            basket_status=query_params.validated_data.get('basket_status'),
        )
        serializer = self.serializer_class(results, many=True)

        return Response(
            data={
                'results': serializer.data,
                'page': query_params.validated_data['page'],
                'per_page': query_params.validated_data['per_page'],
            },
            status=status.HTTP_200_OK,
        )


class CustomerBasketDetailsSendView(
    BaseBooksySessionAPIView,
    GenericAPIView,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = EmptySerializer

    def post(self, request, basket_id: str):
        basket = BasketPort.get_basket(basket_id)
        business_customer_info_ids = set(
            self.request.user.business_customer_infos.values_list("id", flat=True)
        )
        if basket.customer_card_id not in business_customer_info_ids:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        send_basket_payment_details_via_email.delay(
            basket_id=basket_id, email=self.request.user.email
        )
        return Response(status=status.HTTP_200_OK)
