# pylint: disable=duplicate-code
from datetime import date

from django.conf import settings

from country_config.enums import Country
from lib.payment_providers.entities import (
    ProviderAccountDetails,
    PayoutMethodDetails,
    BankAccount,
    PersonalDetails,
    StripeAccountDetails,
    UserInputKYC,
)
from lib.payment_providers.enums import (
    ProviderAccountHolderStatus,
    PayoutMethodErrorCode,
    PayoutMethodStatus,
    PayoutMethodType,
    StripeAccountBusinessType,
    StripeAccountType,
)
from lib.payments.enums import PaymentProviderCode
from webapps.business_consents.enums import ConsentCode
from webapps.payments.views.account_management import (
    get_available_payout_method_types,
    get_available_fast_payout_method_types,
)
from webapps.pos.enums.bank_account_number_form import BANK_ACCOUNT_NUMBER_FORM
from webapps.stripe_integration.enums import FastPayoutStatus

_common_data = dict(  # pylint: disable=use-dict-literal
    payment_provider_code=PaymentProviderCode.STRIPE,
    fast_payouts_status=FastPayoutStatus.AVAILABLE,
    payments_enabled=True,
    kyc_errors=[],
    personal_details=PersonalDetails(
        first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
    ),
    company_details=None,
    provider_specific=StripeAccountDetails(
        account_type=StripeAccountType.CUSTOM,
        business_type=StripeAccountBusinessType.INDIVIDUAL,
        kyc_required_threshold=3_000_00,
    ),
    available_payout_method_types=get_available_payout_method_types(),
    available_fast_payout_method_types=get_available_fast_payout_method_types(
        FastPayoutStatus.AVAILABLE
    ),
    bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
    external_id='acct_123',
    user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
)

_common_payout_method_data = {
    'method_type': PayoutMethodType.BANK_ACCOUNT,
    'card': None,
    'bank_account': BankAccount(
        last_digits="1234",
        account_holder_name="John Smith",
        bank_name="Chase",
        routing_number="*********",
        country='us',
    ),
    'token': '12345',
    'is_default_for_regular_payouts': True,
    'is_default_for_fast_payouts': False,
    'has_fast_payout_capability': True,
}

_active_payout_methods_data = [
    PayoutMethodDetails(
        **_common_payout_method_data, status=PayoutMethodStatus.ACTIVE, error_code=None
    )
]
_payout_methods_data_with_error = [
    PayoutMethodDetails(
        **_common_payout_method_data,
        status=PayoutMethodStatus.ERROR,
        error_code=PayoutMethodErrorCode.ERROR,
    )
]
_active_payout_methods_data_multiple_methods_one_not_used = [
    PayoutMethodDetails(
        **_common_payout_method_data, status=PayoutMethodStatus.ACTIVE, error_code=None
    ),
    PayoutMethodDetails(
        **(_common_payout_method_data | {'is_default_for_regular_payouts': False}),
        status=PayoutMethodStatus.ACTIVE,
        error_code=None,
    ),
]

_common_data_with_valid_payout_method = {
    **_common_data,
    "payout_methods": _active_payout_methods_data,
}

_common_data_pl = _common_data | {
    'bank_account_number_form': BANK_ACCOUNT_NUMBER_FORM.get(Country.PL)
}

dummy_provider_account_details_verified = ProviderAccountDetails(
    status=ProviderAccountHolderStatus.VERIFIED,
    kyc_verified_at_least_once=True,
    payouts_enabled=True,
    **_common_data_with_valid_payout_method,
)
dummy_provider_account_details_verification_pending = ProviderAccountDetails(
    status=ProviderAccountHolderStatus.VERIFICATION_PENDING,
    kyc_verified_at_least_once=False,
    payouts_enabled=True,
    **_common_data_with_valid_payout_method,
)
dummy_provider_account_details_verified_payouts_disabled = ProviderAccountDetails(
    status=ProviderAccountHolderStatus.VERIFIED,
    kyc_verified_at_least_once=True,
    payouts_enabled=False,
    **_common_data_with_valid_payout_method,
)
dummy_provider_account_details_verified_payout_method_with_error = ProviderAccountDetails(
    status=ProviderAccountHolderStatus.VERIFIED,
    kyc_verified_at_least_once=True,
    payouts_enabled=True,
    payout_methods=_payout_methods_data_with_error,
    **_common_data,
)
dummy_provider_account_details_verified_two_external_accounts_one_not_used = ProviderAccountDetails(
    status=ProviderAccountHolderStatus.VERIFIED,
    kyc_verified_at_least_once=True,
    payouts_enabled=True,
    payout_methods=_active_payout_methods_data_multiple_methods_one_not_used,
    **_common_data,
)
dummy_provider_account_details_verified_two_external_accounts_one_not_used_pl = (
    ProviderAccountDetails(
        status=ProviderAccountHolderStatus.VERIFIED,
        kyc_verified_at_least_once=True,
        payouts_enabled=True,
        payout_methods=_active_payout_methods_data_multiple_methods_one_not_used,
        **_common_data_pl,
    )
)
