from rest_framework import serializers

from django.utils.translation import gettext as _

from lib.french_certification.utils import french_certification_enabled
from lib.payment_providers.enums import ExternalPaymentMethodType
from lib.point_of_sale.entities import ExtendedBasketEntity
from lib.tools import format_currency
from lib.point_of_sale.enums import (
    BasketCustomerPaymentAction,
    BasketPaymentType,
    BasketStatus,
    BasketElementType,
)
from lib.serializers import PaginatorSerializer
from webapps.business.ports.business_details import BusinessDetailsEntity, BusinessDetailsPort
from webapps.point_of_sale.ports import BasketPaymentPort
from webapps.booking.ports import BookingPort
from webapps.pos.ports import POSPort


class CustomerBasketQueryParamsSerializer(PaginatorSerializer):
    """Serializer for query parameters in CustomerListBasketsView"""

    basket_status = serializers.ChoiceField(
        choices=BasketStatus.choices(),
        required=False,
        allow_null=True,
    )

    def validate_basket_status(self, value):
        if value is not None:
            return BasketStatus(value)
        return None


class ExternalPaymentMethodSerializer(serializers.Serializer):
    partner = serializers.ChoiceField(
        choices=ExternalPaymentMethodType.choices(),
        required=True,
    )
    token = serializers.CharField(required=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        partner: str = attrs['partner']
        attrs['partner'] = ExternalPaymentMethodType(partner)

        if not BookingPort.check_tokenized_payments_for_payment_creation(partner):
            raise serializers.ValidationError(
                _('Payment is not possible'), code='payment_not_possible'
            )

        return attrs


class CancelPaymentRequestSerializer(serializers.Serializer):
    def validate(self, attrs):
        if not BasketPaymentPort.is_customer_action_allowed(
            basket_payment_id=self.instance.id,
            user_id=self.context['user'].id,
            action=BasketCustomerPaymentAction.CANCEL_PAYMENT,
        ):
            raise serializers.ValidationError(_('Action is not allowed'))

        return attrs


class MakePaymentRequestSerializer(serializers.Serializer):
    external_payment_method = ExternalPaymentMethodSerializer(required=False)
    tokenized_payment_method_id = serializers.CharField(required=False)

    def validate(self, attrs):
        tokenized_payment_method_id = attrs.get('tokenized_payment_method_id')
        external_payment_method = attrs.get('external_payment_method')

        if bool(tokenized_payment_method_id) == bool(external_payment_method):  # XOR
            raise serializers.ValidationError(
                'Payment_method xor external_payment_method should be provided',
                code='missing_method',
            )

        if not BasketPaymentPort.is_customer_action_allowed(
            basket_payment_id=self.instance.id,
            user_id=self.context['user'].id,
            action=BasketCustomerPaymentAction.MAKE_PAYMENT,
        ):
            raise serializers.ValidationError(_('Action is not allowed'))

        is_pba_amount_valid, minimal_amount = BasketPaymentPort.validate_pba_amount(
            amount=self.instance.amount,
        )
        if not is_pba_amount_valid:
            raise serializers.ValidationError(
                {
                    'payment_rows': _('Amount must be greater or equal to {}').format(
                        format_currency(minimal_amount),
                    )
                },
                code='pay_by_app_minimal_amount',
            )

        return attrs


class BusinessDetailsSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    address = serializers.CharField()

    def to_representation(self, instance: BusinessDetailsEntity):
        return {
            'id': instance.id,
            'name': instance.name,
            'address': instance.address,
        }


class AmountSerializer(serializers.Serializer):
    amount = serializers.IntegerField()  # i.e. 123 (equals 1,23 of given currency)
    amount_formatted = serializers.CharField()  # i.e. "1,23 €"

    def to_representation(self, instance: int):  # in cents
        return {
            'amount': instance,
            'amount_formatted': format_currency(instance / 100),
        }


class PaymentTypeSerializer(serializers.Serializer):
    code = serializers.CharField()
    label = serializers.CharField()

    def to_representation(self, instance: BasketPaymentType):
        return {
            'code': instance,
            'label': instance,
        }


class BasketStatusSerializer(serializers.Serializer):
    code = serializers.CharField()
    label = serializers.CharField()

    def to_representation(self, instance: BasketStatus):
        return {
            'code': instance.value,
            'label': instance.label,
        }


class BasketSummary(serializers.Serializer):
    # summary of payments in the basket
    total = AmountSerializer()
    already_paid = AmountSerializer()
    remaining = AmountSerializer()
    payment_type = PaymentTypeSerializer()
    status = BasketStatusSerializer()


class CustomerBasketSerializer(serializers.Serializer):
    """
    Basic serializer (i.e., for a list view) for *customers*
    """

    id = serializers.UUIDField()
    created = serializers.DateTimeField()
    public_id = serializers.CharField()  # receipt id in old pos
    basket_number = serializers.CharField()  # receipt_number in old pos
    business_details = BusinessDetailsSerializer()
    payments_summary = BasketSummary()

    def to_representation(self, instance: ExtendedBasketEntity):
        # Get the business details using the port
        business_details = BusinessDetailsPort.get_business_details(instance.business_id)

        # Create the summary data using serializers
        payments_summary = {
            'total': AmountSerializer(instance.total).data,
            'already_paid': AmountSerializer(instance.already_paid).data,
            'remaining': AmountSerializer(instance.remaining).data,
            'payment_type': (
                PaymentTypeSerializer(instance.payment_type).data if instance.payment_type else None
            ),
            'status': BasketStatusSerializer(instance.status).data if instance.status else None,
        }

        # Return the complete representation
        return {
            'id': instance.id,
            'created': instance.created,
            'public_id': instance.public_id,
            'basket_number': instance.basket_number,
            'business_details': (
                BusinessDetailsSerializer(business_details).data if business_details else None
            ),
            'payments_summary': payments_summary,
        }


class CustomerBasketDetailsSerializer(CustomerBasketSerializer):
    """
    Detailed serializer for a customer's basket, extending CustomerBasketSerializer.
    """

    def to_representation(
        self,
        instance: ExtendedBasketEntity,
    ):
        base_representation = super().to_representation(instance)

        payments = [
            {
                'id': payment.id,
                'status': payment.status.value,
                'payment_type': payment.type.value,
                'amount': payment.amount / 100,
                'amount_formatted': format_currency(payment.amount / 100),
                'created': payment.created,
            }
            for payment in instance.payments
        ]

        items = [
            {
                'id': item.id,
                'name_line_1': item.name_line_1,
                'name_line_2': item.name_line_2,
                'quantity': item.quantity,
                'discount_rate': item.discount_rate,
                'total': item.total,
                'total_formatted': format_currency(item.total / 100),
            }
            for item in instance.items
        ]

        pos = POSPort.get_pos_by_business_id(business_id=instance.business_id)

        return {
            **base_representation,
            'total_elements': self._get_elements_from_transaction(instance),
            'payments': payments,
            'items': items,
            'send_receipt_email_allowed': not french_certification_enabled(),
            'receipt_foot_line_1': pos.receipt_footer_line_1,
            'receipt_foot_line_2': pos.receipt_footer_line_2,
            'disclaimer': _("The above confirmation of sale is not a fiscal receipt."),
        }

    def _get_elements_from_transaction(
        self,
        instance: ExtendedBasketEntity,
    ) -> list[dict]:
        # Based on webapps.pos.calculations.calculate
        tips = sum(tip.amount for tip in instance.tips)
        subtotal = sum(item.total for item in instance.items)
        tax_total = sum(item.tax_amount for item in instance.items)
        discount = sum(item.total - item.discounted_total for item in instance.items)
        total = subtotal - discount + tax_total + tips

        return [
            {
                'label': BasketElementType.TOTAL,
                'amount': AmountSerializer(total).data,
            },
            {
                'label': BasketElementType.SUBTOTAL,
                'amount': AmountSerializer(subtotal).data,
            },
            {
                'label': BasketElementType.TIPS,
                'amount': AmountSerializer(tips).data,
            },
            {
                'label': BasketElementType.DISCOUNT,
                'amount': AmountSerializer(discount).data,
            },
            {
                'label': BasketElementType.TAXES_AND_FEES,
                'amount': AmountSerializer(tax_total).data,
            },
        ]
