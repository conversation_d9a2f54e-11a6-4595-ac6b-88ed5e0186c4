from django.utils.translation import gettext_lazy as _

from webapps.booking.no_show_protection.business_appointment.validation_rules import validation_rule
from webapps.pos.adapters import can_have_business_appointment_deposit, BusinessId


class BusinessCanNotPayDepositError(validation_rule.ValidationError):
    pass


class BusinessCanPayDeposit(validation_rule.ValidationRule):
    def __init__(self, business_id: int):
        super().__init__()
        self.business_id = business_id

    def is_valid(self) -> bool:
        if can_have_business_appointment_deposit(BusinessId(self.business_id)):
            return True
        self._errors = [
            BusinessCanNotPayDepositError(
                _("A payment method allowing business appointment deposits needs to be activated.")
            )
        ]
        return False
