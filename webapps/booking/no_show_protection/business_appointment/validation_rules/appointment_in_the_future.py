from django.utils.translation import gettext_lazy as _

from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.booking.no_show_protection.business_appointment.validation_rules import validation_rule


class AppointmentNotInTheFutureError(validation_rule.ValidationError):
    pass


class AppointmentInTheFuture(validation_rule.ValidationRule):

    def __init__(self, appointment: Appointment):
        super().__init__()
        self.appointment = appointment

    def is_valid(self) -> bool:
        if tznow() < self.appointment.booked_from:
            return True
        self._errors = [
            AppointmentNotInTheFutureError(
                _("Appointment must be scheduled for a future date and time.")
            )
        ]
        return False
