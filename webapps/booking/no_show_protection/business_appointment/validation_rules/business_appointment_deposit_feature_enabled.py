from django.utils.translation import gettext_lazy as _

from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.payment import FeatureDepositOnBusinessAppointment
from webapps.booking.no_show_protection.business_appointment.validation_rules import validation_rule
from webapps.pos.adapters import get_pos_ba_feature_enabled


class BusinessAppointmentDepositNotEnabledError(validation_rule.ValidationError):
    pass


class BusinessAppointmentDepositFeatureEnabled(validation_rule.ValidationRule):

    def __init__(self, business_id: int):
        super().__init__()
        self.business_id = business_id
        self._business_id = business_id

    def is_valid(self) -> bool:
        if bool(
            FeatureDepositOnBusinessAppointment(
                UserData(subject_key=self._business_id),
            )
            and get_pos_ba_feature_enabled(business_id=self.business_id)
        ):
            return True
        self._errors = [
            BusinessAppointmentDepositNotEnabledError(
                _("Deposits for business appointments aren't currently enabled.")
            )
        ]
