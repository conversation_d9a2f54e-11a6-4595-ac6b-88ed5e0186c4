from django.utils.translation import gettext_lazy as _

from webapps.booking.models import Appointment
from webapps.booking.no_show_protection.business_appointment.validation_rules import validation_rule


class AppointmentIsRepeatingError(validation_rule.ValidationError):
    pass


class AppointmentIsNotRepeating(validation_rule.ValidationRule):

    def __init__(self, appointment: Appointment):
        super().__init__()
        self._appointment = appointment

    def is_valid(self) -> bool:
        if self._appointment.repeating_id is None:
            return True

        self._errors = [AppointmentIsRepeatingError(_("Appointment can't be set as recurring."))]
