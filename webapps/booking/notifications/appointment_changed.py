from django.utils.translation import gettext_lazy as _

from lib.tools import sget_v2
from webapps.booking.notifications.base import (
    AppointmentBusinessNotification,
    SubBookingStafferNotification,
)
from webapps.notification.base import PopupTemplate
from webapps.notification.channels import <PERSON>up<PERSON>hannel, PushChannel, push_from_popup
from webapps.notification.enums import (
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
)

__all__ = [
    'AppointmentRescheduleNotification',
    'SubBookingRescheduleNotification',
    'AppointmentRescheduleAcceptedNotification',
    'SubBookingRescheduleAcceptedNotification',
    'AppointmentCancelNotificationWithoutPush',
    'AppointmentCancelNotificationWithPush',
    'SubBookingCancelNotificationWithoutPush',
    'SubBookingCancelNotificationWithPush',
    'AppointmentLateCancelNotification',
    'SubBookingLateCancelNotification',
    'AppointmentNoShowNotification',
    'SubBookingNoShowNotification',
    'AppointmentAutoRefundNotification',
    'AppointmentLateCancellationRefundNotification',
    'SubbookingAutoRefundNotification',
    'SubbookingLateCancellationRefundNotification',
]

APPOINTMENT_CANCELLATION_MESSAGE = [
    _('Appointment cancellation'),
    '{booking_date} • {booking_time}',
    '{customer_name} • {service_name} {addons}',
]

LATE_APPOINTMENT_CANCELLATION_MESSAGE = [
    _('Late appointment cancellation'),
    '{booking_date} • {booking_time}',
    '{customer_name} • {service_name} {addons}',
]

APPOINTMENT_AUTO_REFUND_MESSAGE = [
    _('Appointment cancellation'),
    _("We've issued the client's full refund."),
    '{customer_name} • {service_name} {addons}',
]

LATE_APPOINTMENT_CANCELLATION_REFUND_MESSAGE = [
    _('Appointment cancellation'),
    _('Late cancellation: review and decide on the refund.'),
    '{customer_name} • {service_name} {addons}',
]


class CancellationTemplatesMixin:
    def get_messages(self):
        if sget_v2(self, ['late_message']) is True:
            return LATE_APPOINTMENT_CANCELLATION_MESSAGE

        return APPOINTMENT_CANCELLATION_MESSAGE

    @property
    def popup_template(self) -> PopupTemplate:
        crucial = False
        icon = NotificationIcon.CANCELLATION

        if sget_v2(self, ['late_message']) is True:
            crucial = True
            icon = NotificationIcon.NO_SHOW

        return PopupTemplate(
            crucial=crucial,
            icon=icon,
            relevance=1,
            group=NotificationGroup.NOTIFICATION,
            size=NotificationSize.NORMAL,
            messages=self.get_messages(),
            photo='{customer_photo_url}',
        )

    @property
    def push_template(self):
        return push_from_popup(self.popup_template)


class AppointmentRescheduleNotification(AppointmentBusinessNotification):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.APPOINTMENT,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('An appointment has been rescheduled'),
            '{booking_date} • {booking_time}',
            '{customer_name} • {service_name} {addons}',
        ],
        photo='{customer_photo_url}',
    )
    exclude_subbooking_staffers = True

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.appointment.id},{self.appointment.booked_from}'


class SubBookingRescheduleNotification(SubBookingStafferNotification):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.APPOINTMENT,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('An appointment has been rescheduled'),
            '{booking_date} • {booking_time}',
            '{customer_name} • {service_name} {addons}',
        ],
        photo='{customer_photo_url}',
    )

    @property
    def identity(self):
        return f'{self.notif_type},{self.subbooking.id},{self.subbooking.booked_from}'


class AppointmentRescheduleAcceptedNotification(AppointmentBusinessNotification):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.APPOINTMENT,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('Appointment update has been accepted'),
            '{booking_date} • {booking_time}',
            '{customer_name} • {service_name} {addons}',
        ],
        photo='{customer_photo_url}',
    )
    exclude_subbooking_staffers = True


class SubBookingRescheduleAcceptedNotification(SubBookingStafferNotification):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.APPOINTMENT,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('Appointment update has been accepted'),
            '{booking_date} • {booking_time}',
            '{customer_name} • {service_name} {addons}',
        ],
        photo='{customer_photo_url}',
    )


class AppointmentCancelNotificationWithoutPush(
    CancellationTemplatesMixin, AppointmentBusinessNotification
):
    channels = (PopupChannel,)
    exclude_subbooking_staffers = True


class AppointmentCancelNotificationWithPush(
    CancellationTemplatesMixin, AppointmentBusinessNotification
):
    channels = (PopupChannel, PushChannel)
    exclude_subbooking_staffers = True


class SubBookingCancelNotificationWithoutPush(
    CancellationTemplatesMixin, SubBookingStafferNotification
):
    channels = (PopupChannel,)


class SubBookingCancelNotificationWithPush(
    CancellationTemplatesMixin, SubBookingStafferNotification
):
    channels = (PopupChannel, PushChannel)


class AppointmentLateCancelNotification(
    CancellationTemplatesMixin, AppointmentBusinessNotification
):
    channels = (PopupChannel, PushChannel)
    exclude_subbooking_staffers = True
    late_message = True


class SubBookingLateCancelNotification(CancellationTemplatesMixin, SubBookingStafferNotification):
    channels = (PopupChannel, PushChannel)
    late_message = True


class AppointmentNoShowNotification(AppointmentBusinessNotification):
    channels = (PopupChannel, PushChannel)
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.NO_SHOW,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('Your client is a no-show'),
            '{booking_date} • {booking_time}',
            '{customer_name} • {service_name} {addons}',
        ],
        photo='{customer_photo_url}',
    )
    push_template = push_from_popup(popup_template)
    exclude_subbooking_staffers = True


class SubBookingNoShowNotification(SubBookingStafferNotification):
    channels = (PopupChannel, PushChannel)
    popup_template = PopupTemplate(
        crucial=True,
        icon=NotificationIcon.NO_SHOW,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('Your client is a no-show'),
            '{booking_date} • {booking_time}',
            '{customer_name} • {service_name} {addons}',
        ],
        photo='{customer_photo_url}',
    )
    push_template = push_from_popup(popup_template)


class CancellationRefundTemplatesMixin:
    message = APPOINTMENT_CANCELLATION_MESSAGE

    @property
    def popup_template(self) -> PopupTemplate:
        crucial = False
        icon = NotificationIcon.CANCELLATION

        return PopupTemplate(
            crucial=crucial,
            icon=icon,
            relevance=1,
            group=NotificationGroup.NOTIFICATION,
            size=NotificationSize.NORMAL,
            messages=self.message,
            photo='{customer_photo_url}',
        )

    @property
    def push_template(self):
        return push_from_popup(self.popup_template)


class AppointmentAutoRefundNotification(
    CancellationRefundTemplatesMixin,
    AppointmentBusinessNotification,
):
    channels = (PopupChannel, PushChannel)
    exclude_subbooking_staffers = True
    message = APPOINTMENT_AUTO_REFUND_MESSAGE


class AppointmentLateCancellationRefundNotification(
    CancellationRefundTemplatesMixin,
    AppointmentBusinessNotification,
):
    channels = (PopupChannel, PushChannel)
    exclude_subbooking_staffers = True
    message = LATE_APPOINTMENT_CANCELLATION_REFUND_MESSAGE


class SubbookingAutoRefundNotification(
    CancellationRefundTemplatesMixin,
    SubBookingStafferNotification,
):
    channels = (PopupChannel, PushChannel)
    message = APPOINTMENT_AUTO_REFUND_MESSAGE


class SubbookingLateCancellationRefundNotification(
    CancellationRefundTemplatesMixin,
    SubBookingStafferNotification,
):
    channels = (PopupChannel, PushChannel)
    message = LATE_APPOINTMENT_CANCELLATION_REFUND_MESSAGE
