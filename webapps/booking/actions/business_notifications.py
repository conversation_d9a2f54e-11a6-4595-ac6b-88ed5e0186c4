from collections import defaultdict
from datetime import timedel<PERSON>
from logging import getLogger

from django.conf import settings

from lib.events import lazy_event_receiver
from lib.feature_flag.bug import SendPushAfterAppointmentCancellationFlag
from lib.tools import tznow
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.events import (
    appointment_changed_by_customer_event,
    appointment_no_show_by_business_event,
    appointment_status_changed_by_customer_event,
    customer_appointment_created_event,
)
from webapps.booking.models import Appointment
from webapps.booking.notifications.appointment_changed import (
    AppointmentCancelNotificationWithoutPush,
    AppointmentCancelNotificationWithPush,
    AppointmentLateCancelNotification,
    AppointmentNoShowNotification,
    AppointmentRescheduleAcceptedNotification,
    AppointmentRescheduleNotification,
    AppointmentAutoRefundNotification,
    AppointmentLateCancellationRefundNotification,
    SubbookingAutoRefundNotification,
    SubbookingLateCancellationRefundNotification,
    SubBookingCancelNotificationWithoutPush,
    SubBookingCancelNotificationWithPush,
    SubBookingLateCancelNotification,
    SubBookingNoShowNotification,
    SubBookingRescheduleAcceptedNotification,
    SubBookingRescheduleNotification,
)
from webapps.booking.notifications.appointment_created import (
    AppointmentAwaitingNotification,
    SubBookingAwaitingNotification,
    # NewFromMarketplaceManagerNotification,
)
from webapps.business.notifications.prepayments import (
    ClientNoShowSuggestion,
    LateCancellationSuggestion,
    ManualNoShowSuggestion,
    has_prepayments,
)

__all__ = []

from webapps.booking.notifications.tools import get_notification_class_for_payment


if settings.POPUP_NOTIFICATIONS_ENABLED:
    __all__ = [
        'customer_appointment_created_notify',
        'appointment_changed_by_customer',
        'appointment_status_changed_by_customer',
    ] + (
        [
            'appointment_no_show_by_business',
        ]
        if settings.POPUP_PHASE3
        else []
    )

    log = getLogger('booksy.booking_actions')

    def notify_only_first_staffer_subbooking(subbooking_notification, subbookings):
        staffer_subbookings_dict = defaultdict(list)
        for subbooking in subbookings:
            staffer_subbookings_dict[subbooking.staffer_id].append(subbooking)

        for staffer_subbokings in staffer_subbookings_dict.values():
            subbooking_notification(staffer_subbokings[0]).send()

    @lazy_event_receiver(customer_appointment_created_event)
    def customer_appointment_created_notify(instance, **__):
        appointment = instance
        wrapper = AppointmentWrapper(list(appointment.bookings.active().prefetch_all()))

        if appointment.booked_from <= tznow():
            return

        # skip deleted appointments
        if appointment.deleted:
            return

        if appointment.status == Appointment.STATUS.UNCONFIRMED:
            AppointmentAwaitingNotification(appointment).send()
            notify_only_first_staffer_subbooking(
                SubBookingAwaitingNotification, appointment.subbookings
            )
        else:
            payment, _deposit = wrapper.get_payment_and_deposit()
            appointment_notification, subbooking_notification = get_notification_class_for_payment(
                payment
            )
            appointment_notification(appointment).send()
            notify_only_first_staffer_subbooking(subbooking_notification, appointment.subbookings)

    @lazy_event_receiver(appointment_changed_by_customer_event)
    def appointment_changed_by_customer(instance, **__):
        appointment = instance
        AppointmentRescheduleNotification(appointment).send()
        notify_only_first_staffer_subbooking(
            SubBookingRescheduleNotification, appointment.subbookings
        )

    @lazy_event_receiver(appointment_status_changed_by_customer_event)
    def appointment_status_changed_by_customer(
        instance,
        status=None,
        previous_status=None,
        refundable=False,
        is_auto_refund_possible=False,
        **__,
    ):
        # pylint: disable=too-many-branches
        appointment = instance
        if status == Appointment.STATUS.ACCEPTED and previous_status == Appointment.STATUS.PROPOSED:
            AppointmentRescheduleAcceptedNotification(appointment).send()
            notify_only_first_staffer_subbooking(
                SubBookingRescheduleAcceptedNotification, appointment.subbookings
            )

        if status == Appointment.STATUS.CANCELED:
            if refundable:
                if is_auto_refund_possible:
                    AppointmentAutoRefundNotification(appointment).send()
                    notify_only_first_staffer_subbooking(
                        SubbookingAutoRefundNotification, appointment.subbookings
                    )
                else:
                    AppointmentLateCancellationRefundNotification(appointment).send()
                    notify_only_first_staffer_subbooking(
                        SubbookingLateCancellationRefundNotification, appointment.subbookings
                    )
            elif appointment.booked_from < (
                tznow() + appointment.business.booking_max_modification_time
            ):
                AppointmentLateCancelNotification(appointment).send()
                notify_only_first_staffer_subbooking(
                    SubBookingLateCancelNotification, appointment.subbookings
                )

                if settings.POPUP_PHASE3:
                    if settings.POS__PAY_BY_APP and settings.POS__PREPAYMENTS:
                        if not has_prepayments(appointment):
                            LateCancellationSuggestion(appointment).schedule(
                                when=tznow() + timedelta(minutes=3)
                            )
            else:
                if SendPushAfterAppointmentCancellationFlag():
                    AppointmentCancelNotificationWithPush(appointment).send()
                    notify_only_first_staffer_subbooking(
                        SubBookingCancelNotificationWithPush, appointment.subbookings
                    )
                else:
                    AppointmentCancelNotificationWithoutPush(appointment).send()
                    notify_only_first_staffer_subbooking(
                        SubBookingCancelNotificationWithoutPush, appointment.subbookings
                    )
        elif status == Appointment.STATUS.NOSHOW:
            AppointmentNoShowNotification(appointment).send()
            notify_only_first_staffer_subbooking(
                SubBookingNoShowNotification, appointment.subbookings
            )

            if settings.POPUP_PHASE3:
                if settings.POS__PAY_BY_APP and settings.POS__PREPAYMENTS:
                    if not has_prepayments(appointment):
                        ClientNoShowSuggestion(appointment).schedule(
                            when=tznow() + timedelta(minutes=10)
                        )

    if settings.POPUP_PHASE3:

        @lazy_event_receiver(appointment_no_show_by_business_event)
        def appointment_no_show_by_business(instance, **__):
            if settings.POS__PAY_BY_APP and settings.POS__PREPAYMENTS:
                appointment = instance
                ManualNoShowSuggestion(appointment).send()
