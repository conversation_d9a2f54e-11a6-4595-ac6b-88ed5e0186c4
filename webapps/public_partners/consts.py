from collections import defaultdict
from django.conf import settings
from webapps.business.enums import StaffAccessLevels
from webapps.public_partners.enum import (
    PublicAPIVersionEnum,
    WebhookEnum,
    OAuth2ApplicationCategoryEnum,
    OAuth2InstallationStatusEnum,
)

SUBDOMAIN_NAME_TAKEN_ERROR_CODE = 'subdomain_name_taken'
SUBDOMAIN_NAME_INCORRECT_ERROR_CODE = 'subdomain_name_incorrect'

STAFF_ACCESS_LEVELS_ALL = (
    StaffAccessLevels.OWNER,
    StaffAccessLevels.MANAGER,
    StaffAccessLevels.RECEPTION,
    StaffAccessLevels.ADVANCED,
    StaffAccessLevels.STAFF,
)
STAFF_ACCESS_LEVELS_MIN_ADVANCED = (
    StaffAccessLevels.OWNER,
    StaffAccessLevels.MANAGER,
    StaffAccessLevels.RECEPTION,
    StaffAccessLevels.ADVANCED,
)
STAFF_ACCESS_LEVELS_MIN_RECEPTION = (
    StaffAccessLevels.OWNER,
    StaffAccessLevels.MANAGER,
    StaffAccessLevels.RECEPTION,
)
STAFF_ACCESS_LEVELS_MIN_MANAGER = (
    StaffAccessLevels.OWNER,
    StaffAccessLevels.MANAGER,
)
STAFF_ACCESS_LEVELS_MIN_OWNER = (StaffAccessLevels.OWNER,)

WEBHOOK_ATTEMPTS_LIMIT = 5

OAUTH2_INSTALLATION_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PAOAuth2InstallationSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PAOAuth2InstallationSerializerV04',
}

BUSINESS_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PABusinessSerializerV04',
    PublicAPIVersionEnum.V05: 'PABusinessSerializerV05',
    PublicAPIVersionEnum.DEFAULT: 'PABusinessSerializerV05',
}

RESOURCE_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PAResourceSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PAResourceSerializerV04',
}

SERVICE_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PAServiceSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PAServiceSerializerV04',
}

IMAGE_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PAImageSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PAImageSerializerV04',
}

CONSENT_FORM_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PAConsentFormSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PAConsentFormSerializerV04',
}

CONSENT_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PAAppointmentConsentSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PAAppointmentConsentSerializerV04',
}

CUSTOMER_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PABusinessCustomerSerializerV04',
    PublicAPIVersionEnum.V06: 'PABusinessCustomerSerializerV06',
    PublicAPIVersionEnum.DEFAULT: 'PABusinessCustomerSerializerV06',
}

APPOINTMENT_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V04: 'PABusinessAppointmentSerializerV04',
    PublicAPIVersionEnum.DEFAULT: 'PABusinessAppointmentSerializerV04',
}

CUSTOMER_CLAIM_LOG_WEBHOOK_PAYLOAD_VERSION_MAPPER = {
    PublicAPIVersionEnum.V06: 'PAClaimLogSerializerV06',
    PublicAPIVersionEnum.DEFAULT: 'PAClaimLogSerializerV06',
}

WEBHOOK_EVENT_TO_SERIALIZER_MAPPER = {
    WebhookEnum.APPLICATION_INSTALLATION_CREATED: OAUTH2_INSTALLATION_WEBHOOK_PAYLOAD_VERSION_MAPPER,  # pylint: disable=line-too-long
    WebhookEnum.APPLICATION_INSTALLATION_UPDATED: OAUTH2_INSTALLATION_WEBHOOK_PAYLOAD_VERSION_MAPPER,  # pylint: disable=line-too-long
    WebhookEnum.APPLICATION_INSTALLATION_DELETED: None,
    WebhookEnum.BUSINESS_CREATED: BUSINESS_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.BUSINESS_UPDATED: BUSINESS_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.BUSINESS_DELETED: None,
    WebhookEnum.RESOURCE_CREATED: RESOURCE_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.RESOURCE_UPDATED: RESOURCE_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.RESOURCE_DELETED: None,
    WebhookEnum.SERVICE_CREATED: SERVICE_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.SERVICE_UPDATED: SERVICE_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.SERVICE_DELETED: None,
    WebhookEnum.IMAGE_CREATED: IMAGE_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.IMAGE_UPDATED: IMAGE_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.IMAGE_DELETED: None,
    WebhookEnum.CONSENT_FORM_CREATED: CONSENT_FORM_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CONSENT_FORM_UPDATED: CONSENT_FORM_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CONSENT_FORM_DELETED: None,
    WebhookEnum.CONSENT_CREATED: CONSENT_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CONSENT_UPDATED: CONSENT_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CONSENT_DELETED: None,
    WebhookEnum.CUSTOMER_CREATED: CUSTOMER_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CUSTOMER_UPDATED: CUSTOMER_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CUSTOMER_DELETED: None,
    WebhookEnum.APPOINTMENT_CREATED: APPOINTMENT_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.APPOINTMENT_UPDATED: APPOINTMENT_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.APPOINTMENT_DELETED: None,
    WebhookEnum.CUSTOMER_CLAIM_LOG_CREATED: CUSTOMER_CLAIM_LOG_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CUSTOMER_CLAIM_LOG_UPDATED: CUSTOMER_CLAIM_LOG_WEBHOOK_PAYLOAD_VERSION_MAPPER,
    WebhookEnum.CUSTOMER_CLAIM_LOG_DELETED: None,
}


STATUS_TRANSITIONS = defaultdict(
    lambda: {
        OAuth2InstallationStatusEnum.ACTIVE: (
            OAuth2InstallationStatusEnum.DISABLED,
            OAuth2InstallationStatusEnum.BLOCKED,
        ),
        OAuth2InstallationStatusEnum.PAID: (
            OAuth2InstallationStatusEnum.ACTIVE,
            OAuth2InstallationStatusEnum.BLOCKED,
        ),
        OAuth2InstallationStatusEnum.SUSPENDED: (
            OAuth2InstallationStatusEnum.PAID,
            OAuth2InstallationStatusEnum.BLOCKED,
        ),
        OAuth2InstallationStatusEnum.DISABLED: (
            OAuth2InstallationStatusEnum.ACTIVE,
            OAuth2InstallationStatusEnum.SUSPENDED,
            OAuth2InstallationStatusEnum.BLOCKED,
        ),
        OAuth2InstallationStatusEnum.BLOCKED: (
            OAuth2InstallationStatusEnum.ACTIVE,
            OAuth2InstallationStatusEnum.PAID,
            OAuth2InstallationStatusEnum.SUSPENDED,
            OAuth2InstallationStatusEnum.DISABLED,
        ),
    }
)

OAUTH2_APPLICATIONS_ADMIN_STATUS_TRANSITIONS = defaultdict(lambda: STATUS_TRANSITIONS['default'])
OAUTH2_APPLICATIONS_ADMIN_STATUS_TRANSITIONS[settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID] = {
    OAuth2InstallationStatusEnum.PAID: (
        OAuth2InstallationStatusEnum.PENDING_CANCELLATION,
        OAuth2InstallationStatusEnum.CHURNED,
    ),
    OAuth2InstallationStatusEnum.PENDING_CANCELLATION: (
        OAuth2InstallationStatusEnum.PAID,
        OAuth2InstallationStatusEnum.CHURNED,
        OAuth2InstallationStatusEnum.OVERDUE_BLOCKED,
    ),
    OAuth2InstallationStatusEnum.CHURNED: (OAuth2InstallationStatusEnum.PAID,),
    OAuth2InstallationStatusEnum.OVERDUE_BLOCKED: (
        OAuth2InstallationStatusEnum.PAID,
        OAuth2InstallationStatusEnum.PENDING_CANCELLATION,
        OAuth2InstallationStatusEnum.CHURNED,
    ),
}

STATUS_TRANSITIONS[settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID] = {
    OAuth2InstallationStatusEnum.ACTIVE: (
        OAuth2InstallationStatusEnum.PAID,
        OAuth2InstallationStatusEnum.DISABLED,
    ),
    OAuth2InstallationStatusEnum.PAID: (
        OAuth2InstallationStatusEnum.PENDING_CANCELLATION,
        OAuth2InstallationStatusEnum.OVERDUE_BLOCKED,
    ),
    OAuth2InstallationStatusEnum.PENDING_CANCELLATION: (
        OAuth2InstallationStatusEnum.OVERDUE_BLOCKED,
        OAuth2InstallationStatusEnum.CHURNED,
    ),
    OAuth2InstallationStatusEnum.OVERDUE_BLOCKED: (
        OAuth2InstallationStatusEnum.PAID,
        OAuth2InstallationStatusEnum.CHURNED,
    ),
    OAuth2InstallationStatusEnum.CHURNED: (
        OAuth2InstallationStatusEnum.PAID,
        OAuth2InstallationStatusEnum.DISABLED,
    ),
}

OAUTH2_APP_CATEGORIES = {
    OAuth2ApplicationCategoryEnum.MEDICAL,
}
