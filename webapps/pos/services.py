import uuid
from decimal import Decimal
from typing import (
    Optional,
    Union,
)

from django.db import transaction
from django.db.models import Q, QuerySet

from lib.feature_flag.feature import CancellationFeeFullAmountAuthorizationFlag
from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payment_providers.entities import RecurringModel
from lib.payment_providers.enums import (
    PaymentMethodType as PaymentProviderPaymentMethodType,
    PaymentStatus,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketItemTaxType,
    BasketItemType,
    BasketPaymentAnalyticsTrigger,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketTipSource,
    BasketTipType,
    BasketType,
    CancellationFeeAuthStatus,
    PaymentMethodType,
)
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.serializers import safe_get
from lib.tools import (
    minor_unit,
    quick_assert,
    tznow,
)
from webapps.adyen.exceptions import ThreeDSecureAuthenticationRequired
from webapps.booking.enums import AppointmentTypeChoices
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.commission.models import Commission
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_providers.models import Payment
from webapps.point_of_sale.exceptions import BasketPaymentNotFound
from webapps.point_of_sale.models import (
    Basket,
    BasketItem,
    BasketPayment,
    BasketTip,
    CancellationFeeAuth,
)
from webapps.point_of_sale.services.basket import BasketService
from webapps.point_of_sale.services.basket_item import BasketItemService
from webapps.point_of_sale.services.basket_payment import BasketPaymentService
from webapps.point_of_sale.services.basket_tip import BasketTipService
from webapps.point_of_sale.services.cancellation_fee_auth import CancellationFeeAuthService
from webapps.pos import calculations
from webapps.pos.enums import (
    CARD_TYPE__APPLE_PAY,
    CARD_TYPE__BLIK,
    CARD_TYPE__GOOGLE_PAY,
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status,
    CARD_TYPE__KEYED_IN_PAYMENT,
)
from webapps.pos.enums.consts import (
    PAYMENT_PROVIDER_CODE_INTO_PAYMENT_PROVIDER_ENUM_MAP,
    PAYMENT_TYPE_ENUM_INTO_PAYMENT_METHOD_TYPE_MAP,
    PAYMENT_TYPE_ENUM_INTO_PAYMENT_PROVIDER_CODE_MAP,
    PAYMENT_TYPE_ENUM_INTO_POS_PLAN_PAYMENT_TYPE_ENUM_MAP,
    RECEIPT_STATUS_INTO_BASKET_PAYMENT_STATUS_MAP,
    RECEIPT_STATUS_INTO_BASKET_PAYMENT_TYPE_MAP,
    RECEIPT_STATUS_INTO_CANCELLATION_FEE_AUTH_STATUS_MAP,
    SIMPLE_TIP_INTO_BASKET_TIP_TYPE_MAP,
)
from webapps.pos.exceptions import BlikActionRequired
from webapps.pos.models import (
    Commission as CommissionV1,
    POS,
    PaymentMethod,
    PaymentRow,
    Transaction,
    TransactionRow,
    TransactionTip,
    TransactionTipRow,
)
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.models import StripeAccount
from webapps.stripe_integration.services import SynchronizeService


class PaymentRowService:
    @staticmethod
    def map_receipt_status(status) -> BasketPaymentStatus:
        """Maps receipt_status into BasketPaymentStatus"""
        return RECEIPT_STATUS_INTO_BASKET_PAYMENT_STATUS_MAP[status]

    @staticmethod
    def map_payment_type_code(payment_type_code: PaymentTypeEnum) -> PaymentMethodType:
        """Maps PaymentTypeEnum into PaymentMethodType"""
        return PAYMENT_TYPE_ENUM_INTO_PAYMENT_METHOD_TYPE_MAP[payment_type_code]

    @staticmethod
    def map_payment_type_into_provider(
        payment_type_code: PaymentTypeEnum,
    ) -> Optional[PaymentProviderCode]:
        """
        Maps PaymentTypeEnum into PaymentProviderCode. If payment type is offline, returns None
        """
        return PAYMENT_TYPE_ENUM_INTO_PAYMENT_PROVIDER_CODE_MAP.get(payment_type_code)

    @staticmethod
    def map_status_into_basket_payment_type(status) -> BasketPaymentType:
        """Maps receipt_status into BasketPaymentType."""
        return RECEIPT_STATUS_INTO_BASKET_PAYMENT_TYPE_MAP.get(status, BasketPaymentType.PAYMENT)

    @staticmethod
    def map_cf_receipt_status(status) -> CancellationFeeAuthStatus:
        """Maps receipt_status into CancellationFeeAuthStatus."""
        return RECEIPT_STATUS_INTO_CANCELLATION_FEE_AUTH_STATUS_MAP[status]

    @staticmethod
    def map_payment_provider_code_into_payment_provider_enum(
        payment_provider_code: PaymentProviderCode,
    ) -> PaymentProviderEnum:
        return PAYMENT_PROVIDER_CODE_INTO_PAYMENT_PROVIDER_ENUM_MAP[payment_provider_code]

    @staticmethod
    def get_tip_already_paid(payment_rows: list[Union[PaymentRow, dict]]) -> int:
        """Calculates tip which already have been paid."""
        if not payment_rows:
            return 0

        return minor_unit(
            sum(
                safe_get(row, ['tip_amount'])
                for row in payment_rows
                if safe_get(row, ['locked']) and safe_get(row, ['tip_amount'])
            )
        )


class TransactionTipService:
    @staticmethod
    def map_tip_type(tip_type: SimpleTip.TIP_TYPES) -> BasketTipType:
        """Maps SimpleTipType into BasketTipType"""
        return SIMPLE_TIP_INTO_BASKET_TIP_TYPE_MAP[tip_type]

    @staticmethod
    def _create_basket_tips_from_tip_rows(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        basket: Basket,
        tip: Union[TransactionTip, dict],
        tip_rows: list[dict] | QuerySet[TransactionTipRow],
        source: BasketTipSource,
        tip_already_paid: int,
        should_save_history: bool = True,
    ):
        """
        Creates BasketTip based on tip_rows. Splits prepayment tip in proportion of tip_rows.
        If prepayment tip was split or any new tip were added it will save BasketHistory.
        """
        tip_rows = list(tip_rows)  # ensure we deal with lsit
        any_unassigned_changes = BasketTipService.split_unassigned_tip(
            basket=basket,
            staffer_ids_with_rates=[
                (safe_get(tip_row, ['staffer_id']), safe_get(tip_row, ['rate']))
                for tip_row in tip_rows
            ],
        )
        save_history = should_save_history and any_unassigned_changes

        tip_amount = minor_unit(safe_get(tip, ['amount'])) - (tip_already_paid or 0)
        already_split_tip = 0

        last_tip_row = tip_rows[-1]

        for tip_row in tip_rows:
            if tip_row == last_tip_row:
                tip_row_amount = tip_amount - already_split_tip
            else:
                tip_row_amount = int(tip_amount * safe_get(tip_row, ['rate']) / 100)
                already_split_tip += tip_row_amount

            basket_tip = BasketTipService.create_basket_tip(
                basket=basket,
                staffer_id=safe_get(tip_row, ['staffer_id']),
                amount=tip_row_amount,
                source=source,
            )

            if basket_tip:
                save_history = True
                if isinstance(tip_row, dict):
                    tip_row['basket_tip_id'] = basket_tip.id
                else:
                    setattr(tip_row, 'basket_tip_id', basket_tip.id)
                    tip_row.save()

        if save_history:
            BasketService.save_history(basket)

    @staticmethod
    def _create_basket_tip_from_tip(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        basket: Basket,
        tip: Union[TransactionTip, dict],
        source: BasketTipSource,
        tip_already_paid: int,
        staffer_id: int | None = None,
        should_save_history: bool = True,
    ) -> BasketTip | None:
        """
        Creates BasketTip from tip object.
        Assign prepayment tip to staffer from tip object.
        """
        save_history = False

        if staffer_id:
            changed_items = BasketTipService.set_owner_of_unassigned_tips(
                basket=basket,
                staffer_id=staffer_id,
            )
            save_history = should_save_history and bool(
                changed_items
            )  # save history only if there was any change

        tip_amount = minor_unit(safe_get(tip, ['amount'])) - (tip_already_paid or 0)
        if not tip_amount:
            return

        basket_tip = BasketTipService.create_basket_tip(
            basket=basket,
            staffer_id=staffer_id,
            amount=tip_amount,
            source=source,
        )

        if basket_tip:
            save_history = should_save_history and True
            if isinstance(tip, dict):
                tip['basket_tip_id'] = basket_tip.id
            else:
                setattr(tip, 'basket_tip_id', basket_tip.id)
                tip.save()

        if save_history:
            BasketService.save_history(basket)

    @staticmethod
    def create_basket_tips(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        basket: Basket,
        txn: Transaction,
        tip: Union[TransactionTip, dict],
        tip_rows: list[Union[TransactionTipRow, dict]],
        source: BasketTipSource,
        tip_already_paid: int,
        should_save_history=True,
    ):
        """
        Creates BasketTip depending on given tip/tip+tip_rows.
        BasketTips are not editable, so first method deletes old ones, and then creates new.

        Method can be used either with created objects or in serializer -> it can work with dicts
        and object instances.

        Save is performed if Tip/TipRow instances are provided.

        :param basket: Basket obj
        :param txn: Transaction obj
        :param tip: dict or TransactionTip object
        :param tip_rows: dict or TransactionTipRow object
        :param source: Describes during what action tip was given
        :param tip_already_paid: Amount of tip already paid, for example during prepayment.
        :param should_save_history: If true basketHistory will be saved after BasketTips creation
        """

        if not tip and not tip_rows:
            return

        if tip_rows:
            TransactionTipService._create_basket_tips_from_tip_rows(
                basket=basket,
                tip=tip,
                tip_rows=tip_rows,
                tip_already_paid=tip_already_paid,
                source=source,
                should_save_history=should_save_history,
            )
        elif tip:
            staffer_id = TransactionService.get_staffer_id_from_txn_operator(txn)

            TransactionTipService._create_basket_tip_from_tip(
                basket=basket,
                tip=tip,
                staffer_id=staffer_id,
                tip_already_paid=tip_already_paid,
                source=source,
                should_save_history=should_save_history,
            )


class TransactionService:
    @staticmethod
    def map_transaction_type(transaction_type) -> BasketType:
        """Maps Transaction type into BasketType"""
        return {
            Transaction.TRANSACTION_TYPE__PAYMENT: BasketType.PAYMENT,
            Transaction.TRANSACTION_TYPE__CANCELLATION_FEE: BasketType.DEPOSIT,
        }[transaction_type]

    @staticmethod
    def get_payment_provider_code(
        pos: POS, payment_type_code: PaymentTypeEnum, txn: Transaction | None = None
    ) -> PaymentProviderCode:
        payment_provider_code = PaymentRowService.map_payment_type_into_provider(payment_type_code)

        if txn and not txn_refactor_stage2_enabled(txn):
            return payment_provider_code

        if pos.force_stripe_pba and payment_provider_code == PaymentProviderCode.ADYEN:
            stripe_account = StripeAccount.objects.filter(
                pos=pos,
            ).first()

            if stripe_account and stripe_account.kyc_verified_at_least_once:
                payment_provider_code = PaymentProviderCode.STRIPE

        return payment_provider_code

    @staticmethod
    def get_transaction(basket_payment_id=uuid.UUID) -> Transaction | None:
        payment_row = (
            PaymentRow.objects.select_related('receipt__transaction')
            .filter(basket_payment_id=basket_payment_id)
            .last()
        )
        if not payment_row:
            return None

        return payment_row.receipt.transaction

    @staticmethod
    def calculate_fees(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        pos: POS,
        payment_provider_code: PaymentProviderEnum,
        payment_type_code: PaymentTypeEnum,
    ) -> tuple[PaymentSplitsEntity, RefundSplitsEntity, DisputeSplitsEntity]:
        """
        Calculates fees basing on POSPlan from pos app.

        :param pos: POS object
        :param payment_provider_code: provider of payment
        :param payment_type_code: Code of PaymentType we want to calculate
        :param amount: amount of payment, minor_unit
        :return: tuple(PaymentSplitsEntity, RefundSplitEntity, DisputeSplitsEntity)
        """
        plan_type = PAYMENT_TYPE_ENUM_INTO_POS_PLAN_PAYMENT_TYPE_ENUM_MAP.get(
            (payment_type_code, payment_provider_code)
        )

        if plan_type is None:
            return (
                PaymentSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0),
                RefundSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0),
                DisputeSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0),
            )

        # TODO move to new structure https://booksy.atlassian.net/browse/POS-2238
        plan = pos.get_pos_plan(plan_type)

        return (
            PaymentSplitsEntity(
                percentage_fee=Decimal(plan.provision * 100),
                fixed_fee=minor_unit(plan.txn_fee),
            ),
            RefundSplitsEntity(
                percentage_fee=Decimal(plan.refund_provision * 100),
                fixed_fee=minor_unit(plan.refund_txn_fee),
            ),
            DisputeSplitsEntity(
                percentage_fee=Decimal(plan.chargeback_provision * 100),
                fixed_fee=minor_unit(plan.chargeback_txn_fee),
            ),
        )

    @staticmethod
    def get_basket_payment_source(txn: Transaction, pr: PaymentRow):
        if pr.payment_type.code == PaymentTypeEnum.PREPAYMENT:
            return BasketPaymentSource.PREPAYMENT
        if txn.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE:
            return BasketPaymentSource.CANCELLATION_FEE
        if pr.payment_type.code == PaymentTypeEnum.BOOKSY_PAY:
            return BasketPaymentSource.BOOKSY_PAY

        return BasketPaymentSource.PAYMENT

    @staticmethod
    def get_device_data_trigger(pr: PaymentRow) -> BasketPaymentAnalyticsTrigger:
        # pylint: disable=too-many-return-statements
        if pr.payment_type.code == PaymentTypeEnum.PREPAYMENT and pr.payment_link:
            return BasketPaymentAnalyticsTrigger.BUSINESS__PREPAYMENT_REQUEST
        if pr.payment_type.code == PaymentTypeEnum.PREPAYMENT:
            return BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH
        if pr.payment_type.code == PaymentTypeEnum.STRIPE_TERMINAL:
            return BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP
        if pr.payment_type.code == PaymentTypeEnum.PAY_BY_APP:
            return BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_CFP
        if pr.payment_type.code == PaymentTypeEnum.TAP_TO_PAY:
            return BasketPaymentAnalyticsTrigger.BUSINESS__TAP_TO_PAY
        if pr.payment_type.code == PaymentTypeEnum.BOOKSY_PAY:
            return BasketPaymentAnalyticsTrigger.CUSTOMER__BOOKSY_PAY
        if pr.payment_type.code == PaymentTypeEnum.BLIK:
            return BasketPaymentAnalyticsTrigger.BUSINESS__BLIK
        if pr.payment_type.code == PaymentTypeEnum.KEYED_IN_PAYMENT:
            return BasketPaymentAnalyticsTrigger.BUSINESS__KEYED_IN_PAYMENT
        return BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT

    @staticmethod
    def get_staffer_id_from_txn_operator(txn: Transaction) -> int | None:
        if hasattr(txn, '_staffer_id'):
            # annotate: webapps.pos.migrations_scripts._migrate_transaction_series
            return txn._staffer_id  # pylint: disable=protected-access

        # Active staffer first, if they are not existing, take inactive
        operator = (
            Resource.all_objects.filter(
                staff_user=txn.operator_id,
                type=Resource.STAFF,
                business_id=txn.pos.business_id,
            )
            .order_by('-active', '-id')
            .first()
        )
        return operator.id if operator else None

    @staticmethod
    def create_basket_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        basket: Basket,
        txn: Transaction,
        pr: PaymentRow,
        parent_basket_payment: Optional[BasketPayment] = None,
        payment_initialization: bool = True,
        payment_token: str = None,
    ) -> BasketPayment:
        """
        Creates basketPayment - converts data from old structure (PaymentRow, Transaction) into new
        BasketPayment.

        At the beginning it calculates fees.

        :param basket: Basket object
        :param txn: Transaction object
        :param pr: PaymentRow object, base for BasketPayment
        :param parent_basket_payment: when refund is created, we want to connect it to payment which
            is refunded
        :param payment_initialization: True if initialization in PaymentGateway app should
            be performed.

            Due to complications with Tips in customerApps, PBA payments
            are initialized later, after we are sure, that PaymentRow/BasketPayment won't change
            anymore. So every payment except PBA has payment_initialization = True.
            Offline methods

            FOr PBA TransactionService.get_fees_and_initialize_payment
        :param payment_token: payment token used for PaymentMethodType.KEYED_IN_PAYMENT

        :return: Created BasketPayment
        """

        payment_provider_code = TransactionService.get_payment_provider_code(
            pos=txn.pos, payment_type_code=pr.payment_type.code
        )

        if payment_initialization:
            payment_splits, refund_splits, dispute_splits = TransactionService.calculate_fees(
                pos=txn.pos,
                payment_provider_code=(
                    PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                        payment_provider_code
                    )
                ),
                payment_type_code=PaymentTypeEnum(pr.payment_type.code),
            )
        else:
            payment_splits, refund_splits, dispute_splits = None, None, None

        is_semi_auto_enabled = not (
            pr.payment_type.code == PaymentTypeEnum.PREPAYMENT
            and txn.pos.business.booking_mode == Business.BookingMode.SEMIAUTO
            and txn.appointment.type == AppointmentTypeChoices.CUSTOMER
        )
        auto_capture = (
            payment_provider_code == PaymentProviderCode.BOOKSY_GIFT_CARDS
        ) or is_semi_auto_enabled

        operator_id = TransactionService.get_staffer_id_from_txn_operator(txn)

        status = (
            BasketPaymentStatus.PENDING
            if payment_provider_code
            or (
                # Even though the 'Square' PaymentType doesn't specify a provider,
                # it operates as an "online" transaction. Consequently, we must await
                # a response from the SQUARE SDK and set the status to BasketPaymentStatus.Pending.
                pr.payment_type.code == PaymentTypeEnum.SQUARE
                and pr.status == receipt_status.PENDING
            )
            else BasketPaymentStatus.SUCCESS
        )

        assert txn_refactor_stage2_enabled(txn) is True
        return BasketPaymentService.initialize_basket_payment(
            basket=basket,
            basket_payment_type=PaymentRowService.map_status_into_basket_payment_type(pr.status),
            amount=minor_unit(pr.amount),
            payment_method_type=PaymentRowService.map_payment_type_code(pr.payment_type.code),
            payment_initialization=payment_initialization,
            status=status,
            auto_capture=auto_capture,
            source=TransactionService.get_basket_payment_source(txn, pr),
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
            payment_provider_code=payment_provider_code,
            user_id=(
                txn.customer_card.user.id
                if (txn.customer_card and txn.customer_card.user)
                else None
            ),
            operator_id=operator_id,
            parent_basket_payment=parent_basket_payment,
            payment_token=payment_token,
        )

    @staticmethod
    def get_fees_and_initialize_payment(
        pr: PaymentRow,
        payment_method: PaymentMethod,
    ):
        """
        Second part of initialization PBA payment.

        First method only creates BasketPayment for PBA Payment.
        Now fee calculations and initialization in PaymentGateway is performed.
        If payment_method has attribute 'token' means that is Apple/GooglePay. If so method changes
        payment_method in BasketPayment.

        :param pr: PaymentRow object, PBA payment.
        :param payment_method: PaymentMethod
        """
        if not pr.basket_payment_id or not txn_refactor_stage2_enabled(pr.receipt.transaction):
            return

        basket_payment = BasketPaymentService.get_basket_payment(pr.basket_payment_id)

        if basket_payment.balance_transaction_id:
            return

        payment_splits, refund_splits, dispute_splits = TransactionService.calculate_fees(
            pos=pr.receipt.transaction.pos,
            payment_provider_code=(
                PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                    basket_payment.payment_provider_code
                )
            ),
            payment_type_code=PaymentTypeEnum(pr.payment_type.code),
        )

        if hasattr(payment_method, 'token'):
            new_payment_method = {
                CARD_TYPE__GOOGLE_PAY: PaymentMethodType.GOOGLE_PAY,
                CARD_TYPE__APPLE_PAY: PaymentMethodType.APPLE_PAY,
                CARD_TYPE__BLIK: PaymentMethodType.BLIK,
                CARD_TYPE__KEYED_IN_PAYMENT: PaymentMethodType.KEYED_IN_PAYMENT,
            }[payment_method.card_type]

            BasketPaymentService.change_basket_payment_payment_method(
                basket_payment=basket_payment,
                new_payment_method=new_payment_method,
            )

        assert txn_refactor_stage2_enabled(pr.receipt.transaction) is True
        # pylint: disable=protected-access,duplicate-code
        payment_splits, refund_splits, dispute_splits = (
            BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
        )
        BasketPaymentService.initialize_payment_balance_transaction(
            basket_payment=basket_payment,
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
            payment_token=payment_method.token if hasattr(payment_method, 'token') else None,
        )

    @staticmethod
    def convert_txn_into_basket(txn: Transaction) -> Basket:
        """
        Converts Transaction with TransactionRows into Basket and BasketItems.
        Used in transforming CancellationFeeAuth into Basket.

        :param txn: Transaction object
        """
        basket = BasketService.create_basket(
            business_id=txn.pos.business.id,
            basket_type=TransactionService.map_transaction_type(txn.transaction_type),
            customer_card_id=txn.customer_card_id,
        )
        txn.basket_id = basket.id
        txn.save(update_fields=['basket_id'])

        for row in txn.rows.all():
            basket_item = TransactionService.create_basket_item(basket, row)
            row.basket_item_id = basket_item.id
            row.save(update_fields=['basket_item_id'])

        return basket

    @staticmethod
    def create_basket_from_cf_auth(txn: Transaction) -> Optional[BasketPayment]:
        """
        In old structure CF is transaction, in the new one not anymore, so that's the reason we need
        to create basket later - in the moment of paying for Cancellation Fee
        :param txn: Transaction object
        """
        cf_auth = CancellationFeeAuthService.get_auth(txn.cancellation_fee_auth_id)
        basket = None

        if (
            not txn.basket_id
            and cf_auth
            and cf_auth.status == CancellationFeeAuthStatus.SUCCESS
            and txn.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        ):
            basket = TransactionService.convert_txn_into_basket(txn)
            cf_auth.basket = basket
            cf_auth.save(update_fields=['basket'])

        if txn.basket_id:
            basket = BasketService.get_basket(txn.basket_id)

        if not basket:
            return

        for pr in txn.latest_receipt.payment_rows.all():
            basket_payment = TransactionService.create_basket_payment(
                basket=basket, txn=txn, pr=pr, payment_initialization=True
            )
            pr.basket_payment_id = basket_payment.id
            pr.save(update_fields=['basket_payment_id'])

            SynchronizeService.synchronize_stripe_payment_intent(
                basket_payment,
            )

            return basket_payment

    @staticmethod
    def create_basket_items(basket: Basket, rows: list[dict]):
        BasketItem.objects.filter(basket_id=basket.id).update(deleted=tznow())
        for row in rows:
            basket_item = TransactionService.create_basket_item(basket, row)
            row['basket_item_id'] = basket_item.id

    @staticmethod
    def create_basket_item(basket: Basket, row: dict | TransactionRow) -> BasketItem:
        """
        Converts TransactionRow into BasketItem. Method is used before saving TransactionRow, so
        it needs to operate on dict. Used in TransactionSerializer.create().

        :param basket: Basket objects
        :param row: dict representation of TransactionRow.
        :param order: row ordinal
        :return: Saved instance of BasketItem
        """

        tax_rate = safe_get(row, ['tax_rate'])

        basket_item = BasketItem.objects.create(
            basket=basket,
            type=TransactionRowService.map_type(safe_get(row, ['type'])),
            name_line_1=safe_get(row, ['name_line_1']),
            name_line_2=safe_get(row, ['name_line_2']),
            quantity=safe_get(row, ['quantity']),
            item_price=minor_unit(safe_get(row, ['item_price'])),
            discount_rate=safe_get(row, ['discount_rate']),
            discounted_item_price=minor_unit(safe_get(row, ['discounted_item_price'])),
            tax_amount=minor_unit(safe_get(row, ['tax_amount'])),
            tax_rate=None if tax_rate is None else minor_unit(tax_rate),
            tax_type=(
                BasketItemTaxType.EXCLUDED
                if safe_get(row, ['tax_type']) == POS.POS_TAX_MODE__EXCLUDED
                else BasketItemTaxType.INCLUDED
            ),
            total=minor_unit(safe_get(row, ['total'])),
            net_total=minor_unit(safe_get(row, ['net_total'])),
            gross_total=minor_unit(safe_get(row, ['gross_total'])),
            discounted_total=minor_unit(safe_get(row, ['discounted_total'])),
            net_total_wo_discount=minor_unit(safe_get(row, ['net_total_wo_discount'])),
            order=safe_get(row, ['order']),
        )

        BasketItemService.create_basket_item_details(
            basket_item,
            external_object_id=(
                safe_get(row, ['service_variant', 'id'])
                or safe_get(row, ['product', 'id'])
                or safe_get(row, ['voucher', 'id'])
            ),
            subbooking_id=safe_get(row, ['subbooking', 'id']),
            appointment_id=safe_get(row, ['subbooking', 'appointment', 'id']),
        )

        return basket_item

    @staticmethod
    def process_cf_auth_status(txn, txn_status):
        """
        Updates CancellationFeeAuth status if related txn in old structure enters
        particular statuses.

        DepositChargeSuccess is not handled here, because it will be converted to Basket.

        """
        cf_auth = CancellationFeeAuth.objects.filter(id=txn.cancellation_fee_auth_id).first()

        if cf_auth and txn_status in [
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt_status.DEPOSIT_AUTHORISATION_FAILED,
            receipt_status.DEPOSIT_CHARGE_CANCELED,
        ]:
            cf_auth.status = PaymentRowService.map_cf_receipt_status(txn_status)
            cf_auth.save(update_fields=['status'])

        if (
            cf_auth
            and cf_auth.status == CancellationFeeAuthStatus.PENDING
            and txn_status == receipt_status.CALL_FOR_DEPOSIT
        ):
            SynchronizeService.synchronize_stripe_payment_intent_from_cf(
                cf_auth=cf_auth,
            )

    @staticmethod
    @transaction.atomic
    def change_pba_tip(
        txn: Transaction,
        data: dict,
    ):
        """
        Changes tip. Tip, TipRows are updated.

        if pos_refactor_stage2 is enabled BasketPayment and BasketTip is also updated.
        :param txn: Transaction object
        :param data: CustomerTransactionActionRequest.validated_data
        """
        old_total = txn.total
        calculations.recalculate_tips(txn, request_data=data, update_instance=True)
        txn.save()

        # Tip can be set only on PBA Row
        tip_already_paid = PaymentRowService.get_tip_already_paid(txn.payment_rows)
        pr = txn.latest_receipt.payment_rows.get(
            id=data['row_id'],
            status__in=(
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.CALL_FOR_PREPAYMENT,
            ),
        )
        pr.amount = pr.amount + txn.total - old_total
        pr.tip_amount = txn.tip.amount - tip_already_paid
        pr.save(update_fields=['amount', 'tip_amount'])

        if not txn_refactor_stage2_enabled(txn):
            return

        try:
            basket_payment = BasketPaymentService.get_basket_payment(
                basket_payment_id=pr.basket_payment_id
            )
        except BasketPaymentNotFound:
            return

        BasketTipService.delete_tips(basket_payment.basket)
        TransactionTipService.create_basket_tips(
            basket=basket_payment.basket,
            txn=txn,
            tip=txn.tip,
            tip_rows=txn.tip.tip_rows.all(),
            source=BasketTipSource.CUSTOMER_APP,
            tip_already_paid=PaymentRowService.get_tip_already_paid(txn.payment_rows),
        )

        BasketPaymentService.update_basket_payment_amount(
            basket_payment=basket_payment,
            new_amount=minor_unit(pr.amount),
        )

    @staticmethod
    def _check_balance_transaction_and_rise_error(
        balance_transaction_id: uuid.UUID,
    ):
        balance_transaction = BalanceTransaction.objects.get(id=balance_transaction_id)
        payment = Payment.objects.get(id=balance_transaction.external_id)

        quick_assert(
            not payment.error_code,
            ('invalid', 'validation', 'payment_method'),
            payment.get_error_code_display(),
        )

        if payment.provider_code == PaymentProviderCode.ADYEN:
            adyen_auth = hasattr(payment, 'adyen_payment') and payment.adyen_payment.auth
            auth_reference = adyen_auth.external_id
        else:
            auth_reference = None

        is_apple_pay_native = (
            payment.payment_method == PaymentProviderPaymentMethodType.APPLE_PAY
            and payment.status == PaymentStatus.NEW
        )
        if payment.payment_method == PaymentProviderPaymentMethodType.BLIK or is_apple_pay_native:
            raise BlikActionRequired(
                balance_transaction_id=balance_transaction.id,
            )

        if payment.action_required_details:
            raise ThreeDSecureAuthenticationRequired(
                '3DSecure',
                three_d_data=payment.action_required_details,
                auth_reference=auth_reference,
            )

    @staticmethod
    def _check_auth_success(txn: Transaction):
        updated_prepayment_row = txn.latest_receipt.payment_rows.get()
        basket_payment = BasketPayment.objects.get(
            id=updated_prepayment_row.basket_payment_id,
        )

        TransactionService._check_balance_transaction_and_rise_error(
            balance_transaction_id=basket_payment.balance_transaction_id
        )

    @classmethod
    def check_prepayment_auth_success(cls, txn: Transaction):
        cls._check_auth_success(txn)

    @classmethod
    def check_booksy_pay_auth_success(cls, txn: Transaction):
        cls._check_auth_success(txn)

    @staticmethod
    def check_cancellation_fee_auth_success(txn: Transaction):
        if CancellationFeeFullAmountAuthorizationFlag():
            updated_cf_row = txn.latest_receipt.payment_rows.get()
            cf_auth = CancellationFeeAuth.objects.get(
                id=updated_cf_row.receipt.transaction.cancellation_fee_auth_id,
            )
            TransactionService._check_balance_transaction_and_rise_error(
                balance_transaction_id=cf_auth.balance_transaction_id
            )

    @staticmethod
    def action__cancel_payment(txn: Transaction, log_note: str, pr_id: int | None = None) -> bool:
        from webapps.pos.provider.proxy import ProxyProvider  # pylint: disable=cyclic-import
        from webapps.pos.provider.adyen_ee import (
            AdyenEEPaymentProvider,
        )  # pylint: disable=cyclic-import

        filters = [
            Q(status__in=receipt_status.CANCELABLE_STATUSES),
            Q(payment_type__code__in=PaymentTypeEnum.online_methods()),
        ]

        if pr_id:
            filters.append(Q(id=pr_id))

        payment_row = txn.latest_receipt.payment_rows.filter(
            *filters,
        ).first()

        if not payment_row:
            return False
        if txn_refactor_stage2_enabled(txn):
            return ProxyProvider.cancel_payment(payment_row=payment_row, log_note=log_note)

        if payment_row.payment_type.code in PaymentTypeEnum.stripe_only_methods():
            # This scenario should never happen for Stripe-only payment methods.
            # This exception is just an extra level of protection.
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                f'Unexpected call for {payment_row.payment_type}'
            )

        return AdyenEEPaymentProvider.cancel_payment(payment_row=payment_row, log_note=log_note)

    @staticmethod
    def get_fees(payment_provider_code, pr):
        payment_splits, refund_splits, dispute_splits = TransactionService.calculate_fees(
            pos=pr.receipt.transaction.pos,
            payment_provider_code=(
                PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                    payment_provider_code
                )
            ),
            payment_type_code=PaymentTypeEnum(pr.payment_type.code),
        )
        return dispute_splits, payment_splits, refund_splits

    @classmethod
    def is_off_session_payment(
        cls,
        txn: Transaction,
        payment_method_type: PaymentMethodType | None = None,
    ) -> bool | None:
        return cls._is_off_session_payment(
            txn,
            payment_method_type,
        )

    @classmethod
    def get_adyen_recurring_model(cls, txn: Transaction) -> RecurringModel:
        if cls._is_off_session_payment(txn):
            return RecurringModel.UNSCHEDULED_CARD_ON_FILE
        return RecurringModel.CARD_ON_FILE

    @staticmethod
    def _is_off_session_payment(
        txn: Transaction,
        payment_method_type: PaymentMethodType | None = None,
    ) -> bool:
        # TODO move to new structure https://booksy.atlassian.net/browse/POS-1962
        if payment_method_type in (PaymentMethodType.BLIK, PaymentMethodType.KEYED_IN_PAYMENT):
            return False

        cancellation_fee_transaction = (
            txn.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        )
        if txn.customer:
            automatically_finished_booking = (
                txn.latest_receipt.status_code == receipt_status.CALL_FOR_PAYMENT
                and txn.customer.is_payment_auto_accept_possible
            )
        else:
            automatically_finished_booking = False
        booking_confirmed_in_manual_mode = (
            txn.latest_receipt.status_code == receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
            and txn.pos.business.booking_mode == Business.BookingMode.SEMIAUTO
        )

        return (
            cancellation_fee_transaction
            or automatically_finished_booking
            or booking_confirmed_in_manual_mode
        )


class TransactionRowService:
    @staticmethod
    def map_type(transaction_row_type: str) -> BasketItemType:
        """Maps TransactionRowType into BasketItemType."""
        return {
            TransactionRow.TRANSACTION_ROW_TYPE__SERVICE: BasketItemType.SERVICE,
            TransactionRow.TRANSACTION_ROW_TYPE__PRODUCT: BasketItemType.PRODUCT,
            TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER: BasketItemType.VOUCHER,
            TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT: BasketItemType.DEPOSIT,
            TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE: BasketItemType.TRAVEL_FEE,
            TransactionRow.TRANSACTION_ROW_TYPE__ADDON: BasketItemType.ADDON,
        }[transaction_row_type]

    @staticmethod
    def map_tax_type(tax_type: str) -> BasketItemTaxType:
        """Maps TaxType into BasketItemTaxType."""
        return {
            POS.POS_TAX_MODE__INCLUDED: BasketItemTaxType.INCLUDED,
            POS.POS_TAX_MODE__EXCLUDED: BasketItemTaxType.EXCLUDED,
        }[tax_type]


class CommissionService:
    @staticmethod
    def create_commissions(commissions_v1: list[CommissionV1]):
        Commission.objects.bulk_create(
            Commission(
                business_id=commission_v1.row.transaction.pos.business_id,
                item_id=commission_v1.row.basket_item_id,
                resource_id=commission_v1.resource.id,
                amount=minor_unit(commission_v1.amount),
                type=commission_v1.type,
                rate=minor_unit(commission_v1.rate),
            )
            for commission_v1 in commissions_v1
        )
