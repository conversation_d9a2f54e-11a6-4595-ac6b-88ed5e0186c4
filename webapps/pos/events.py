from lib.events import EventSignal

deposit_charged_event = EventSignal(event_type='deposit_charged_event')

payment_completed_event = EventSignal(event_type='payment_completed_event')

refund_received_event = EventSignal(event_type='refund_received')

chargeback_received_event = EventSignal(event_type='chargeback_received')

chargeback_reversed_event = EventSignal(event_type='chargeback_reversed')

second_chargeback_received_event = EventSignal(event_type='second_chargeback_received')

payout_settled_event = EventSignal(event_type='payout_settled')

basket_payment_completed_event = EventSignal(event_type='basket_payment_completed')
