import uuid
from collections import namedtuple

from decimal import Decimal

from django.utils.encoding import force_str

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.pos.entities import (
    PaymentRowEntity,
    ReceiptDetailsEntity,
    TransactionEntity,
    POSEntity,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status, TapToPayStatus
from webapps.pos.models import PaymentRow, POS, Receipt
from webapps.pos.serializers import CustomerInfoSerializer
from webapps.pos.services import PaymentRowService, TransactionService
from webapps.pos.tools import get_minimal_pba_amount

PendingFeeData = namedtuple(
    'PendingFeeData',
    [
        'business_id',
        'amount',
        'fee_refs',
    ],
)

CustomerInvoiceItemData = namedtuple(
    'CustomerInvoiceItemData',
    [
        'id',
        'name',
        'quantity',  # integer
        'unit_symbol',
        'net_unit_price',  # minor units
        'gross_unit_price',  # minor units
        'tax_rate',  # Optional[Decimal]
    ],
)

CustomerInvoiceData = namedtuple(
    'CustomerInvoiceData',
    [
        'id',
        'buyer_customer_id',
        'payment_method',
        'total_gross_value',
        'items',
    ],
)


def get_pending_fees(before_midnight=True):
    from webapps.pos.refund import get_pending_fees as _get_pending_fees

    for fee in _get_pending_fees(before_midnight=before_midnight):
        yield PendingFeeData(
            business_id=fee['pos__business_id'], amount=fee['amount'], fee_refs=fee['fee_refs']
        )


def get_customer_info_data(bci_instance):
    return CustomerInfoSerializer(instance=bci_instance).data


def enable_stripe_terminal_payments(business_pk: int):
    pos = POS.objects.filter(business_id=business_pk).first()
    if pos:
        pos.enable_stripe_terminal_payments(set_as_default=False)


def set_tap_to_pay_status(business_pk: int, status: TapToPayStatus):
    POS.objects.filter(business_id=business_pk).update(tap_to_pay_status=status)


class PosSettingsPort:
    @staticmethod
    def get_minimal_pba_amount() -> Decimal:
        return get_minimal_pba_amount()


class POSPort:
    @staticmethod
    def blik_in_prepayment_promo_enabled(business_id: int) -> bool:
        return POS.objects.filter(
            business_id=business_id,
            blik_in_prepayment_promo_enabled=True,
        ).exists()

    @staticmethod
    def get_pos_by_business_id(business_id: int) -> POSEntity:
        pos = POS.objects.filter(business_id=business_id).first()
        return POSEntity(
            id=pos.id,
            receipt_footer_line_1=pos.receipt_footer_line_1,
            receipt_footer_line_2=pos.receipt_footer_line_2,
        )


class PaymentRowPort:
    @staticmethod
    def get_last_payment_row_entity(basket_payment_id: uuid.UUID) -> PaymentRowEntity:
        return PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last().entity


class TransactionPort:
    @staticmethod
    def is_off_session_transaction(receipt_id: int) -> bool:
        return TransactionService.is_off_session_payment(
            txn=Receipt.objects.get(id=receipt_id).transaction,
        )

    @staticmethod
    def get_receipt_details(basket_id: uuid.UUID) -> ReceiptDetailsEntity | None:
        """
        Returns receipt details (id and receipt_number) for a given basket_id.

        :param basket_id: UUID of the basket
        :return: ReceiptDetailsEntity with receipt id and receipt_number or None if not found
        """
        from webapps.pos.models import Transaction

        transaction = (
            Transaction.objects.filter(
                basket_id=basket_id,
            )
            .select_related('latest_receipt')
            .first()
        )

        if not transaction or not transaction.latest_receipt:
            return None

        receipt = transaction.latest_receipt
        return ReceiptDetailsEntity(
            id=receipt.id,
            receipt_number=receipt.receipt_number,
            assigned_number=receipt.assigned_number,
        )

    @staticmethod
    def calculate_fees(
        payment_row_id: int,
        payment_provider_code: PaymentProviderCode,
    ) -> tuple[PaymentSplitsEntity, RefundSplitsEntity, DisputeSplitsEntity]:
        payment_row = PaymentRow.objects.get(id=payment_row_id)
        return TransactionService.calculate_fees(
            pos=payment_row.receipt.transaction.pos,
            payment_provider_code=(
                PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                    payment_provider_code,
                )
            ),
            payment_type_code=PaymentTypeEnum(payment_row.payment_type.code),
        )

    @staticmethod
    def get_transaction(basket_payment_id: uuid.UUID) -> TransactionEntity | None:
        transaction = TransactionService.get_transaction(basket_payment_id=basket_payment_id)
        return transaction.entity if transaction else None


class RefundPort:
    @staticmethod
    def is_refund_possible(
        basket_payment_id: uuid.UUID,
        check_requested: bool,
        check_balance: bool,
        refresh_transaction: bool,
    ) -> tuple[bool, None]:
        from webapps.pos.refund import is_refund_possible

        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
        if not payment_row:
            return False, None

        return is_refund_possible(
            payment_row,
            check_requested=check_requested,
            check_balance=check_balance,
            refresh_transaction=refresh_transaction,
        )

    @staticmethod
    def basket_payment_refund_status(basket_payment_id: uuid.UUID) -> dict | None:
        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
        if payment_row.status not in receipt_status.REFUND_STATUSES:
            return None
        short_status = receipt_status.STATUS_TYPES_REVERSE.get(payment_row.status, '')
        return {
            'short_status': short_status,
            'short_status_label': force_str(
                receipt_status.STATUS_TYPES_DISPLAY.get(
                    short_status,
                )
            ),
        }

    @staticmethod
    def payment_row_id_from_basket_payment(basket_payment_id: uuid.UUID) -> int | None:
        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
        return payment_row.id if payment_row else None
