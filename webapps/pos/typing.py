from dataclasses import dataclass
from typing import List, TypedDict

from dateutil.relativedelta import relativedelta


class ModifiedServiceData(TypedDict):
    created_service_variant_ids: List[int]
    deleted_service_variant_ids: List[int]


class BooksyPayPaymentInfo(TypedDict):
    is_paid: bool
    refundable: bool
    is_auto_refund_possible: bool
    late_cancellation_window: relativedelta


@dataclass(slots=True)
class RefundEligibility:
    refundable: bool = False
    is_auto_refund_possible: bool = False
