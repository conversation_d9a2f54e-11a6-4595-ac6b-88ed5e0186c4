# pylint: disable=too-many-lines,too-many-statements
import datetime
from decimal import Decimal

import pytest
import pytz
from dateutil.relativedelta import relativedelta
from django.utils.crypto import get_random_string
from mock import patch
from model_bakery import baker
from rest_framework.test import APIClient
from stripe.error import CardError

from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Service,
    ServiceVariant,
)
from webapps.market_pay.models import AccountHolder
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import StripeAccountHolder
from webapps.pos.enums import (
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.enums.receipt_status import PAYMENT_SUCCESS, PENDING
from webapps.pos.models import (
    POS,
    PaymentType,
    TaxRate,
    Transaction,
    POSPlan,
)
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.stripe_app.models import PaymentIntent
from webapps.stripe_integration.models import StripeAccount


@pytest.mark.django_db
class TestCheckoutKeyedInPaymentEntry(BaseAsyncHTTPTest, StripeMixin):
    def setUp(self):
        super().setUp()
        self.client = APIClient()
        self.client_secret = get_random_string(12)
        self.payment_intent_stripe_id = 'pi_123456789'
        self.service_1 = baker.make(Service, business=self.business)
        self.service_variant = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )

        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            pos_refactor_stage2_enabled=True,
        )
        self.pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,  # not random just to force non-negative value of a fee
            txn_fee=0.1,  # not random just to force non-negative value of a fee
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        baker.make(
            StripeAccountHolder,
            account_holder_id=self.business_wallet.account_holder_id,
        )
        baker.make(StripeAccount, pos=self.business.pos)
        pos.pos_plans.add(self.pos_plan)

        baker.make(TaxRate, pos=pos, default_for_service=True, rate=Decimal('10.00'))
        baker.make(PaymentType, code=PaymentTypeEnum.KEYED_IN_PAYMENT, pos=pos)

        booked_from1 = datetime.datetime(2019, 1, 1, 10, 0, tzinfo=pytz.UTC)
        self.appointment = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + datetime.timedelta(minutes=15),
                    'service_variant': self.service_variant,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        )
        self.booking_1 = self.appointment.subbookings[0]

        self.payment_failed_body = {
            'id': 'evt_failed',
            'type': 'payment_intent.payment_failed',
            'data': {
                'object': {
                    'id': self.payment_intent_stripe_id,
                    'status': 'failed',
                    'last_payment_error': {
                        'code': 'card_declined',
                        'message': 'Your card was declined.',
                    },
                }
            },
        }

    def test_success_checkout_using_keyed_in_payment(self):
        total = 250

        # when
        # create Transaction and PaymentIntent
        txn_url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        with patch('stripe.PaymentIntent.create') as pi_create_mock:
            pi_create_mock.return_value = PaymentIntent(id=self.payment_intent_stripe_id)
            txn_resp = self.fetch(
                txn_url, method='POST', body=self._get_transaction_POST_body(total, self.owner.id)
            )
        self.assertEqual(Transaction.objects.first().id, txn_resp.json['transaction']['id'])

        # payment is completed on front with Stripe SDK
        # front starts pooling for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PENDING)

        # handle notification of PaymentIntent.succeeded event
        payment_succeeded_body = self._get_payment_intent_succeeded_notification_body(
            self.payment_intent_stripe_id, self.client_secret
        )
        notification_resp = self._simulate_stripe_incoming_webhook(
            data=payment_succeeded_body, connect=False, expect_new_webhook_handling=True
        )

        self.assertEqual(notification_resp.status_code, 201)

        # another request for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        # then
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PAYMENT_SUCCESS)
        self.assertEqual(
            resp_last_receipt.json['receipt']['payment_type']['code'],
            PaymentTypeEnum.KEYED_IN_PAYMENT.value,
        )
        total_dolars = f'${total}.00'
        self.assertEqual(resp_last_receipt.json['receipt']['total'], total_dolars)
        self.assertEqual(resp_last_receipt.json['receipt']['already_paid'], total_dolars)

    def test_fail_checkout_using_keyed_in_payment(self):
        total = 250

        # when
        # create Transaction and PaymentIntent
        txn_url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        with patch('stripe.PaymentIntent.create') as pi_create_mock:
            pi_create_mock.side_effect = CardError(
                message="Your card was declined.",
                param=None,
                code="card_declined",
                http_status=402,
                json_body=self._get_payment_intent_fail_notification_body(
                    self.payment_intent_stripe_id, self.client_secret
                ),
            )
            txn_resp = self.fetch(
                txn_url, method='POST', body=self._get_transaction_POST_body(total, self.owner.id)
            )
        self.assertEqual(Transaction.objects.first().id, txn_resp.json['transaction']['id'])

        # payment is failed on front with Stripe SDK
        # front starts pooling for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PENDING)

        # handle notification of PaymentIntent.failed event

        notification_resp = self._simulate_stripe_incoming_webhook(
            data=self.payment_failed_body, connect=False, expect_new_webhook_handling=True
        )

        self.assertEqual(notification_resp.status_code, 201)

        # another request for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        # then
        self.assertEqual(
            resp_last_receipt.json['receipt']['status_code'], receipt_status.PAYMENT_FAILED
        )
        self.assertEqual(
            resp_last_receipt.json['receipt']['payment_type']['code'],
            PaymentTypeEnum.KEYED_IN_PAYMENT.value,
        )
        self.assertEqual(resp_last_receipt.json['receipt']['total'], f'${total}.00')
        self.assertEqual(resp_last_receipt.json['receipt']['already_paid'], '$0.00')

    def test_retry(self):
        # when
        # create Transaction and PaymentIntent
        txn_url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        with patch('stripe.PaymentIntent.create') as pi_create_mock:
            pi_create_mock.side_effect = CardError(
                message="Your card was declined.",
                param=None,
                code="card_declined",
                http_status=402,
                json_body=self._get_payment_intent_fail_notification_body(
                    self.payment_intent_stripe_id, self.client_secret
                ),
            )
            txn_resp = self.fetch(
                txn_url,
                method='POST',
                body=self._get_transaction_POST_body(
                    int(self.service_variant.price), self.owner.id
                ),
            )
        self.assertEqual(Transaction.objects.first().id, txn_resp.json['transaction']['id'])

        # payment is failed on front with Stripe SDK
        # front starts pooling for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PENDING)

        # handle notification of PaymentIntent.failed event

        notification_resp = self._simulate_stripe_incoming_webhook(
            data=self.payment_failed_body, connect=False, expect_new_webhook_handling=True
        )

        self.assertEqual(notification_resp.status_code, 201)

        # another request for Transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')

        # then
        self.assertEqual(
            resp_last_receipt.json['receipt']['status_code'], receipt_status.PAYMENT_FAILED
        )
        self.assertEqual(
            resp_last_receipt.json['receipt']['payment_type']['code'],
            PaymentTypeEnum.KEYED_IN_PAYMENT.value,
        )
        self.assertEqual(
            resp_last_receipt.json['receipt']['total'], f'${self.service_variant.price}.00'
        )
        self.assertEqual(resp_last_receipt.json['receipt']['already_paid'], '$0.00')

        transaction_id = txn_resp.json['transaction']['id']

        # Mock the payment intent confirmation
        with patch('stripe.PaymentIntent.confirm') as confirm_mock:
            confirm_mock.return_value = {
                'id': self.payment_intent_stripe_id,
                'status': 'succeeded',
                'client_secret': self.client_secret,
            }

            body = {
                'action': 'retry_kip',
                'payment_token': 'pm_1Q0PsIJvEtkwdCNYMSaVuRz6',
            }

            url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{transaction_id}/action/'  # pylint: disable=line-too-long
            resp = self.fetch(url, method='POST', body=body)
            self.assertEqual(resp.code, 200)
            new_transaction_id = resp.json['transaction']['id']

        # Simulate successful payment webhook notification
        payment_succeeded_body = self._get_payment_intent_succeeded_notification_body(
            self.payment_intent_stripe_id, self.client_secret
        )
        notification_resp = self._simulate_stripe_incoming_webhook(
            data=payment_succeeded_body, connect=False, expect_new_webhook_handling=True
        )
        self.assertEqual(notification_resp.status_code, 201)

        # Verify the transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}"
            f"/pos/transactions/{new_transaction_id}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PAYMENT_SUCCESS)

    def test_retry_with_commodity(self):
        # Setup: create a warehouse and a commodity with stock
        warehouse = baker.make(
            'warehouse.Warehouse',
            business=self.business,
            is_default=True,
        )
        commodity = baker.make(
            'warehouse.Commodity',
            business=self.business,
            product_type='r',  # Retail
            enable_stock_control=True,
            total_pack_capacity=10,
            order=1,
        )
        baker.make(
            'warehouse.CommodityStockLevel',
            warehouse=warehouse,
            commodity=commodity,
            remaining_volume=10,
        )

        # Prepare transaction data with a product
        def get_body_with_product(total, staffer_id):
            body = self._get_transaction_POST_body(total, staffer_id)
            body['products'] = [{
                'product_id': commodity.id,
                'item_price': total,
                'quantity': 1,
                'warehouse': warehouse.id,
            }]
            return body

        txn_url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/'
        with patch('stripe.PaymentIntent.create') as pi_create_mock:
            pi_create_mock.side_effect = CardError(
                message="Your card was declined.",
                param=None,
                code="card_declined",
                http_status=402,
                json_body=self._get_payment_intent_fail_notification_body(
                    self.payment_intent_stripe_id, self.client_secret
                ),
            )
            txn_resp = self.fetch(
                txn_url,
                method='POST',
                body=get_body_with_product(100, self.owner.id),
            )
        self.assertEqual(Transaction.objects.first().id, txn_resp.json['transaction']['id'])

        # payment is failed on front with Stripe SDK
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}/pos/transactions/"
            f"{txn_resp.json['transaction']['id']}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PENDING)

        # handle notification of PaymentIntent.failed event
        notification_resp = self._simulate_stripe_incoming_webhook(
            data=self.payment_failed_body, connect=False, expect_new_webhook_handling=True
        )
        self.assertEqual(notification_resp.status_code, 201)

        # another request for Transaction status
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')
        self.assertEqual(
            resp_last_receipt.json['receipt']['status_code'], receipt_status.PAYMENT_FAILED
        )

        transaction_id = txn_resp.json['transaction']['id']

        # Mock the payment intent confirmation
        with patch('stripe.PaymentIntent.confirm') as confirm_mock:
            confirm_mock.return_value = {
                'id': self.payment_intent_stripe_id,
                'status': 'succeeded',
                'client_secret': self.client_secret,
            }
            body = {
                'action': 'retry_kip',
                'payment_token': 'pm_1Q0PsIJvEtkwdCNYMSaVuRz6',
            }
            url = f'/business_api/me/businesses/{self.business.id}/pos/transactions/{transaction_id}/action/'
            resp = self.fetch(url, method='POST', body=body)
            self.assertEqual(resp.code, 200)
            new_transaction_id = resp.json['transaction']['id']

        # Simulate successful payment webhook notification
        payment_succeeded_body = self._get_payment_intent_succeeded_notification_body(
            self.payment_intent_stripe_id, self.client_secret
        )
        notification_resp = self._simulate_stripe_incoming_webhook(
            data=payment_succeeded_body, connect=False, expect_new_webhook_handling=True
        )
        self.assertEqual(notification_resp.status_code, 201)

        # Verify the transaction status
        last_receipt_url = (
            f"/business_api/me/businesses/{self.business.id}"
            f"/pos/transactions/{new_transaction_id}/last_receipt/?"
        )
        resp_last_receipt = self.fetch(last_receipt_url, method='GET')
        self.assertEqual(resp_last_receipt.json['receipt']['status_code'], PAYMENT_SUCCESS)
        # Optionally: check that the product row is present in the transaction
        self.assertTrue(
            any(row['product_id'] == commodity.id for row in resp_last_receipt.json['transaction']['rows'])
        )

    def _get_transaction_POST_body(self, total: int, staffer_id: int) -> dict:
        return {
            "transaction_type": "P",
            'payment_token': 'pm_1Q0PsIJvEtkwdCNYMSaVuRz6',
            "customer_card_id": None,
            "discount_rate": 0,
            "booking": None,
            "appointment": self.appointment.id,
            "multibooking": None,
            "payment_rows": [
                {
                    "basket_payment_id": None,
                    "amount": f"{total}.00",
                    "payment_type_code": "keyed_in_payment",
                    "label": "Quick Card Entry",
                    "amount_text": f"${total}.00",
                    "mode": "C",
                    "tip_amount": None,
                    "service_amount": None,
                    "voucher_id": None,
                    "voucher_service_id": None,
                    "voucher_service_price": None,
                    "voucher_code": None,
                }
            ],
            "issuing_staffer": staffer_id,
            "confirming_staffer": None,
            "note": None,
            "bookings": [
                {
                    "booking_id": self.booking_1.id,
                    "quantity": 1,
                    "item_price": total,
                    "discount_rate": 0,
                    "commission_staffer_id": staffer_id,
                    "service_name": "Opłata",
                    "row_hash_uuid": "23f60ee1e79e477e8f51919741ffbe19",
                }
            ],
            "booking_id": self.booking_1.id,
            "travel_fee": None,
            "vouchers": [],
            "addons": [],
            "products": [],
            "force_customer": True,
            "dry_run": False,
            "selected_register_id": None,
            "compatibilities": {
                "stripe_terminal": True,
                "square": True,
                "split": True,
                "prepayment": True,
                "new_checkout": True,
                "membership": True,
                "egift_card": True,
                "package": True,
                "blik": True,
                "keyed_in_payment": True,
            },
        }

    @staticmethod
    def _get_payment_intent_fail_notification_body(
        payment_intent_id: str, client_secret: str
    ) -> dict:
        return {
            "error": {
                "message": "Your card has insufficient funds.",
                "type": "card_error",
                "code": "card_declined",
                "decline_code": "insufficient_funds",
                "advice_code": "try_again_later",
                "doc_url": "https://stripe.com/docs/error-codes/card-declined",
                "charge": "ch_3R511KJRQdKvDXrk2MkroAz8",
                "payment_intent": {
                    "id": payment_intent_id,
                    "object": "payment_intent",
                    "amount": 2627,
                    "currency": "usd",
                    "status": "requires_payment_method",
                    "client_secret": client_secret,
                    "created": **********,
                    "livemode": False,
                    "metadata": {"provider_request_id": "0b882614-a324-4027-818b-1250479d967b"},
                    "last_payment_error": {
                        "advice_code": "try_again_later",
                        "charge": "ch_3R511KJRQdKvDXrk2MkroAz8",
                        "code": "card_declined",
                        "decline_code": "insufficient_funds",
                        "message": "Your card has insufficient funds.",
                        "payment_method": {
                            "id": "pm_1R511JJRQdKvDXrkokGrbq6P",
                            "object": "payment_method",
                            "type": "card",
                        },
                        "type": "card_error",
                    },
                    "latest_charge": "ch_3R511KJRQdKvDXrk2MkroAz8",
                },
                "payment_method": {
                    "id": "pm_1R511JJRQdKvDXrkokGrbq6P",
                    "object": "payment_method",
                    "type": "card",
                },
            }
        }

    @staticmethod
    def _get_payment_intent_succeeded_notification_body(
        payment_intent_id: str, client_secret: str
    ) -> dict:
        return {
            "id": "evt_3Quu0vJRQdKvDXrk17dMYE3F",
            "object": "event",
            "api_version": "2020-08-27",
            "created": 1740135808,
            "data": {
                "object": {
                    "id": payment_intent_id,
                    "object": "payment_intent",
                    "amount": 1310,
                    "amount_capturable": 0,
                    "amount_details": {"tip": {}},
                    "amount_received": 1310,
                    "application": None,
                    "application_fee_amount": 1410,
                    "automatic_payment_methods": None,
                    "canceled_at": None,
                    "cancellation_reason": None,
                    "capture_method": "automatic_async",
                    "client_secret": client_secret,
                    "confirmation_method": "automatic",
                    "created": 1740135805,
                    "currency": "usd",
                    "customer": None,
                    "description": None,
                    "invoice": None,
                    "last_payment_error": None,
                    "latest_charge": "ch_3Quu0vJRQdKvDXrk1XgaH3NH",
                    "livemode": False,
                    "metadata": {},
                    "next_action": None,
                    "on_behalf_of": None,
                    "payment_method": "pm_1Quu0xJRQdKvDXrkGxklgXuC",
                    "payment_method_configuration_details": None,
                    "payment_method_options": {
                        "card": {
                            "installments": None,
                            "mandate_options": None,
                            "network": None,
                            "request_three_d_secure": "automatic",
                        }
                    },
                    "payment_method_types": ["card"],
                    "processing": None,
                    "receipt_email": None,
                    "review": None,
                    "setup_future_usage": "off_session",
                    "shipping": None,
                    "source": None,
                    "statement_descriptor": None,
                    "statement_descriptor_suffix": None,
                    "status": "succeeded",
                    "transfer_data": {"destination": "acct_1QOOD6R6bIludG1R"},
                    "transfer_group": "group_pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                    "charges": {
                        "object": "list",
                        "data": [
                            {
                                "id": "ch_3Quu0vJRQdKvDXrk1XgaH3NH",
                                "object": "charge",
                                "amount": 1310,
                                "amount_captured": 1310,
                                "amount_refunded": 0,
                                "application": None,
                                "application_fee": None,
                                "application_fee_amount": 1410,
                                "balance_transaction": None,
                                "billing_details": {
                                    "address": {
                                        "city": None,
                                        "country": None,
                                        "line1": None,
                                        "line2": None,
                                        "postal_code": "60651",
                                        "state": None,
                                    },
                                    "email": None,
                                    "name": None,
                                    "phone": None,
                                },
                                "calculated_statement_descriptor": "Stripe",
                                "captured": True,
                                "created": 1740135807,
                                "currency": "usd",
                                "customer": None,
                                "description": None,
                                "destination": "acct_1QOOD6R6bIludG1R",
                                "dispute": None,
                                "disputed": False,
                                "failure_balance_transaction": None,
                                "failure_code": None,
                                "failure_message": None,
                                "fraud_details": {},
                                "invoice": None,
                                "livemode": False,
                                "metadata": {
                                    "provider_request_id": "5c20eb1e-545d-4c84-90c4-4ef63424b563"
                                },
                                "on_behalf_of": None,
                                "order": None,
                                "outcome": {
                                    "advice_code": None,
                                    "network_advice_code": None,
                                    "network_decline_code": None,
                                    "network_status": "approved_by_network",
                                    "reason": None,
                                    "risk_level": "normal",
                                    "risk_score": 8,
                                    "seller_message": "Payment complete.",
                                    "type": "authorized",
                                },
                                "paid": True,
                                "payment_intent": "pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                                "payment_method": "pm_1Quu0xJRQdKvDXrkGxklgXuC",
                                "payment_method_details": {
                                    "card": {
                                        "amount_authorized": 1310,
                                        "authorization_code": None,
                                        "brand": "visa",
                                        "checks": {
                                            "address_line1_check": None,
                                            "address_postal_code_check": "pass",
                                            "cvc_check": "pass",
                                        },
                                        "country": "US",
                                        "exp_month": 3,
                                        "exp_year": 2030,
                                        "extended_authorization": {"status": "disabled"},
                                        "fingerprint": "7KvhjvGGknrYO07H",
                                        "funding": "credit",
                                        "incremental_authorization": {"status": "unavailable"},
                                        "installments": None,
                                        "last4": "4242",
                                        "mandate": None,
                                        "moto": True,
                                        "multicapture": {"status": "unavailable"},
                                        "network": "visa",
                                        "network_token": {"used": False},
                                        "network_transaction_id": "557511810410611",
                                        "overcapture": {
                                            "maximum_amount_capturable": 1310,
                                            "status": "unavailable",
                                        },
                                        "regulated_status": "unregulated",
                                        "three_d_secure": None,
                                        "wallet": None,
                                    },
                                    "type": "card",
                                },
                                "radar_options": {},
                                "receipt_email": None,
                                "receipt_number": None,
                                "receipt_url": "https://pay.stripe.com/receipts/payment/CAcaFwoVYWNjdF8xUTZDandKUlFkS3ZEWHJrKIC74b0GMgZm8vev7Zg6LBa6bM93Zq6WCJbbbdmNSl64HNbNN1OpxtotwBctL0Bf1B6xKQOex_KorM4g",
                                "refunded": False,
                                "refunds": {
                                    "object": "list",
                                    "data": [],
                                    "has_more": False,
                                    "total_count": 0,
                                    "url": "/v1/charges/ch_3Quu0vJRQdKvDXrk1XgaH3NH/refunds",
                                },
                                "review": None,
                                "shipping": None,
                                "source": None,
                                "source_transfer": None,
                                "statement_descriptor": None,
                                "statement_descriptor_suffix": None,
                                "status": "succeeded",
                                "transfer_data": {
                                    "amount": None,
                                    "destination": "acct_1QOOD6R6bIludG1R",
                                },
                                "transfer_group": "group_pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                            }
                        ],
                        "has_more": False,
                        "total_count": 1,
                        "url": "/v1/charges?payment_intent=pi_3Quu0vJRQdKvDXrk1FwU6NAA",
                    },
                }
            },
            "livemode": False,
            "pending_webhooks": 2,
            "request": {
                "id": "req_TxmUIjNtmOyHl2",
                "idempotency_key": "c25fa0e8-76d5-414f-b571-fa4d25d4f859",
            },
            "type": "payment_intent.succeeded",
        }
