import uuid
from dataclasses import asdict

import mock
import pytest
from django.test import TestCase
from mock import MagicMock
from model_bakery import baker
from parameterized import parameterized

from lib.feature_flag.feature.payment import BasketPaymentCompletedAnalyticsFlag
from lib.payment_gateway.enums import PaymentStatus
from lib.payment_providers.entities import AccountHolderCreatedEventEntity, AccountHolderEntity
from lib.payment_providers.events import (
    payment_providers_account_holder_created_from_external_source_event,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import BasketPaymentType, PaymentMethodType, BasketPaymentStatus
from lib.segment_analytics.api import SegmentAnalyticsWrapper
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.models import Business
from webapps.pos.actions import (
    basket_payment_details_updated_event_handler,
    run_basket_payment_completed_analytics,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    POS,
    PaymentRow,
    PaymentType,
    Receipt,
    Transaction,
)


@pytest.mark.django_db
class TestPOSActions(TestCase):

    def setUp(self):
        self.pos = baker.make(
            POS,
            force_stripe_kyc=False,
        )

    @mock.patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_account_holder_wallet')
    def test_update_pos_account__account_holder_migrated_from_external_source(
        self,
        get_account_holder_wallet_mock,
    ):
        get_account_holder_wallet_mock.return_value = MagicMock(business_id=self.pos.business_id)
        # Test Adyen
        payment_providers_account_holder_created_from_external_source_event.send(
            asdict(
                AccountHolderCreatedEventEntity(
                    provider_code=PaymentProviderCode.ADYEN,
                    account_holder=AccountHolderEntity(
                        id=uuid.uuid4(),
                        statement_name='statement_name',
                        metadata={},
                    ),
                ),
            )
        )
        self.pos.refresh_from_db()
        self.assertEqual(
            self.pos.force_stripe_kyc,
            False,
        )
        # Test Stripe
        payment_providers_account_holder_created_from_external_source_event.send(
            asdict(
                AccountHolderCreatedEventEntity(
                    provider_code=PaymentProviderCode.STRIPE,
                    account_holder=AccountHolderEntity(
                        id=uuid.uuid4(),
                        statement_name='statement_name',
                        metadata={},
                    ),
                ),
            )
        )
        self.pos.refresh_from_db()
        self.assertEqual(
            self.pos.force_stripe_kyc,
            True,
        )

    @parameterized.expand(
        [
            # Booksy Pay
            (
                # CALL_FOR_BOOKSY_PAY ==> CALL_FOR_BOOKSY_PAY_3DS
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentMethodType.CARD,
                PaymentStatus.ACTION_REQUIRED,
                receipt_status.CALL_FOR_BOOKSY_PAY,
                receipt_status.CALL_FOR_BOOKSY_PAY_3DS,
            ),
            (
                # CALL_FOR_BOOKSY_PAY ==> CALL_FOR_BOOKSY_PAY
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentMethodType.BLIK,
                PaymentStatus.ACTION_REQUIRED,
                receipt_status.CALL_FOR_BOOKSY_PAY,
                receipt_status.CALL_FOR_BOOKSY_PAY,
            ),
            (
                # CALL_FOR_BOOKSY_PAY ==> BOOKSY_PAY_FAILED
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentMethodType.CARD,
                PaymentStatus.AUTHORIZATION_FAILED,
                receipt_status.CALL_FOR_BOOKSY_PAY,
                receipt_status.BOOKSY_PAY_FAILED,
            ),
            (
                # CALL_FOR_BOOKSY_PAY ==> BOOKSY_PAY_SUCCESS
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentMethodType.CARD,
                PaymentStatus.CAPTURED,
                receipt_status.CALL_FOR_BOOKSY_PAY,
                receipt_status.BOOKSY_PAY_SUCCESS,
            ),
            (
                # CALL_FOR_BOOKSY_PAY ==> BOOKSY_PAY_FAILED
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentMethodType.CARD,
                PaymentStatus.CAPTURE_FAILED,
                receipt_status.CALL_FOR_BOOKSY_PAY,
                receipt_status.BOOKSY_PAY_FAILED,
            ),
            (
                # BOOKSY_PAY_FAILED ==> PAYMENT_CANCELED
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentMethodType.CARD,
                PaymentStatus.CANCELED,
                receipt_status.BOOKSY_PAY_FAILED,
                receipt_status.PAYMENT_CANCELED,
            ),
            # BLIK ==> PREPAYMENT_SUCCESS
            (
                PaymentTypeEnum.PREPAYMENT,
                PaymentMethodType.BLIK,
                PaymentStatus.CAPTURED,
                receipt_status.CALL_FOR_PREPAYMENT,
                receipt_status.PREPAYMENT_SUCCESS,
            ),
            # BLIK ==> PREPAYMENT_FAILED
            (
                PaymentTypeEnum.PREPAYMENT,
                PaymentMethodType.BLIK,
                PaymentStatus.AUTHORIZATION_FAILED,
                receipt_status.CALL_FOR_PREPAYMENT,
                receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
            ),
        ]
    )
    def test_basket_payment_details_updated_event_handler(
        self,
        payment_type: PaymentTypeEnum,
        payment_method: PaymentMethodType,
        payment_status: PaymentStatus,
        initial_status: str,
        expected_status: str,
    ) -> None:
        from webapps.payment_gateway.models import BalanceTransaction, Payment
        from webapps.point_of_sale.models import BasketPayment

        business = baker.make(Business)
        pos = baker.make(POS, business=business, pos_refactor_stage2_enabled=True)
        txn = baker.make(
            Transaction,
            pos=pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(Receipt, transaction=txn, status_code=initial_status)
        txn.latest_receipt = receipt
        txn.save()
        balance_transaction = baker.make(BalanceTransaction)
        basket_payment = baker.make(
            BasketPayment,
            balance_transaction_id=balance_transaction.id,
            type=BasketPaymentType.PAYMENT,
            payment_method=payment_method,
            payment_provider_code=PaymentProviderCode.STRIPE,
        )
        payment_type = baker.make(
            PaymentType,
            pos=pos,
            code=payment_type,
        )
        baker.make(
            PaymentRow,
            receipt=receipt,
            basket_payment_id=basket_payment.id,
            payment_type=payment_type,
            status=initial_status,
        )
        baker.make(
            Payment,
            balance_transaction=balance_transaction,
            status=payment_status,
        )

        basket_payment_details_updated_event_handler(asdict(basket_payment.entity))
        # Refresh both a txn and its latest_receipt as a new receipt could've been created
        txn.refresh_from_db()
        txn.latest_receipt.refresh_from_db()

        self.assertEqual(txn.latest_receipt.status_code, expected_status)

    @override_eppo_feature_flag({BasketPaymentCompletedAnalyticsFlag.flag_name: True})
    @mock.patch.object(SegmentAnalyticsWrapper, "track")
    def test_run_basket_payment_completed_analytics(self, task_mock):
        basket_payment = baker.make(
            'BasketPayment',
            type=BasketPaymentType.PAYMENT,
            payment_method=PaymentMethodType.CASH,
            payment_provider_code=PaymentProviderCode.STRIPE,
            status=BasketPaymentStatus.SUCCESS,
        )

        run_basket_payment_completed_analytics(basket_payment.entity, business_id=123)

        assert task_mock.call_count == 1
