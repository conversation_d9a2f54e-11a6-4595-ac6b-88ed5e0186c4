import functools
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from django.db.models.query_utils import Q
from model_bakery import baker

from lib.feature_flag.feature.voucher import BlockPPForServicesWithActivePackageFlag
from lib.serializers import flatten_errors
from lib.tests.utils import override_feature_flag
from lib.tools import tznow
from service.pos.business_pos import (
    NoShowProtectionFeeAllServicesHandler,
)
from webapps.business.baker_recipes import (
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    NoShowProtectionType,
    PriceType,
    RateType,
)
from webapps.business.models import (
    ComboMembership,
    Business,
    Service,
    ServiceCategory,
    ServiceVariant,
    ServiceVariantChangelog,
    ServiceVariantPayment,
)
from webapps.experiment_v3.management.commands.migrate import (
    initialize_experiments,
)
from webapps.pos.serializers import (
    NoShowFeeSerializer,
    NoShowFeeWizardServiceSerializer,
    UpdateNoShowFeeSerializer,
    UpdateNoShowFeeWizardServicesSerializer,
)
from webapps.pos.tests import TestCaseWithSetUp
from webapps.pos.tools import get_service_variants_prices
from webapps.purchase.models import (
    Subscription,
    SubscriptionListing,
)
from webapps.voucher.models import (
    VoucherTemplate,
    Voucher,
    VoucherTemplateServiceVariant,
)


@pytest.mark.django_db
class TestNoShowWizard(TestCaseWithSetUp):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()

        initialize_experiments()

        self.service = service_recipe.make(
            business=self.business,
        )

        # Completly valid
        self.sv1 = service_variant_recipe.make(
            service=self.service,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FIXED,
        )
        # Amount too small
        self.sv2 = service_variant_recipe.make(
            service__business=self.business,
            duration=relativedelta(minutes=15),
            price=4,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )
        # Starts at
        self.sv3 = service_variant_recipe.make(
            service__business=self.business,
            duration=relativedelta(minutes=20),
            price=20,
            valid_from=tznow(),
            type=PriceType.STARTS_AT,
            time_slot_interval=relativedelta(minutes=30),
        )
        # Free
        self.sv4 = service_variant_recipe.make(
            service__business=self.business,
            duration=relativedelta(minutes=25),
            valid_from=tznow(),
            type=PriceType.FREE,
            time_slot_interval=relativedelta(minutes=30),
        )
        # Don't show
        self.sv5 = service_variant_recipe.make(
            service__business=self.business,
            duration=relativedelta(minutes=30),
            valid_from=tznow(),
            type=PriceType.DONT_SHOW,
            time_slot_interval=relativedelta(minutes=30),
        )
        # Varies
        self.sv6 = service_variant_recipe.make(
            service__business=self.business,
            duration=relativedelta(minutes=35),
            valid_from=tznow(),
            type=PriceType.VARIES,
            time_slot_interval=relativedelta(minutes=30),
        )

        self.service_category = service_category_recipe.make(
            business=self.business,
        )

        self.service_with_category1 = service_recipe.make(
            service_category=self.service_category,
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        self.service_with_category2 = service_recipe.make(
            service_category=self.service_category,
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        self.sv_with_category1 = service_variant_recipe.make(
            service=self.service_with_category1,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FIXED,
        )

        self.sv_with_category2 = service_variant_recipe.make(
            service=self.service_with_category1,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FIXED,
        )

        self.sv_with_category3 = service_variant_recipe.make(
            service=self.service_with_category2,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FIXED,
        )

        self.service_variants = [
            self.sv1,
            self.sv2,
            self.sv3,
            self.sv4,
            self.sv5,
            self.sv6,
            self.sv_with_category1,
            self.sv_with_category2,
            self.sv_with_category3,
        ]

        self.product = baker.make(
            SubscriptionListing,
            id=228,
        )

        # CF9 subscription
        baker.make(
            SubscriptionListing,
            id=229,
        )
        # CF29 subscription
        baker.make(
            SubscriptionListing,
            id=230,
        )

        baker.make(
            Subscription,
            business=self.business,
            source=Business.PaymentSource.BRAINTREE,
            product=self.product,
        )

    def _test_get(self):
        serializer = NoShowFeeSerializer(
            instance=self.pos,
            context={
                'business': self.business,
            },
        )

        # Make one list
        service_ids = set()
        filtered_categories = []
        for category in serializer.data['service_categories']:
            if category['category_name'] == self.service_category.name:
                filtered_categories.append(category)

            for service_data in category['services']:
                service_ids.add(service_data['id'])

        assert len(filtered_categories) == 1
        assert len(filtered_categories[0]['services']) == 2

        not_should_be = {self.sv4.service_id, self.sv5.service_id, self.sv6.service_id}

        for sv in self.service_variants:
            if sv.service_id in service_ids and sv.service_id in not_should_be:
                assert False

    def _get_updated_services(self):
        service_prices = get_service_variants_prices(self.business)

        serializer = NoShowFeeSerializer(
            instance=self.pos,
            context={
                'business': self.business,
            },
        )

        updated_services = NoShowProtectionFeeAllServicesHandler.prepare_services(
            data=serializer.data, payment_percentage=50
        )

        for service_data in updated_services:
            if not service_data['no_show_protection']:
                continue

            service_data['no_show_protection']['type'] = ServiceVariantPayment.CANCELLATION_FEE_TYPE

        return service_prices, updated_services

    def test_get_serializer(self):
        """Check if only editable services via wizard are get."""
        self._test_get()

    def test_put_serializer(self):
        service_prices, updated_services = self._get_updated_services()

        serializer = UpdateNoShowFeeSerializer(
            data={
                'services': updated_services,
            },
            instance=self.pos,
            context={
                'business': self.business,
                # until we validate we don't know which service
                # will be updated, that why need get all prices here
                # and validate payment_amount of each
                'service_prices': service_prices,
            },
        )

        serializer.is_valid()
        print(serializer.errors)
        assert serializer.is_valid()

        serializer.save(operator_id=self.user.id)
        self._test_get()


@pytest.mark.django_db
class TestNoShowServicesWizard(TestNoShowWizard):
    def test_service_now_show_get_endpoint_list(self) -> None:
        new_services_variants_with_price_count = 5
        service = baker.make(
            Service,
            name='New Service',
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )
        expected_variants_isd = []
        for _ in range(new_services_variants_with_price_count):
            service_variant = baker.make(
                ServiceVariant,
                service=service,
                duration=relativedelta(minutes=10),
                price=50,
                valid_from=tznow(),
                type=PriceType.FIXED,
            )
            expected_variants_isd.append(service_variant.id)

        serializer = NoShowFeeWizardServiceSerializer(
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        data = serializer.data
        service_without_category = list(
            filter(
                lambda service_without_category: service_without_category['id'] == service.id,
                data['service_categories'][0]['services'],
            )
        )[0]
        variants_ids = list(
            map(lambda variant: variant['id'], service_without_category['variants'])
        )

        # Only variants with fixed prices should be showed
        assert expected_variants_isd.sort() == variants_ids.sort()

    def test_multiple_services_per_category_listed(self) -> None:
        """
        Ensures that the returned service categories contain full list of
        services (and not only the first one).
        Presented services must have all variants with their prices defined
        as in Booksy 3.0 the wizard does not support payment-amount NSP
        assignment.
        """
        # Create additional services
        new_services_count = 5
        for i in range(new_services_count):
            service = baker.make(
                Service,
                name=f'New Service {i}',
                business=self.business,
                gap_time=relativedelta(minutes=0),
                padding_time=relativedelta(minutes=30),
            )
            baker.make(
                ServiceVariant,
                service=service,
                duration=relativedelta(minutes=10),
                price=50,
                valid_from=tznow(),
                type=PriceType.FIXED,
            )
        serializer = NoShowFeeWizardServiceSerializer(
            instance=self.pos,
            context={
                'business': self.business,
            },
        )

        service_categories = serializer.data['service_categories']
        service_count = functools.reduce(
            lambda acc, service_category: acc + len(service_category['services']),
            service_categories,
            0,
        )
        assert (
            service_count
            == self.business.services.filter(
                deleted__isnull=True,
                active=True,
            )
            .filter(
                Q(service_variants__type__in=PriceType.has_price()),
                service_variants__active=True,
            )
            .distinct()
            .count()
        )

    def test_nsp_service_error_codes(self) -> None:
        # cannot use pytest.mark.parametrize when class inherits from unittest.TestCase
        params = [
            (
                {'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE},
                'payment_amount_or_percentage_required',
            ),
            (
                {'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE, 'payment_amount': 1},
                'minimal_payment_amount',
            ),
        ]

        for nsp_parameters, expected_code in params:
            serializer = UpdateNoShowFeeWizardServicesSerializer(
                data={
                    'services': [
                        {
                            'id': self.service.id,
                            'name': self.service.name,
                            'no_show_protection': nsp_parameters,
                        }
                    ]
                },
                instance=self.pos,
                context={'business': self.business},
            )
            assert not serializer.is_valid()
            assert flatten_errors(serializer.errors)[0]['code'] == expected_code

    @override_feature_flag({BlockPPForServicesWithActivePackageFlag: True})
    def test_service_no_show_propagates_to_all_service_variants(self) -> None:
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            'payment_amount': '5',
                        },
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        assert serializer.is_valid(raise_exception=False)
        serializer.save(self.user.id)
        service_variant_ids = self.service.service_variants.filter(
            active=True,
        ).values_list(
            'id',
            flat=True,
        )
        assert ServiceVariantPayment.objects.filter(
            service_variant_id__in=service_variant_ids,
        ).count() == len(service_variant_ids)
        for service_variant_payment in ServiceVariantPayment.objects.filter(
            service_variant_id__in=service_variant_ids,
        ):
            service_variant_payment: ServiceVariantPayment
            assert service_variant_payment.payment_amount == 5

    @override_feature_flag({BlockPPForServicesWithActivePackageFlag: True})
    def test_block_pp_for_service_with_active_package_template(self):
        pg_template = baker.make(
            VoucherTemplate,
            type=Voucher.VOUCHER_TYPE__PACKAGE,
            valid_till=VoucherTemplate.DAYS_30,
            pos=self.pos,
            active=True,
        )
        baker.make(
            VoucherTemplateServiceVariant,
            voucher_template=pg_template,
            service_variant=self.sv1,
            amount=2,
            item_price=19.28,
        )
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.PRE_PAYMENT_TYPE,
                            'payment_amount': '5',
                        },
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        assert serializer.is_valid() is False
        assert serializer.errors['services'][0]['non_field_errors'][0] == (
            f"Deposit can't be set, because {self.service.name} is a part of an existing package."
        )
        assert serializer.errors['services'][0]['non_field_errors'][0].code == 'package_with_pp'

    def test_unset_no_show_protection(self) -> None:
        service_variant_ids = list(
            self.service.service_variants.filter(
                active=True,
            ).values_list(
                'id',
                flat=True,
            )
        )
        ServiceVariantPayment.objects.bulk_create(
            [
                ServiceVariantPayment(
                    service_variant_id=service_variant_id,
                    payment_type=NoShowProtectionType.PREPAYMENT,
                    payment_amount=Decimal('5.00'),
                )
                for service_variant_id in service_variant_ids
            ]
        )

        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': None,
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        assert serializer.is_valid(raise_exception=False)
        serializer.save(self.user.id)
        assert (
            ServiceVariantPayment.objects.filter(
                service_variant_id__in=service_variant_ids,
            ).count()
            == 0
        )

    def test_percentage_nsp_for_services_with_different_variant_prices(self) -> None:
        self.service2 = baker.make(
            Service,
            name="Nazwa podstawowa2",
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )
        self.service3 = baker.make(
            Service,
            name="Nazwa podstawowa3",
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        for service in [self.service2, self.service3]:
            baker.make(
                ServiceVariant,
                service=service,
                duration=relativedelta(minutes=10),
                price=50,
                valid_from=tznow(),
                type=PriceType.FIXED,
            )

            baker.make(
                ServiceVariant,
                service=service,
                duration=relativedelta(minutes=10),
                price=100,
                valid_from=tznow(),
                type=PriceType.FIXED,
            )

        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service2.id,
                        'name': self.service2.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            'percentage': '50',
                        },
                    },
                    {
                        'id': self.service3.id,
                        'name': self.service3.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.PRE_PAYMENT_TYPE,
                            'percentage': '100',
                        },
                    },
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )

        assert serializer.is_valid()
        serializer.save(self.user.id)

        service2_variant_payments = list(
            ServiceVariantPayment.objects.filter(
                service_variant_id__in=self.service2.service_variants.filter(
                    active=True,
                ).values_list(
                    'id',
                    flat=True,
                ),
            )
        )
        assert len(service2_variant_payments) == 2
        for sv in service2_variant_payments:
            assert sv.payment_type == ServiceVariantPayment.CANCELLATION_FEE_TYPE
            assert Decimal(sv.service_variant.price) * Decimal(0.5) == sv.payment_amount

        service3_variant_payments = list(
            ServiceVariantPayment.objects.filter(
                service_variant_id__in=self.service3.service_variants.filter(
                    active=True,
                ).values_list(
                    'id',
                    flat=True,
                ),
            )
        )
        assert len(service3_variant_payments) == 2
        for sv in service3_variant_payments:
            assert sv.payment_type == ServiceVariantPayment.PRE_PAYMENT_TYPE
            assert Decimal(sv.service_variant.price) == sv.payment_amount

    def test_with_combo_custom_prices(self):
        service_variant = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Child',
            ),
            price=Decimal('100.00'),
            type=PriceType.FIXED,
            payment__payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment__payment_amount=40,
            payment__saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        combo_1 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Combo 1',
                combo_type=ComboType.SEQUENCE,
            ),
            combo_pricing=ComboPricing.CUSTOM,
            type=None,
            price=None,
        )
        baker.make(
            ComboMembership,
            combo=combo_1,
            child=service_variant,
            price=Decimal('10.0'),
            type=PriceType.FIXED,
        )
        baker.make(
            ComboMembership,
            combo=combo_1,
            child=service_variant,
            price=Decimal('1.0'),
            type=PriceType.FIXED,
            deleted=tznow(),
        )

        combo_2 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Combo 2',
                combo_type=ComboType.SEQUENCE,
            ),
            combo_pricing=ComboPricing.CUSTOM,
            type=None,
            price=None,
        )
        baker.make(
            ComboMembership,
            combo=combo_2,
            child=service_variant,
            price=Decimal('12.0'),
            type=PriceType.FIXED,
        )

        data = {
            'services': [
                {
                    'id': service_variant.service.id,
                    'no_show_protection': {
                        'type': ServiceVariantPayment.PRE_PAYMENT_TYPE,
                        'percentage': '40',
                    },
                },
            ],
        }
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data=data,
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        self.assertFalse(serializer.is_valid())
        self.assertSetEqual(
            {error.code for error in serializer.errors['services'][0]['non_field_errors']},
            {'minimal_payment_amount'},
        )

        data['services'][0]['no_show_protection']['percentage'] = '50'
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data=data,
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        self.assertTrue(serializer.is_valid())

    def test_bump_service_variants_version(self) -> None:
        """
        Changing No-show-protection should bump `ServiceVariant.version` and
        new no-show-protection settings should be included in ServiceVariantChangelog.
        """
        user = self.staffer.staff_user
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            'payment_amount': '5',
                        },
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
                'user': user,
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save(self.user.id)

        changelogs = ServiceVariantChangelog.objects.filter(
            service_variant__service_id=self.service.id,
        )
        self.assertEqual(len(changelogs), 1)
        self.assertEqual(changelogs[0].requested_by, user)
        self.assertEqual(
            changelogs[0].data,
            changelogs[0].data
            | {
                'payment_amount': 5.0,
                'payment_saving_type': RateType.PERCENTAGE.value,
                'payment_type': NoShowProtectionType.CANCELLATION_FEE.value,
                'version': self.sv1.version + 1,
            },
        )

    def test_deleted_payment_visible_on_changelog(self):
        user = self.staffer.staff_user

        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            'payment_amount': '5',
                        },
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
                'user': user,
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save(self.user.id)

        changelogs = ServiceVariantChangelog.objects.filter(
            service_variant__service_id=self.service.id
        )
        self.assertEqual(len(changelogs), 1)
        self.assertEqual(changelogs[0].requested_by, user)
        self.assertIsNotNone(changelogs[0].data['payment_amount'])
        self.assertIsNotNone(changelogs[0].data['payment_saving_type'])
        self.assertIsNotNone(changelogs[0].data['payment_type'])

        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': None,
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
                'user': user,
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save(self.user.id)

        changelogs = ServiceVariantChangelog.objects.filter(
            service_variant__service_id=self.service.id
        )
        self.assertEqual(len(changelogs), 2)
        self.assertIsNone(changelogs[0].data['payment_amount'])
        self.assertIsNone(changelogs[0].data['payment_saving_type'])
        self.assertIsNone(changelogs[0].data['payment_type'])

    def test_turn_off_auto_charge_cancellation_fee(self):
        self.pos.auto_charge_cancellation_fee = True
        self.pos.save()
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            'payment_amount': '5',
                        },
                    }
                ],
                'auto_charge_cancellation_fee': False,
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        serializer.is_valid()
        serializer.save(self.user.id)
        assert self.pos.auto_charge_cancellation_fee is False


@pytest.mark.django_db
class TestNoShowServicesWizardSpecialCases(TestNoShowWizard):
    def test_all_services_have_category(self) -> None:
        Service.objects.all().delete()
        ServiceVariant.objects.all().delete()
        service_cat = baker.make(ServiceCategory, name='Category A')
        service = baker.make(
            Service,
            name='Service A',
            service_category=service_cat,
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FIXED,
        )

        serializer = NoShowFeeWizardServiceSerializer(
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        data = serializer.data
        assert len(data['service_categories'][0]['services'][0]['variants']) == 1

    def test_save_with_variants_having_no_version(self):
        service_variants = ServiceVariant.objects.filter(service_id=self.service.id)
        service_variants.update(version=None)

        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.PRE_PAYMENT_TYPE,
                            'percentage': '100',
                        },
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        serializer.is_valid(raise_exception=True)
        serializer.save(operator_id=self.user.id)

        self.assertSetEqual(set(service_variants.values_list('version', flat=True)), {1})

    def test_service_with_free_variant(self) -> None:
        service_with_inactive_no_price_variant = baker.make(
            Service,
            name='New Service 1',
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )
        baker.make(
            ServiceVariant,
            service=service_with_inactive_no_price_variant,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FIXED,
        )
        baker.make(
            ServiceVariant,
            service=service_with_inactive_no_price_variant,
            duration=relativedelta(minutes=10),
            price=50,
            valid_from=tznow(),
            type=PriceType.FREE,
            active=False,
        )

        serializer = NoShowFeeWizardServiceSerializer(
            instance=self.pos,
            context={
                'business': self.business,
            },
        )

        service_categories = serializer.data['service_categories']
        service_ids = list(map(lambda service: service['id'], service_categories[0]['services']))
        assert service_with_inactive_no_price_variant.id in service_ids

    def test_service_nsp_with_skip_variant_flag(self) -> None:
        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data={
                'services': [
                    {
                        'id': self.service.id,
                        'name': self.service.name,
                        'no_show_protection': {
                            'type': ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            'payment_amount': '5',
                            'skip_variant_if_no_price': False,
                        },
                    }
                ],
            },
            instance=self.pos,
            context={
                'business': self.business,
            },
        )
        assert serializer.is_valid(raise_exception=False)
        serializer.save(self.user.id)
        service_variant_ids = self.service.service_variants.filter(
            active=True,
        ).values_list(
            'id',
            flat=True,
        )
        assert ServiceVariantPayment.objects.filter(
            service_variant_id__in=service_variant_ids,
        ).count() == len(service_variant_ids)


@pytest.mark.django_db
def test_update_or_create_payment():
    service_variant = service_variant_recipe.make()

    no_show_protection_1 = {
        'payment_amount': 25,
        'payment_type': NoShowProtectionType.CANCELLATION_FEE.value,
    }
    UpdateNoShowFeeSerializer.update_or_create_payment(
        service_variant, no_show_protection_1, tznow()
    )
    assert service_variant.payment.payment_amount == no_show_protection_1['payment_amount']
    assert service_variant.payment.payment_type == no_show_protection_1['payment_type']
    assert service_variant.payment.deleted is None

    service_variant.payment.delete()

    no_show_protection_2 = {
        'payment_amount': 30,
        'payment_type': NoShowProtectionType.PREPAYMENT.value,
    }
    UpdateNoShowFeeSerializer.update_or_create_payment(
        service_variant, no_show_protection_2, tznow()
    )
    service_variant.payment.refresh_from_db()
    assert service_variant.payment.payment_amount == no_show_protection_2['payment_amount']
    assert service_variant.payment.payment_type == no_show_protection_2['payment_type']
    assert service_variant.payment.deleted is None
