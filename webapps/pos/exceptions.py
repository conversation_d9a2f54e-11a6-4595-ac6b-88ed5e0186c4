import uuid
from service.exceptions import ServiceError


class BlikActionRequired(Exception):

    def __init__(
        self,
        balance_transaction_id: uuid.UUID,
        *args,
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.balance_transaction_id = balance_transaction_id


class PaymentRowNotFound(Exception):
    pass


class InvalidPaymentMethod(ServiceError):
    pass


class RefundNotPossibleException(Exception): ...
