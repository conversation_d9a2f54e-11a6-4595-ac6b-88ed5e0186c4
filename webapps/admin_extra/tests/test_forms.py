from datetime import datetime, timedelta

import pytz
from django.conf import settings
from django.contrib.admin import AdminSite
from django.test import override_settings
from django.urls.base import reverse
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized

from lib.tools import tznow
from webapps.admin_extra.forms.sms import PrepaidSMSPackageUpdateForm
from webapps.admin_extra.tests import DjangoTestCase
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.public_partners.admin.oauth2_installation import (
    OAuth2InstallationAdminInline,
    OAuth2InstallationModelForm,
    PartnerAppsAdminInline,
)
from webapps.public_partners.models import OAuth2Installation, OAuth2Application
from webapps.public_partners.enum import OAuth2InstallationStatusEnum
from webapps.purchase.cache import _get_sms_allowance
from webapps.purchase.models import (
    SMSPackage,
    SubscriptionListing,
    Subscription,
    SubscriptionSMSPackage,
    SubscriptionHistory,
)


@freeze_time(datetime(2022, 1, 10, tzinfo=pytz.UTC))
class TestPrepaidSMSPackageUpdateForm(DjangoTestCase):
    def _create_product(self, source):
        sms_package = baker.make(
            SMSPackage,
            amount_per_billing_cycle=123,
            chargeable=False,
        )
        if source == Business.PaymentSource.OFFLINE:
            return baker.make(
                SubscriptionListing,
                sms_package=sms_package,
            )
        if source == Business.PaymentSource.PLAY:
            return baker.make(
                SubscriptionListing,
                sms_package=sms_package,
                google_id='qwe123',
            )
        if source == Business.PaymentSource.ITUNES:
            return baker.make(
                SubscriptionListing,
                sms_package=sms_package,
                apple_id='qwe123',
            )
        if source == Business.PaymentSource.BRAINTREE:
            return baker.make(
                SubscriptionListing,
                sms_package=sms_package,
                braintree_id='qwe123',
            )

    def setUp(self):
        self.new_package = baker.make(
            SMSPackage,
            amount_per_billing_cycle=333,
            chargeable=False,
        )

    @parameterized.expand(
        [
            (Business.PaymentSource.OFFLINE, True, 333, 333, 4),
            (Business.PaymentSource.OFFLINE, False, 138, 155, 4),
            (Business.PaymentSource.ITUNES, True, 333, 333, 4),
            (Business.PaymentSource.ITUNES, False, 138, 155, 5),
            (Business.PaymentSource.PLAY, True, 333, 333, 5),
            (Business.PaymentSource.PLAY, False, 138, 155, 5),
        ]
    )
    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_valid_current_sub__invalid_amount_per_billing_cycle(
        self,
        source,
        replace_for_existing,
        current_limit,
        future_limit,
        packages_count,
    ):
        product = self._create_product(source)
        now_ = tznow()
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            payment_source=source,
        )
        current_sub = baker.make(  # default package from product for 123 limit is created
            Subscription,
            business=business,
            product=product,
            start=datetime(2022, 1, 1, tzinfo=pytz.UTC),
            expiry=datetime(2022, 6, 1, tzinfo=pytz.UTC),
            source=source,
        )
        # additional current packages
        baker.make(
            SubscriptionSMSPackage,
            subscription=current_sub,
            date_start=current_sub.start,
            amount_per_billing_cycle=15,
        )
        # past package
        baker.make(
            SubscriptionSMSPackage,
            subscription=current_sub,
            date_start=current_sub.start - timedelta(days=10),
            date_end=now_ - timedelta(days=1),
            amount_per_billing_cycle=8,
        )
        # future package
        baker.make(
            SubscriptionSMSPackage,
            subscription=current_sub,
            date_start=now_ + timedelta(days=1),
            amount_per_billing_cycle=17,
        )

        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': replace_for_existing,
            },
        )
        self.assertTrue(form.is_valid())

        form.save()

        product.refresh_from_db()
        self.assertEqual(product.sms_package, self.new_package)
        packages_count = 5 if replace_for_existing else 4
        self.assertEqual(current_sub.sms_packages.count(), packages_count)

        _get_sms_allowance.cache_clear()
        self.assertEqual(
            _get_sms_allowance(current_sub.business_id)['prepaid_sms_count'], current_limit
        )

        with freeze_time(datetime(2022, 2, 10)):
            _get_sms_allowance.cache_clear()
            self.assertEqual(
                _get_sms_allowance(current_sub.business_id)['prepaid_sms_count'], future_limit
            )

        self.assertEqual(SubscriptionHistory.objects.filter(subscription=current_sub).count(), 4)

    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_valid_current_sub__valid_amount_per_billing_cycle(self):
        product = self._create_product(Business.PaymentSource.OFFLINE)
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            payment_source=Business.PaymentSource.OFFLINE,
        )
        current_sub = baker.make(  # default package from product for 123 limit is created
            Subscription,
            business=business,
            product=product,
            start=datetime(2022, 1, 1, tzinfo=pytz.UTC),
            expiry=datetime(2022, 6, 1, tzinfo=pytz.UTC),
            source=Business.PaymentSource.OFFLINE,
        )
        # additional current packages
        baker.make(
            SubscriptionSMSPackage,
            subscription=current_sub,
            date_start=current_sub.start,
            amount_per_billing_cycle=210,  # 123 + 210 = 333 as or new package
        )

        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': True,
            },
        )
        self.assertTrue(form.is_valid())
        form.save()

        product.refresh_from_db()
        self.assertEqual(product.sms_package, self.new_package)
        self.assertEqual(current_sub.sms_packages.count(), 2)

        _get_sms_allowance.cache_clear()
        self.assertEqual(_get_sms_allowance(current_sub.business_id)['prepaid_sms_count'], 333)

        with freeze_time(datetime(2022, 2, 10)):
            _get_sms_allowance.cache_clear()
            self.assertEqual(_get_sms_allowance(current_sub.business_id)['prepaid_sms_count'], 333)

        self.assertEqual(SubscriptionHistory.objects.filter(subscription=current_sub).count(), 2)

    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_valid__max_number_of_queries(self):
        product = self._create_product(Business.PaymentSource.OFFLINE)
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            payment_source=Business.PaymentSource.OFFLINE,
        )
        current_subs = baker.make(  # default package from product for 123 limit is created
            Subscription,
            business=business,
            product=product,
            start=datetime(2022, 1, 1, tzinfo=pytz.UTC),
            expiry=datetime(2022, 6, 1, tzinfo=pytz.UTC),
            source=Business.PaymentSource.OFFLINE,
            _quantity=20,
        )
        for current_sub in current_subs:
            # additional current packages
            baker.make(
                SubscriptionSMSPackage,
                subscription=current_sub,
                date_start=current_sub.start,
                amount_per_billing_cycle=11,
            )

        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': True,
            },
        )
        self.assertTrue(form.is_valid())
        with self.assertNumQueries(6 + 2):  # 2 additional savepoint queries
            # 1 select sms package
            # 1 update product sms package
            # 1 end current packages
            # 1 select subscriptions
            # 1 create new packages
            # 1 count
            form.save()

    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_valid__same_package(self):
        product = self._create_product(Business.PaymentSource.OFFLINE)
        product.sms_package = self.new_package
        product.save()
        last_update = product.updated

        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': False,
            },
        )
        self.assertTrue(form.is_valid())
        form.save()

        product.refresh_from_db()
        self.assertEqual(product.updated, last_update)
        self.assertEqual(product.sms_package, self.new_package)

    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_invalid__braintree(self):
        product = self._create_product(Business.PaymentSource.BRAINTREE)
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            payment_source=Business.PaymentSource.BRAINTREE,
        )
        baker.make(  # default package from product for 123 limit is created
            Subscription,
            business=business,
            product=product,
            start=datetime(2022, 1, 1, tzinfo=pytz.UTC),
            expiry=datetime(2022, 6, 1, tzinfo=pytz.UTC),
        )
        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': True,
            },
        )
        self.assertFalse(form.is_valid())
        self.assertIn('product', form.errors)

    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_invalid__chargeable_package(self):
        product = self._create_product(Business.PaymentSource.OFFLINE)
        business = baker.make(
            Business,
            status=Business.Status.PAID,
        )
        baker.make(  # default package from product for 123 limit is created
            Subscription,
            business=business,
            product=product,
            start=datetime(2022, 1, 1, tzinfo=pytz.UTC),
            expiry=datetime(2022, 6, 1, tzinfo=pytz.UTC),
        )
        self.new_package.chargeable = True
        self.new_package.save()
        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': True,
            },
        )
        self.assertFalse(form.is_valid())
        self.assertIn('sms_package', form.errors)

    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    def test_form_valid__past_subscription(self):
        product = self._create_product(Business.PaymentSource.OFFLINE)
        business = baker.make(
            Business,
            status=Business.Status.OVERDUE,
        )
        past_sub = baker.make(  # default package from product for 123 limit is created
            Subscription,
            business=business,
            product=product,
            start=datetime(2021, 1, 1, tzinfo=pytz.UTC),
            expiry=datetime(2021, 12, 31, tzinfo=pytz.UTC),
            source=Business.PaymentSource.OFFLINE,
        )

        # additional current packages
        baker.make(
            SubscriptionSMSPackage,
            subscription=past_sub,
            date_start=past_sub.start,
            amount_per_billing_cycle=11,
            _quantity=5,
        )
        form = PrepaidSMSPackageUpdateForm(
            data={
                'product': product,
                'sms_package': self.new_package,
                'replace_for_existing': True,
            },
        )
        self.assertTrue(form.is_valid())
        form.save()
        self.assertEqual(SubscriptionSMSPackage.objects.count(), 6)
        _get_sms_allowance.cache_clear()
        self.assertEqual(
            _get_sms_allowance(past_sub.business_id)['prepaid_sms_count'], 178
        )  # 123 + 55


class OASetupTestCase(DjangoTestCase):
    def setUp(self):
        self.site = AdminSite()
        self.booksy_med_business = business_recipe.make()
        self.booksy_med_user = self.booksy_med_business.owner
        self.booksy_med_application = baker.make(
            OAuth2Application,
            client_id=settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,
        )
        self.booksy_med_application_business = baker.make(
            OAuth2Installation,
            application=self.booksy_med_application,
            business=self.booksy_med_business,
            user=self.booksy_med_user,
            status=OAuth2Installation.STATUSES.PAID,
        )
        self.business = business_recipe.make()
        self.user = self.business.owner
        self.application = baker.make(
            OAuth2Application,
            client_id="NOT_BOOKSY_MED",
        )
        self.application_business = baker.make(
            OAuth2Installation,
            application=self.application,
            business=self.business,
            user=self.user,
            status=OAuth2Installation.STATUSES.PAID,
        )


class PartnerAppsAdminInlineTestCase(OASetupTestCase):
    def setUp(self):
        super().setUp()
        self.login_admin()

    def test_inline_options(self):
        inline = PartnerAppsAdminInline(self.application_business, self.site)
        self.assertEqual(
            [
                ('A', 'Active'),
                ('P', 'Paid'),
                ('E', 'Pending Cancellation'),
                ('O', 'Overdue Blocked'),
                ('C', 'Churned'),
                ('S', 'Suspended'),
                ('D', 'Disabled'),
                ('B', 'Blocked'),
            ],
            inline.form.base_fields['status'].choices,
        )

    def test_business_with_partner_apps_installation_pa_admin_inline_choices(self):
        url = reverse('admin:business_business_change', args=(self.business.id,))
        inlines = self.client.get(url).context_data['adminform'].model_admin.inlines
        self.assertIn(PartnerAppsAdminInline, inlines)
        pa_admin_inline_choices = (
            next(inline for inline in inlines if inline == PartnerAppsAdminInline)
            .form.base_fields['status']
            .choices
        )
        self.assertEqual(
            [
                ('A', 'Active'),
                ('P', 'Paid'),
                ('E', 'Pending Cancellation'),
                ('O', 'Overdue Blocked'),
                ('C', 'Churned'),
                ('S', 'Suspended'),
                ('D', 'Disabled'),
                ('B', 'Blocked'),
            ],
            pa_admin_inline_choices,
        )

    def test_business_without_partner_apps_installation(self):
        business = business_recipe.make()
        url = reverse('admin:business_business_change', args=(business.id,))
        inlines = self.client.get(url).context_data['adminform'].model_admin.inlines
        self.assertIn(PartnerAppsAdminInline, inlines)
        pa_admin_inline_choices = (
            next(inline for inline in inlines if inline == PartnerAppsAdminInline)
            .form.base_fields['status']
            .choices
        )
        self.assertEqual(
            [
                ('A', 'Active'),
                ('P', 'Paid'),
                ('E', 'Pending Cancellation'),
                ('O', 'Overdue Blocked'),
                ('C', 'Churned'),
                ('S', 'Suspended'),
                ('D', 'Disabled'),
                ('B', 'Blocked'),
            ],
            pa_admin_inline_choices,
        )


class OAuth2InstallationModelFormTest(OASetupTestCase):

    def setUp(self):
        super().setUp()
        self.statuses = OAuth2InstallationStatusEnum
        self.login_admin()

    def test_form_choices_for_booksy_med_not_paid(self):
        self.booksy_med_application_business.status = self.statuses.ACTIVE
        self.booksy_med_application_business.save()
        form = OAuth2InstallationModelForm(instance=self.booksy_med_application_business)
        self.assertEqual([('A', 'ACTIVE')], form.fields["status"].choices)

    def test_form_choices_for_booksy_med_paid(self):
        form = OAuth2InstallationModelForm(instance=self.booksy_med_application_business)
        self.assertEqual(
            [('P', 'PAID'), ('E', 'PENDING_CANCELLATION'), ('C', 'CHURNED')],
            form.fields["status"].choices,
        )

    def test_form_choices_for_booksy_med_pending_cancellation(self):
        self.booksy_med_application_business.status = self.statuses.PENDING_CANCELLATION
        self.booksy_med_application_business.save()
        form = OAuth2InstallationModelForm(instance=self.booksy_med_application_business)
        self.assertEqual(
            [
                ('E', 'PENDING_CANCELLATION'),
                ('P', 'PAID'),
                ('C', 'CHURNED'),
                ('O', 'OVERDUE_BLOCKED'),
            ],
            form.fields["status"].choices,
        )

    def test_form_choices_for_booksy_med_overdue_blocked(self):
        self.booksy_med_application_business.status = self.statuses.OVERDUE_BLOCKED
        self.booksy_med_application_business.save()
        form = OAuth2InstallationModelForm(instance=self.booksy_med_application_business)
        self.assertEqual(
            [
                ('O', 'OVERDUE_BLOCKED'),
                ('P', 'PAID'),
                ('E', 'PENDING_CANCELLATION'),
                ('C', 'CHURNED'),
            ],
            form.fields["status"].choices,
        )

    def test_form_choices_for_booksy_med_non_existing_status(self):
        self.booksy_med_application_business.status = "UNKNWN"
        self.booksy_med_application_business.save()
        form = OAuth2InstallationModelForm(instance=self.booksy_med_application_business)
        self.assertEqual([], form.fields["status"].choices)

    def test_form_choices(self):
        self.application_business.status = self.statuses.ACTIVE
        self.application_business.save()
        form = OAuth2InstallationModelForm(instance=self.application_business)
        self.assertEqual(
            [('A', 'ACTIVE'), ('P', 'PAID'), ('D', 'DISABLED'), ('B', 'BLOCKED')],
            form.fields["status"].choices,
        )


class OAuth2InstallationAdminInlineTestCase(OASetupTestCase):
    def test_inline_options(self):
        inline = OAuth2InstallationAdminInline(self.booksy_med_application_business, self.site)
        self.assertEqual(
            [
                ('A', 'Active'),
                ('P', 'Paid'),
                ('E', 'Pending Cancellation'),
                ('O', 'Overdue Blocked'),
                ('C', 'Churned'),
                ('S', 'Suspended'),
                ('D', 'Disabled'),
                ('B', 'Blocked'),
            ],
            inline.form.base_fields['status'].choices,
        )
