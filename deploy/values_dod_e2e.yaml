deploy:
  admin: true
  api: true
  celeryAllQueueWorker: true
  grpcApi: true
  publicApi: true
  init: false
  dbInit: true
  elasticInit: true
  executeScriptsInit: true
  otherInit: true

variables:
  subdomainHost: booksy-subdomains-api-grpc.devops-tools.svc.cluster.local:9099
  authHost: auth-server.devops-tools.svc.cluster.local:8010
  loggingUseStdout: "false"
  truncate_logs: "false"
  apiUwsgiProcesses: 1
  apiUwsgiThreads: 4
  adminUwsgiProcesses: 1
  adminUwsgiThreads: 2
  adminUwsgiLazyApp: False
  adminUwsgiForkHooks: True
  adminUwsgiRssReload: True
  adminUwsgiRssReloadThreshold: 700
  publicApiUwsgiLazyApp: False
  publicApiUwsgiForkHooks: True
  publicApiUwsgiRssReload: True
  reportsApiUwsgiLazyApp: False
  reportsApiUwsgiForkHooks: True
  reportsApiUwsgiRssReload: True
  searchApiUwsgiLazyApp: False
  searchApiUwsgiForkHooks: True
  searchApiUwsgiRssReload: True

  fiscalArchives:
    GCSProjectID: bks-dev-3-eu-w1
    GCSBucketName: bks-dev-3-eu-w1-fiscal-archives

  pubsub:
    projectID: bks-dev-3-eu-w1

extraEnvs: []

resources:
  api:
    limits:
      cpu: 3000m
      memory: 2.5Gi
    requests:
      cpu: 3000m
      memory: 2.5Gi
  admin:
    limits:
      cpu: 3000m
      memory: 1Gi
    requests:
      cpu: 3000m
      memory: 1Gi
  celeryBeat:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 1000m
      memory: 1Gi
  celeryAllQueueWorker:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 2000m
      memory: 2Gi
  grpcApi:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  publicApi:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 2000m
      memory: 2Gi
  tools:
    limits:
      cpu: 2000m
      memory: 8Gi
    requests:
      cpu: 2000m
      memory: 8Gi
  dbInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  elasticInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  executeScriptsInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  otherInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi

nodeSelector:
  adminApi:
    role: vm-worker-e2e
  admin:
    role: vm-worker-e2e
  api:
    role: vm-worker-e2e
  celeryAllQueueWorker:
    role: vm-worker-e2e
  celeryBeat:
    role: e2e-ondemand-persistance-worker
  grpcApi:
    role: vm-worker-e2e
  publicApi:
    role: vm-worker-e2e
  reportsApi:
    role: vm-worker-e2e
  tools:
    role: vm-worker-e2e
  init:
    role: vm-worker-e2e
  dbInit:
    role: vm-worker-e2e
  elasticInit:
    role: vm-worker-e2e
  executeScriptsInit:
    role: vm-worker-e2e
  otherInit:
    role: vm-worker-e2e

providerCalendarImporter:
  bucketName: bks-dev-3-eu-w1-provider-calendar-importer
  projectId: bks-dev-3-eu-w1
