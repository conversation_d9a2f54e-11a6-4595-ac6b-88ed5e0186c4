import logging
import os
import warnings
from contextlib import contextmanager
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from uuid import uuid4
from unittest.mock import patch, MagicMock

import ddtrace

from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.booking import IncentivizeWithGiftCardBackend
import pytest

import ldclient
from django.test import TestCase
from google.protobuf.json_format import MessageToDict
from ldclient.config import Config

from _pytest.monkeypatch import MonkeyPatch
from django.conf import settings
from django.db import connection
from django.core.cache import cache
from django.utils.timezone import get_current_timezone_name
from django.utils.translation import get_language
from mock import (
    MagicMock,
    patch,
)
from mock.mock import Mock
from model_bakery import baker
from urllib3 import HTTPConnectionPool
from tornado import testing

from conftest_helpers import (
    change_index_language_settings,
    clean_elastisearch_index_helper,
    delete_documents_from_index,
)
from lib.db import READ_ONLY_DB, <PERSON><PERSON>ORTS_DB, REPORTS_READ_ONLY_DB
from lib.deeplink.deeplinks_ms.protobuf.deeplinks_pb2 import (
    DeeplinkResponse,
    Fields,
    BatchDeeplinkResponse,
    BatchDeeplinkRequest,
)
from lib.feature_flag.bug import (
    AnyResourceAssignedFlag,
    DAC7PeselValidationFixFlag,
    DeeplinkToMyBooksyInBookingNotficiations,
    DoNotEvaluateBciQueryForFalsyUserEmailOrPhoneFlag,
    FixErrorInReleaseDepositOnPaymentTask,
    FixMissingReminderForRepeatingBooking,
    FixRevenueForecastAlgorithm,
    FixTotalPriceInAppointmentListReport,
    SendPushAfterAppointmentCancellationFlag,
    UseNoReplyAddressInReplyToFieldFlag,
    InvokeBCIPostSaveInUpdateUserBCITask,
)
from lib.feature_flag.experiment.booking import DontSendCustomerConfirmationSMSForBusinessBooking
from lib.feature_flag.experiment.boost import BoostFraudSuspicionWarningExperiment
from lib.feature_flag.experiment.customer import (
    UseSearchServiceForBusinessCategoryHintsExperiment,
    VistedLikedSelected4UExperiment,
)
from lib.feature_flag.feature.admin import ShowEnforcePasswordResetFlag
from lib.feature_flag.feature.boost import (
    BoostChargingForBusinessBookingsNoPromoFixFlag,
    RefactorSetChargeableTaskFlag,
    BoostUseExemptedInSetChargeableTaskFlag,
)
from lib.feature_flag.feature.booking import (
    InvokeDecoupledDryRunFromSimplifiedBooking,
    RecordBenchmarkTimeSlots,
    StreamlinedBookingAnalyticsWithSegment,
    CalendarDragStafferUpdateFlag,
    RemoveBookingCntInBulkFinishAppointments,
)
from lib.feature_flag.feature.customer import AutomaticAccountLinkingFlag, VistedLikedSelected4UFlag
from lib.feature_flag.feature.customer import BooksyAuthTempExtraMetadataFlag
from lib.feature_flag.feature.customer import CustomerMyBooksySugestedForYouGalleryFlag
from lib.feature_flag.feature.customer import FixAutomaticAccountLinkingCellPhoneFlag
from lib.feature_flag.feature.customer import NotificationReceiversFlag
from lib.feature_flag.feature.customer import SelectedForYouSearchableV2Flag
from lib.feature_flag.feature.customer import SelectedForYouExcludeBoostFlag
from lib.feature_flag.feature.customer import SessionAuthImprovementFlag
from lib.feature_flag.feature.customer import SetNotificationReceiversFlag
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.feature_flag.feature.notification import (
    EnableSendingAdditionalBookingReminderFlag,
    MoveOptOutSuffixToSettingsFlag
)
from lib.feature_flag.old_experiment import (
    CalendarChangedDatesRedisFlag,
    ElasticsearchWithoutDeleteByQueryFlag,
)
from lib.feature_flag.feature import (
    BooksyGiftcardsEnabledFlag,
    CustomerDontShowPrivateEmailFlag,
    CustomerQuickSignInUpFlag,
    CustomerSendPrivateEmail,
    HelpCenterFlag,
)
from lib.feature_flag.feature.analytics import (
    FifthCustomerBookingInFourteenDaysGTM,
    FifthCustomerBookingInFourteenDaysSegment,
)
from lib.feature_flag.feature.booking import (
    BookAgainUseReadOnlyDBFlag,
    DecoupledDryRunFromAPI,
    DisableParallelSameComboDrawer,
    IncentivizeWithGiftCardStickyAssignmentSimulation,
    MoreStrictSameServiceCheck,
    NewParameterInBusinessDetailsLegalFootnoteVisible,
    RepeatingBookingDecoupling2,
    SimplifiedBookingTargeting,
    SendCBStartedForCustomer,
    UseNewAlgorithmForAdditionalTimeslots,
    UseNewAlgorithmForAdditionalTimeslotsExperiment,
)
from lib.feature_flag.feature.booking import (
    SubBookingComboChildrenPrefetch,
)
from lib.feature_flag.feature.business import (
    CustomComboPriceChangeLogFlag,
    DSABusinessVersumNameFlag,
    EnablePayoutsCalendarBannerFlag,
    PreventResetCurrentStafferServicesFlag,
)
from lib.feature_flag.feature.gdpr import DontSendDisclosureObligationAgreementEmailFlag
from lib.feature_flag.feature.payment import (
    EnablePayoutsPaymentsSettingBannerFlag,
    ProfileAndSettingsTTPPromoFlag,
)
from lib.feature_flag.feature.security import HCaptchaFlag
from lib.test_utils import get_pytest_current_test_name
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from webapps.images.enums import ImageTypeEnum
from webapps.pos.enums import PaymentTypeEnum

# noinspection PyUnresolvedReferences
from webapps.subdomain_grpc.tests.conftest import (
    mock_subdomain_grpc_client,
)  # pylint: disable=unused-import
from webapps.voucher.gift_card_content.gift_card_default_backgrounds import DEFAULT_BACKGROUND_URL

collect_ignore = ["domain_services/booking/tests/test_scripts/test_compare_requirements.py"]

warnings.filterwarnings(
    'error',
    r"DateTimeField .* received a naive datetime",
    RuntimeWarning,
    r'django\.db\.models\.fields',
)

TestCase.databases = '__all__'

# Workaround for https://github.com/pytest-dev/pytest/issues/12263.
# Backported fix https://github.com/tornadoweb/tornado/pull/3382/files
# removed a wrapper needed for python<3.11 that breaks pytest>8.1 code
testing._TestMethodWrapper = lambda x: x
testing.AsyncTestCase.runTest = None

_cache_booksy_auth = {}


@pytest.fixture(scope='session')
def monkeypatch_session():
    monkey_patch = MonkeyPatch()
    yield monkey_patch
    monkey_patch.undo()


@contextmanager
def disable_loggers(*loggers):
    disabled_loggers = []
    try:
        for logger in loggers:
            if not logger.disabled:
                logger.disabled = True
                disabled_loggers.append(logger)

        yield
    finally:
        for logger in disabled_loggers:
            logger.disabled = False


def get_custom_sql(filename):
    path = os.path.join(os.path.dirname(__file__), filename)
    return open(path).read().replace(r'%', r'%%')


# Do not add  @pytest.fixture(scope='session')
# this fixture reserved by pytest and will be added
# automatically by it
def pytest_configure(config):
    from django.conf import settings

    # add environment variable
    # for tests; see conditions in settings/local.py:159
    # for multithread  sys.argv will not work
    os.environ['PYTEST'] = 'PYTEST'

    # <editor-fold desc="Only one db should be present in tests">
    settings.DATABASES.pop('adyen', None)
    settings.DATABASES.pop(READ_ONLY_DB, None)
    settings.DATABASES.pop(REPORTS_DB, None)
    settings.DATABASES.pop(REPORTS_READ_ONLY_DB, None)

    # </editor-fold>
    settings.POS = True

    verbosity = config.getoption('--verbosity')

    settings.LOGGING['loggers']['booksy']['level'] = logging.ERROR
    settings.LOGGING['loggers']['celery']['level'] = logging.ERROR
    settings.LOGGING['loggers']['qrf_notification']['level'] = logging.WARNING
    if verbosity > 0:
        settings.LOGGING['loggers']['booksy']['level'] = logging.INFO
        settings.LOGGING['loggers']['celery']['level'] = logging.INFO
        settings.LOGGING['loggers']['qrf_notification']['level'] = logging.INFO

    ddtrace.tracer.enabled = False


def pytest_unconfigure(config):
    # delete environment variables after tests
    del os.environ['PYTEST']


def _execute_setup_sql():
    filename = './tests/test_db_setup.sql'
    path = os.path.join(os.path.dirname(__file__), filename)
    with open(path) as _file:
        connection.cursor().execute(_file.read().replace(r'%', r'%%'))


@pytest.fixture(scope='session')
def django_db_setup(django_db_setup, django_db_blocker, django_db_keepdb):
    # pylint: disable=unused-argument
    if not django_db_keepdb:
        with django_db_blocker.unblock():
            _execute_setup_sql()


@pytest.fixture(scope='session', autouse=True)
def elastic_index_session_setup(request):
    from cliapps.es.indices import (
        _logger as cliapps_logger,
        clean_indices,
        setup_indices,
    )
    from webapps.elasticsearch.tools import wait_for_elasticsearch_status, delete_unassigned_shards
    from webapps.elasticsearch.tools.logger import log as tools_logger

    verbosity = request.config.getoption('verbose')

    cliapps_logger.setLevel(logging.ERROR)
    tools_logger.setLevel(logging.WARNING)
    loggers_to_disable = [cliapps_logger, tools_logger]

    for logger in loggers_to_disable:
        logger.propagate = False

    if verbosity > 1:
        cliapps_logger.setLevel(logging.DEBUG)
        tools_logger.setLevel(logging.DEBUG)
        loggers_to_disable = []

    with disable_loggers(*loggers_to_disable):
        delete_unassigned_shards()
        clean_indices(force=True)

    setup_indices(raise_exception=True)
    wait_for_elasticsearch_status()

    yield

    # delete after tests session all
    with disable_loggers(cliapps_logger):
        clean_indices(force=True)


@pytest.fixture(scope='session', autouse=True)
def disable_annoying_task_or_signals():
    from lib.gcs_dataset.tools import create_google_client_mock

    to_disable = (
        # TODO use disable signal
        dict(
            target='webapps.user.tasks.business_customer_info.user_claim_bci_task',
        ),
        # end TODO
        dict(
            target='webapps.wait_list.tasks.waitlist_scenario_task',
        ),
        dict(
            # moved from disable_first_cb
            target='service.booking.appointments.first_cb',
        ),
        dict(
            target='django.core.files.storage.Storage.save',
            new=MagicMock(return_value="mocked-image.png"),
        ),
        dict(
            target='storages.backends.s3boto3.S3Boto3Storage.delete',
            new_callable=MagicMock(return_value=None),
        ),
        dict(
            target='django.core.files.images.ImageFile._get_image_dimensions',
            new=MagicMock(return_value=(50, 50)),
        ),
        dict(target='webapps.images.mixins.s3_client', new=MagicMock()),
        dict(target='webapps.images.tasks.s3_client', new=MagicMock()),
        dict(target='webapps.consents.tasks.render_consent_pdf', new=MagicMock()),
        dict(
            target=('webapps.user.elasticsearch.tools.append_to_user_search_data_fast_river'),
            new=MagicMock(),
        ),
        dict(
            target=('google.cloud.storage.Client.from_service_account_json'),
            new=create_google_client_mock(),
        ),
        dict(
            target='webapps.search_engine_tuning.tasks.update_user_tuning_task',
            new=MagicMock(),
        ),
        dict(
            target='stripe.Account.modify',
        ),
        dict(
            target='stripe.Customer.modify',
        ),
        dict(
            target='webapps.purchase.tasks.brain_tree.BraintreeFetchPlansTask',
            new=MagicMock(),
        ),
        dict(
            target='lib.pubsub_depr.publishers.publish',
            new=MagicMock(),
        ),
    )

    patchers = []
    try:
        for params in to_disable:
            patcher = patch(**params)
            patcher.start()
            patchers.append(patcher)
        yield
    finally:  # prevents potential error when raising an exception during patcher setup would not revert patch action  # noqa
        for patcher in patchers:
            patcher.stop()


def branch_io_func(url, json):
    # response depends on passed json, because we have different links for sms, blast, etc.
    url = '-'.join(
        filter(None, ('https://tdl.booksy.com/*********', json.get('channel'), json.get('feature')))
    )

    return MagicMock(
        status_code=200,
        json=MagicMock(return_value={'url': url}),
    )


@pytest.fixture(scope='session', autouse=True)
def deeplink_request_mock(request):
    # returned value is wrapped twice in Magic mock
    # because method returns partial object
    mocked_method = MagicMock(return_value=MagicMock(side_effect=branch_io_func))
    patcher = patch(
        'lib.deeplink.branchio.client.BranchIOClient.get_request_method',
        mocked_method,
    )
    patcher.start()
    # add special parameter to unittest.TestCase instances
    if hasattr(request, 'cls') and request.cls is not None:
        request.cls.deeplink_request_mock = mocked_method
    yield mocked_method
    patcher.stop()


def mocked_send_request(message, method_name):
    resp = []
    if not isinstance(message, BatchDeeplinkRequest):
        message = BatchDeeplinkRequest(deeplink_requests=[message])

    for msg in message.deeplink_requests:
        deeplink = '-'.join(
            filter(
                None,
                ('https://tdl.booksy.com/*********', msg.fields.channel, msg.fields.feature),
            )
        )
        fields = Fields(
            app_type=msg.fields.app_type,
            campaign=msg.fields.campaign,
            channel=msg.fields.channel,
            feature=msg.fields.feature,
        )
        mobile_deeplink = msg.fields.data.fields.get('mobile_deeplink', 'booksy.com')
        fields.data.update(
            {
                'mobile_deeplink': str(mobile_deeplink),
            }
        )

        resp.append(
            DeeplinkResponse(
                business_id=msg.business_id,
                country_code=msg.country_code,
                deeplink=deeplink,
                deeplink_type=msg.deeplink_type,
                entity_id=msg.entity_id,
                task_id=msg.task_id,
                fields=fields,
            )
        )

    return (
        MessageToDict(resp[0])
        if len(resp) == 1
        else MessageToDict(
            BatchDeeplinkResponse(deeplink_responses=resp),
            preserving_proto_field_name=True,
            including_default_value_fields=True,
        )
    )


@pytest.fixture(autouse=True)
def deeplink_grpc_request_mock(request):
    patcher = patch(
        'lib.deeplink.deeplinks_ms.grpc.clients.DeeplinkGRPCClient._send_request',
        mocked_send_request,
    )
    patcher.start()
    yield mocked_send_request
    patcher.stop()


# <editor-fold desc="elasticsearch fixtures">
@pytest.fixture(scope='module')
def clean_index_module_fixture():
    yield clean_elastisearch_index_helper


@pytest.fixture(scope='function')
def clean_index_function_fixture():
    yield clean_elastisearch_index_helper


@pytest.fixture(scope='class')
def clean_index_class_fixture():
    from webapps.elasticsearch.elastic import ELASTIC

    # make sure clean before other tests
    for index in ELASTIC.indices.values():
        delete_documents_from_index(index)
    yield
    for index in ELASTIC.indices.values():
        delete_documents_from_index(index)


@pytest.fixture(scope='module')
def change_index_language_module_fixture():
    yield change_index_language_settings


# </editor-fold>


@pytest.fixture(scope='package')
def patch_kill_switch_near_availability():
    patcher = patch(
        'webapps.business.searchables.business'
        '.sub_searchables.availability.AvailabilityESMixin.near_availability_on',
        MagicMock(return_value=True),
    )
    patcher.start()
    yield
    patcher.stop()


@pytest.fixture(scope='function')
def switch_on_new_login_fixture(django_db_blocker):
    from webapps.kill_switch.models import KillSwitch

    with django_db_blocker.unblock():
        switch = KillSwitch(
            name=KillSwitch.System.BOOKSY_AUTH,
            is_killed=False,
        )
        switch.save()
    yield
    with django_db_blocker.unblock():
        # delete kill switch for new login if were turnon
        KillSwitch.objects.filter(
            name=KillSwitch.System.BOOKSY_AUTH,
        ).delete()


def _get_pytest_current_test_name():
    return os.environ.get('PYTEST_CURRENT_TEST')


def _get_worker_cache() -> dict:
    cache = _cache_booksy_auth.setdefault(get_pytest_current_test_name(), {})
    return cache


def _gen_session_key() -> str:
    return f'mock_s_{uuid4().hex}'[:34]


def _make_request(method_name, request_class, data, **kwargs):
    from webapps.session.booksy_auth.pb2.auth_pb2 import CreateSessionResponse
    from webapps.session.booksy_auth.pb2.auth_pb2 import DeleteAllUserSessionsResponse
    from webapps.session.booksy_auth.pb2.auth_pb2 import LoginUserResponse
    from webapps.session.booksy_auth.pb2.auth_pb2 import LogoutUserResponse
    from webapps.session.booksy_auth.pb2.auth_pb2 import SessionExistsResponse
    from webapps.session.booksy_auth.pb2.auth_pb2 import SyncUserResponse

    from webapps.user.models import User

    cache = _get_worker_cache()
    match method_name:
        case 'login_user':
            session_key = _gen_session_key()
            u_id = User.objects.filter(email=data['email']).values_list('id', flat=True).last()
            cache[session_key] = {'country_user_id': u_id}
            return LoginUserResponse(
                account_exists=True,
                user_exists=True,
                country_user_id=u_id,
                session_key=session_key,
                expired=(datetime.now() + timedelta(days=2)).isoformat(),
            )
        case 'create_session':
            session_key = _gen_session_key()
            cache[session_key] = data
            return CreateSessionResponse(
                is_created=True,
                session_key=session_key,
                expired=(datetime.now() + timedelta(days=1)).isoformat(),
            )
        case 'sync_user':
            return SyncUserResponse(
                is_created=True,
                user_id=f"id-{data['user_data_with_sessions']['user_data']['country_user_id']}",
            )
        case 'delete_all_user_sessions':
            user_id = data['country_user_id']
            return DeleteAllUserSessionsResponse(
                session_keys=[s for s, d in cache.items() if d['country_user_id'] == user_id],
            )
        case 'session_exists':
            cached_data = cache.get(data['session_key'])
            return (
                SessionExistsResponse(
                    session_exists=True,
                    superuser_email=cached_data['superuser_email'],
                    country_user_id=cached_data['country_user_id'],
                    user_id='booksy_auth-user_uuid',
                    expired=(datetime.now() + timedelta(days=1)).isoformat(),
                    origin=cached_data['origin'],
                )
                if cached_data
                else SessionExistsResponse(session_exists=False)
            )
        case 'logout_user':
            return LogoutUserResponse(deleted=True)

    raise RuntimeError(f'{method_name} is not mocked!')


@pytest.fixture(autouse=True)
def patch_booksy_auth_client_make_request(request):
    marker = request.node.get_closest_marker('patch_booksy_auth_client_make_request')
    if marker and marker.kwargs.get('use') is False:
        yield
    else:
        patcher = patch(
            'webapps.session.booksy_auth.BooksyAuthClient._make_request',
            side_effect=_make_request,
            _mock_name='patch_booksy_auth_client_make_request',
        )
        patcher.start()
        yield
        patcher.stop()


@pytest.fixture(autouse=True)
def patch_booksy_auth_sync(request):
    # TODO remove when booksy_auth available in tests
    marker = request.node.get_closest_marker('patch_booksy_auth_sync')
    if marker and marker.kwargs.get('use') is False:
        yield
    else:
        sync_task_patched = MagicMock(delay=MagicMock(return_value="Do not sync users in tests"))
        patcher = patch(
            # why do I mock inner function. Look docs of function
            'webapps.user.tasks.sync._sync_user_booksy_auth_proxy',
            sync_task_patched,
        )
        patcher.start()
        yield
        patcher.stop()


@pytest.fixture(autouse=True)
def patch_segment_api_calls(request):
    marker = request.node.get_closest_marker('patch_segment_api_calls')

    if marker and marker.kwargs.get('use') is False:
        yield
    else:
        with patch('lib.segment_analytics.get_segment_api', autospec=True) as get_segment_api:
            yield get_segment_api


@pytest.fixture(autouse=True)
def patch_iterable_api_calls(request):
    marker = request.node.get_closest_marker('patch_iterable_api_calls')

    if marker and marker.kwargs.get('use') is False:
        yield
    else:
        with patch('lib.email.client.IterableClient._request_api', autospec=True) as _request_api:
            yield _request_api


@pytest.fixture(scope='module')
def celery_disabled():
    with patch('lib.celery_tools.celery_task'):
        yield
    # # doesnt work for user_claim_bci_task...
    # patch('lib.celery_tools.post_transaction_task').start()


@pytest.fixture
def volume_measures():
    from webapps.warehouse.models import VolumeMeasure
    from webapps.warehouse.volume_measures import STANDARD_VOLUME_MEASURES

    for vm_recipe in STANDARD_VOLUME_MEASURES:
        baker.make(VolumeMeasure, **vm_recipe)


@pytest.fixture
def cash_register():
    from webapps.register.models import Register
    from webapps.pos.models import POS, PaymentType
    from webapps.business.models import Business

    business = baker.make(Business)
    pos = baker.make(POS, business=business)
    baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.CASH)
    yield baker.make(Register, pos=pos, is_open=True, opening_cash=Decimal(0))


@pytest.fixture
def default_pos_fixture() -> 'POS':
    from webapps.pos.models import POS, PaymentType

    _default_pos = baker.make_recipe(
        'webapps.pos.default_pos_recipe',
    )

    baker.make_recipe(
        'webapps.pos.payment_type_cash_recipe',
        pos=_default_pos,
        default=True,
        enabled=True,
    )
    baker.make_recipe(
        'webapps.pos.payment_type_old_gift_card_recipe',
        pos=_default_pos,
        enabled=True,
    )
    baker.make_recipe(
        'webapps.pos.payment_type_voucher_recipe',
        pos=_default_pos,
        enabled=True,
    )
    baker.make_recipe(
        'webapps.pos.payment_type_subscription_card',
        pos=_default_pos,
        enabled=True,
    )
    baker.make_recipe(
        'webapps.pos.payment_type_square',
        pos=_default_pos,
        enabled=True,
    )

    yield _default_pos

    PaymentType.all_objects.all().delete()
    POS.all_objects.all().delete()


@pytest.fixture
def default_voucher_background() -> 'Image':
    from webapps.images.models import Image

    image = baker.make(
        Image,
        category=ImageTypeEnum.VOUCHER_GIFTCARD,
        image_url=DEFAULT_BACKGROUND_URL,
        width=688,
        height=433,
        business=None,
        tags=[],
        staffers=[],
    )
    yield image

    image.soft_delete()


@pytest.fixture(scope='class')
def clean_up_fixture(django_db_blocker):
    yield
    with django_db_blocker.unblock():
        connection.cursor().execute('SELECT clean_tables();')  # nosemgrep: bandit.B608


@pytest.fixture(scope='session', autouse=True)
def check_rollback(request, django_db_setup, django_db_blocker):
    # if fails please check whether your test methods removes objects correctly
    def check_objects_exists():
        from django.apps import apps

        with django_db_blocker.unblock():
            existing = []
            for model in apps.get_models():
                name = model.__name__
                if name in {'Permission', 'ContentType', 'Site', 'PermissionHelperModel'}:
                    continue
                if model.objects.exists():
                    existing.append(name)
            assert existing == []

    request.addfinalizer(check_objects_exists)


@pytest.fixture(scope='session', autouse=True)
def check_language_after_test_session():
    yield
    assert get_language()[:2] == settings.LANGUAGE_CODE[:2], 'language was changed, fix it!'


@pytest.fixture(scope='class', autouse=True)
def check_timezone_after_test_case():
    yield
    assert get_current_timezone_name() == settings.TIME_ZONE, 'timezone was changed, fix it!'


@pytest.fixture(scope='class', autouse=True)
def check_settings_after_tests():
    def get_simple_settings_dict():
        return {
            k: v
            for k, v in settings._wrapped.__dict__.items()
            if isinstance(k, str) and isinstance(v, (bool, int, float, str, tuple, list, set, dict))
        }

    old = get_simple_settings_dict()
    yield
    new = get_simple_settings_dict()
    assert old == new, 'settings were changed, fix it!'


@pytest.fixture(scope='session', autouse=True)
def prevent_http_requests(monkeypatch_session):
    s3_stub_fake_host = '***************'
    datadog_synth_tests_ips_fake_host = '10.0.0.0'

    allowed_hosts = {
        'elastic',
        'elasticsearch',
        s3_stub_fake_host,
        datadog_synth_tests_ips_fake_host,
    }
    original_urlopen = HTTPConnectionPool.urlopen

    def urlopen_mock(self, method, url, *args, **kwargs):
        if self.host in allowed_hosts:
            return original_urlopen(self, method, url, *args, **kwargs)
        pytest.fail(f'The test was about to {method} {self.scheme}://{self.host}{url}')

    monkeypatch_session.setattr('urllib3.connectionpool.HTTPConnectionPool.urlopen', urlopen_mock)


@pytest.fixture(scope='session', autouse=True)
def gcs_client_mock(request):
    """
    Mocks client to GCP storage.

    To use that mock you must import whole module: from lib import gcs.
    If class is imported directly (from lib.gcs import GCSClient) one needs to patch
    class in place of usage: patch('mymodule.GCSClient').
    More here: https://docs.python.org/3/library/unittest.mock.html#where-to-patch

    To mock single blob set return_value:
    gcs_client_mock.return_value.get_data = Mock(return_value=mock_storage)
    To mock multiple blobs set side_effect:
    gcs_client_mock.return_value.get_data = Mock(side_effect=mock_storage)
    """

    storage_patcher = patch(
        'lib.gcs.GCSClient',
        autospec=True,
    )
    storage_mock = storage_patcher.start()
    request.addfinalizer(storage_patcher.stop)
    return storage_mock


@pytest.fixture(scope='module', autouse=True)
def gcs_client_mock_for_unit_test(request, gcs_client_mock):
    """
    It's a simple workaround for using gcs_client_mock in TestCase based class, which add or update
    categories by webapps.business.business_categories.helpers.BusinessCategoriesUtil.

    Please, do not modify this method or contact with SearchTeam if necessary.

    To use this you have to add @pytest.mark.usefixtures("gcs_client_mock_for_unit_test") decorator
    to your TestCase based class. More: https://pytest.org/en/latest/how-to/unittest.html
    """
    request.gcs_client_mock = gcs_client_mock.return_value.get_data = Mock(return_value=bytes())


@pytest.fixture(scope='session', autouse=False)
def patch_ldclient():
    from lib.feature_flag.adapter import LaunchDarklyAdapter

    config = Config(sdk_key='fake_key', offline=True)
    ldclient.set_config(config)
    assert ldclient.get().is_initialized()

    with patch.object(
        LaunchDarklyAdapter, '_get_launch_darkly_client', ldclient.get
    ) as ldclient_factory_mock:
        yield ldclient_factory_mock


@pytest.fixture(scope='session', autouse=True)
def set_feature_flags_for_all_tests():
    with (
        override_feature_flag(
            {
                HCaptchaFlag.flag_name: False,
                HelpCenterFlag.flag_name: True,
                AnyResourceAssignedFlag.flag_name: True,
                CalendarChangedDatesRedisFlag.flag_name: True,
                ElasticsearchWithoutDeleteByQueryFlag.flag_name: True,
                EnablePayoutsCalendarBannerFlag: True,
                EnablePayoutsPaymentsSettingBannerFlag: True,
                CustomComboPriceChangeLogFlag.flag_name: True,
                CustomerQuickSignInUpFlag: True,
                CustomerSendPrivateEmail: True,
                RepeatingBookingDecoupling2: True,
                DisableParallelSameComboDrawer: True,
                UseNoReplyAddressInReplyToFieldFlag: True,
                SendPushAfterAppointmentCancellationFlag: True,
                DontSendDisclosureObligationAgreementEmailFlag: True,
                DoNotEvaluateBciQueryForFalsyUserEmailOrPhoneFlag: True,
                FifthCustomerBookingInFourteenDaysSegment: True,
                FifthCustomerBookingInFourteenDaysGTM: True,
                SubBookingComboChildrenPrefetch: True,
                NewParameterInBusinessDetailsLegalFootnoteVisible: True,
                FixErrorInReleaseDepositOnPaymentTask: True,
                FixRevenueForecastAlgorithm: True,
                MoreStrictSameServiceCheck.flag_name: True,
                FixMissingReminderForRepeatingBooking: True,
                ProfileAndSettingsTTPPromoFlag: True,
                DeeplinkToMyBooksyInBookingNotficiations: True,
                BooksyGiftcardsEnabledFlag: True,
            }
        ),
        override_eppo_feature_flag(
            {
                BooksyAuthTempExtraMetadataFlag.flag_name: True,
                BoostChargingForBusinessBookingsNoPromoFixFlag.flag_name: True,
                CustomerDontShowPrivateEmailFlag.flag_name: True,
                EnablePeakHoursFlag.flag_name: True,
                EnableSendingAdditionalBookingReminderFlag.flag_name: True,
                PreventResetCurrentStafferServicesFlag.flag_name: True,
                SimplifiedBookingTargeting.flag_name: True,
                SendCBStartedForCustomer.flag_name: True,
                InvokeBCIPostSaveInUpdateUserBCITask.flag_name: True,
                SessionAuthImprovementFlag.flag_name: True,
                IncentivizeWithGiftCardBackend.flag_name: 'November_28th',
                IncentivizeWithGiftCardStickyAssignmentSimulation.flag_name: True,
                DecoupledDryRunFromAPI.flag_name: True,
                UseSearchServiceForBusinessCategoryHintsExperiment.flag_name: ExperimentVariants.CONTROL,
                FixTotalPriceInAppointmentListReport.flag_name: True,
                DAC7PeselValidationFixFlag.flag_name: True,
                InvokeDecoupledDryRunFromSimplifiedBooking.flag_name: True,
                DontSendCustomerConfirmationSMSForBusinessBooking.flag_name: True,
                StreamlinedBookingAnalyticsWithSegment.flag_name: True,
                CalendarDragStafferUpdateFlag.flag_name: True,
                BookAgainUseReadOnlyDBFlag.flag_name: True,
                RemoveBookingCntInBulkFinishAppointments.flag_name: True,
                DSABusinessVersumNameFlag.flag_name: True,
                BoostFraudSuspicionWarningExperiment.flag_name: True,
                CustomerMyBooksySugestedForYouGalleryFlag.flag_name: {'is_required_user': False},
                RefactorSetChargeableTaskFlag.flag_name: True,
                UseNewAlgorithmForAdditionalTimeslotsExperiment.flag_name: True,
                AutomaticAccountLinkingFlag.flag_name: True,
                FixAutomaticAccountLinkingCellPhoneFlag.flag_name: True,
                UseNewAlgorithmForAdditionalTimeslots.flag_name: True,
                BoostUseExemptedInSetChargeableTaskFlag.flag_name: True,
                NotificationReceiversFlag.flag_name: True,
                SetNotificationReceiversFlag.flag_name: True,
                SelectedForYouSearchableV2Flag.flag_name: True,
                SelectedForYouExcludeBoostFlag.flag_name: True,
                VistedLikedSelected4UFlag.flag_name: {'iPhone': '1.0.0'},
                VistedLikedSelected4UExperiment.flag_name: ExperimentVariants.VARIANT_A,
                RecordBenchmarkTimeSlots.flag_name: True,
                MoveOptOutSuffixToSettingsFlag.flag_name: True,
                ShowEnforcePasswordResetFlag.flag_name: True,
            }
        ),
    ):
        yield


@pytest.fixture(autouse=True)
def skip_for_pubsub_emulator_missing(request):
    from webapps.pubsub.config import USE_EMULATOR

    if not USE_EMULATOR and request.node.get_closest_marker('pubsub_emulator'):
        pytest.skip('Pub/Sub Emulator is missing')


@pytest.fixture(scope='function')
def clean_otp_cache():
    cache_keys = cache.keys('otp_user:*')  # Adjust for your backend
    for key in cache_keys:
        cache.delete(key)


@pytest.fixture(autouse=True)
def mock_static_url_s3():
    with patch('settings.storage.StaticRootS3Boto3Storage.get_static_url_s3') as mock:
        mock.return_value = '/static/us/'
        yield mock


@pytest.fixture(scope='module')
def business(django_db_blocker):
    from webapps.business.baker_recipes import business_recipe, unmake_business_recipe

    with django_db_blocker.unblock():
        business = business_recipe.make()
        yield business
        unmake_business_recipe(business)
