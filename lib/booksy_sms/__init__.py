#!/usr/bin/env python
import functools
import logging
import typing as t
import uuid
from datetime import datetime, time, timedelta
from random import choice, randint

import holidays
import nptime
from dateutil import tz
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from country_config import DEFAULT_TIME_ZONES
from country_config.enums import Country
from lib.booksy_sms.devel import devel_send_sms
from lib.booksy_sms.enums import NOTIFICATION_CATEGORY_TO_SMS_GROUP_MAP, SmsGroup, SMSServiceStatus
from lib.booksy_sms.evox import evox_send_sms
from lib.booksy_sms.loggers.decorators import trace_send_sms
from lib.booksy_sms.result import SMSServiceResult
from lib.booksy_sms.routesms import routesms_send_sms
from lib.booksy_sms.smsapi import smsapi_send_sms
from lib.booksy_sms.telnyx import telnyx_send_sms
from lib.booksy_sms.twilio_sms import twilio_send_sms
from lib.booksy_sms.utils import (
    PhoneNumber,
    SMSCosts,
    parse_phone_number,
    phone_number_is_whitelisted,
)
from lib.booksy_sms.vonage import vonage_send_sms
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature import (
    ForceSmsReasonableHoursFlag,
    SmsMessagesOptOutSuffixFlag,
    SMSServiceNameFlag,
)
from lib.feature_flag.feature.notification import (
    InvitationSmsGroupFlag,
    MoveOptOutSuffixToSettingsFlag,
    MoveToEppoSMSServiceNameFlag,
    SaveSmsNotificationHistoryViaCeleryFlag,
    SMSMoveToSettingsInvitationProfileFlag,
    SMSServiceProviderFlag,
)
from lib.tools import quick_assert, tznow
from lib.unicode_utils import sms_dediacrit
from settings.sms import SMS_INVITE_PROFILE_COUNTRIES, SMS_OPT_OUT_SUFFIX_COUNTRIES
from webapps.kill_switch.models import KillSwitch
from webapps.notification.enums import (
    NotificationCategory,
    NotificationSendStatus,
    NotificationService,
)
from webapps.notification.utils import ELASTIC_TRANSPORT_ERRORS

_logger = logging.getLogger('booksy.sms')

CELL_PHONE_ERROR_BRAZIL = _('Phone number is not valid. Be sure to add dial code')
CELL_PHONE_ERROR_DEFAULT = _('Phone number is not valid')

CELL_PHONE_ERROR = (
    CELL_PHONE_ERROR_DEFAULT if settings.API_COUNTRY != Country.BR else CELL_PHONE_ERROR_BRAZIL
)

COUNTRIES_WITH_NO_PREFIX_IN_BLAST = {Country.CA, Country.US}


class BooksySmsException(Exception):
    pass


# There is no syntax to indicate keyword arguments
SMS_SERVICE = t.Callable[..., SMSServiceResult]

SMS_SERVICES_BY_NAME: t.Dict[NotificationService, SMS_SERVICE] = {
    NotificationService.SMSAPI: smsapi_send_sms,
    NotificationService.ROUTE: routesms_send_sms,
    NotificationService.TWILIO: twilio_send_sms,
    NotificationService.EVOX: evox_send_sms,
    NotificationService.DEVEL: devel_send_sms,
    NotificationService.TELNYX: telnyx_send_sms,
    NotificationService.VONAGE: vonage_send_sms,
}


def get_send_sms_service(parsed_phone: PhoneNumber, blast_id=None):
    """
    The service with which we send sms depends on recipient's country
    (SMS_SETTINGS_PER_COUNTRY dict) and Launch Darkly settings.

    For every country we have sms_services and a fallback_sms_service defined.
    If provider set in Launch Darkly does not exist in sms_services, fallback_sms_service is used.
    If the country is not found in the mapping, SMS_SETTINGS_DEFAULT is used.

    Each service has a function that is used to send sms - see SMS_SERVICES_BY_NAME
    dict. This function is then decorated with functools.partial that applies
    correct service settings as first argument to the function.
    """
    country = parsed_phone.country.lower()
    provider_name = SMSServiceNameFlag(
        UserData(
            country=country, custom={CustomUserAttributes.PHONE_NO: parsed_phone.global_short}
        ),
    )
    if MoveToEppoSMSServiceNameFlag():
        user_data = UserData(country=country)
        user_data.phone_no = parsed_phone.global_short
        provider_name = SMSServiceProviderFlag(user_data)

    country_sms_settings = settings.SMS_SETTINGS_PER_COUNTRY.get(country)
    if country_sms_settings is None:
        if settings.SMS_SETTINGS_DEFAULT is None:
            raise BooksySmsException(f'receiver country {country} is not supported')
        else:
            country_sms_settings = settings.SMS_SETTINGS_DEFAULT

    country_sms_services = country_sms_settings.get('sms_services')
    if country_sms_services is None:
        raise BooksySmsException(f'receiver country {country} has no sms_services')

    if provider_name not in country_sms_services:
        provider_name = country_sms_settings.get('fallback_sms_service')
        _logger.warning(f'using fallback sms provider {provider_name} for {country}')

    sms_service_settings = country_sms_services.get(provider_name)
    if not sms_service_settings:
        raise BooksySmsException(f'invalid sms service configuration set for country {country}')

    service_name = sms_service_settings['service']
    if service_name not in SMS_SERVICES_BY_NAME:
        raise BooksySmsException(f'unknown service {service_name}')

    return (
        service_name,
        functools.partial(
            SMS_SERVICES_BY_NAME[service_name],
            sms_service_settings,
        ),
    )


def can_send_sms_to_country(parsed_phone, blast_id=False):
    try:
        get_send_sms_service(parsed_phone, blast_id=blast_id)
        return True
    except BooksySmsException:
        return False


def check_recipient(to, history_data) -> SMSServiceResult:
    if to is None:
        send_status = SMSServiceResult(
            phone=None,
            status=SMSServiceStatus.ERROR,
            webhook_id=None,
            error_type='phone_number',
            error_details='phone number is None',
        )
        _logger.warning(send_status)
        return send_status
    if KillSwitch.alive(
        KillSwitch.System.SMS_WHITELIST_ENABLED
    ) and not phone_number_is_whitelisted(
        to, is_registration_code=is_registration_code(history_data)
    ):
        send_status = SMSServiceResult(
            phone=to,
            status=SMSServiceStatus.ERROR,
            webhook_id=None,
            error_type='phone_number',
            error_details='phone number is not whitelisted',
        )
        _logger.warning(send_status)
        return send_status


@trace_send_sms
def send_sms(
    to: str,  # cell_phone
    message: str,
    history_data: dict = None,
    dediacrit=None,
    fast=False,
    sms_deduction=0,
    blast_id=None,
) -> SMSServiceResult:
    from webapps.notification.models import NotificationHistory, UserNotification

    send_status = check_recipient(to, history_data)
    if send_status is not None:
        return send_status

    if history_data is None:
        history_data = {}

    if dediacrit is None:
        dediacrit = settings.SMS_DEDIACRIT

    if dediacrit:
        dediacrit_success, message = sms_dediacrit(message)

    to_original = to
    parsed_phone = parse_phone_number(to)
    to = parsed_phone.global_short

    if not parsed_phone.is_mobile:
        send_status = SMSServiceResult(
            phone=to,
            status=SMSServiceStatus.ERROR,
            webhook_id=None,
            error_type='phone_number',
            error_details='phone number is not mobile',
        )
        _logger.warning(send_status)
        return send_status
    business_id = history_data.get('business_id')
    if business_id:
        try:
            NotificationHistory._meta.get_field('business_id').run_validators(business_id)
        except ValidationError as e:
            send_status = SMSServiceResult(
                phone=to,
                status=SMSServiceStatus.ERROR,
                webhook_id=None,
                error_type='business_id',
                error_details=', '.join(e.messages),
                metadata=dict(business_id=business_id),
            )
            _logger.warning(send_status)
            return send_status
    try:
        service_name, service_code = get_send_sms_service(
            parsed_phone,
            blast_id=blast_id,
        )
    except BooksySmsException as e:
        send_status = SMSServiceResult(
            phone=to,
            status=SMSServiceStatus.ERROR,
            webhook_id=None,
            error_type='get_send_sms_service',
            error_details=str(e),
        )
        _logger.warning(send_status)
        return send_status

    country_settings = settings.SMS_SETTINGS_PER_COUNTRY.get(parsed_phone.country.lower(), {})
    message = f"{message}{country_settings.get('sms_sufix', '')}"
    if not (blast_id and settings.API_COUNTRY in COUNTRIES_WITH_NO_PREFIX_IN_BLAST):
        # add prefix to all but blast in US/CA
        message = f"{country_settings.get('sms_prefix', '')}{message}"

    # Assign sms group based on task_type
    NotificationHistory.guess_and_set_task_type(history_data)
    task_type = history_data.get('task_type')

    tasks_categories = [NotificationCategory.SMS_REGISTRATION_CODE]

    if task_type not in tasks_categories and SmsMessagesOptOutSuffixFlag(
        UserData(country=parsed_phone.country.lower())
    ):
        message += "\n" + _("Reply STOP to opt-out.")
    elif (
        task_type not in tasks_categories
        and parsed_phone.country.lower() in SMS_OPT_OUT_SUFFIX_COUNTRIES
        and MoveOptOutSuffixToSettingsFlag()
    ):
        message += "\n" + _("Reply STOP to opt-out.")

    if service_name in {
        NotificationService.EVOX,
        NotificationService.TELNYX,
        NotificationService.VONAGE,
    }:
        # We accept here task types from NotificationHistory
        # because NotificationCategory is copy of it
        message_type = NOTIFICATION_CATEGORY_TO_SMS_GROUP_MAP.get(task_type)

        if (
            task_type == NotificationCategory.INVITATION
            and not InvitationSmsGroupFlag()
        ):
            message_type = SmsGroup.SYSTEM
        elif (
            task_type == NotificationCategory.INVITATION
            and settings.API_COUNTRY not in SMS_INVITE_PROFILE_COUNTRIES
            and SMSMoveToSettingsInvitationProfileFlag()
        ):
            message_type = SmsGroup.SYSTEM

        if message_type:
            history_data['message_type'] = message_type.value

        if settings.API_COUNTRY == Country.FR and history_data.get('message_type') is None:
            send_status = SMSServiceResult(
                phone=to,
                status=SMSServiceStatus.ERROR,
                webhook_id=None,
                error_type='invalid_message_type',
                error_details=f"For {Country.FR} country, message type must be specified!",
                metadata=history_data,
            )
            _logger.warning(send_status)
            return send_status

    notification_history_data = dict(
        title=message,
        type=UserNotification.SMS_NOTIFICATION,
        blast_id=blast_id,
        recipient_phone=to,
        service=service_name.value,
        webhook_id=str(uuid.uuid4()),
        meta_to=to,
        meta_to_original=to_original,
    )

    notification_history_data.update(history_data)
    notification_history_data['meta_message_type'] = notification_history_data.pop(
        'message_type', None
    )

    send_status = service_code(
        phone_number=parsed_phone,
        message=message,
        fast=fast,
        metadata=history_data,
        webhook_id=notification_history_data['webhook_id'],
    )
    if send_status.status == SMSServiceStatus.SUCCESS:
        settle_sent_sms(
            to=to,
            send_status=send_status,
            history_data=notification_history_data,
            sms_deduction=sms_deduction,
        )

        notification_history_data.update(
            status=NotificationSendStatus.GATEWAY_SUCCESS,
            sender=notification_history_data.get('sender'),
            external_id=send_status.external_id,
            **{'meta_' + k: v for k, v in send_status.metadata.items()},
        )
        if SaveSmsNotificationHistoryViaCeleryFlag(
            UserData(custom={CustomUserAttributes.PHONE_NO: parsed_phone.global_short}),
        ):
            try:
                notification = NotificationHistory.add(
                    only_document=True,
                    **notification_history_data,
                )
                if notification.is_paid_sms():
                    NotificationHistory.from_document(notification)
            except ELASTIC_TRANSPORT_ERRORS:
                from webapps.notification.tasks.sms_history import save_sms_history_task

                _logger.exception(
                    'Elastic error occurs during sending sms id=%s',
                    blast_id,
                )
                save_sms_history_task.delay(
                    send_status=send_status.status,
                    history_data=notification_history_data,
                )
        else:
            notification = NotificationHistory.add(only_document=True, **notification_history_data)
            if notification.is_paid_sms():
                NotificationHistory.from_document(notification)

        _logger.info(send_status)
    else:
        # We save errors to ES only - see #74140
        error = dict(
            type=send_status.error_type,
            details=send_status.error_details,
            api_response=send_status.api_response,
        )
        notification_history_data.update(
            status=NotificationSendStatus.GATEWAY_ERROR,
            errors=[error],
        )
        if SaveSmsNotificationHistoryViaCeleryFlag(
            UserData(custom={CustomUserAttributes.PHONE_NO: parsed_phone.global_short}),
        ):
            try:
                NotificationHistory.add(only_document=True, **notification_history_data)
            except ELASTIC_TRANSPORT_ERRORS:
                from webapps.notification.tasks.sms_history import save_sms_history_task

                _logger.exception(
                    'Elastic error occurs during sending sms id=%s',
                    blast_id,
                )
                save_sms_history_task.delay(
                    history_data=notification_history_data,
                    send_status=send_status.status,
                )
        else:
            NotificationHistory.add(only_document=True, **notification_history_data)
        _logger.warning(send_status)

    return send_status


def settle_sent_sms(
    to: str,
    send_status: SMSServiceResult,
    history_data: dict,
    sms_deduction: int = 0,
):
    """Updates information about sms sender, costs, etc."""
    from webapps.notification.models import (
        NotificationHistory,
        NotificationSMSStatistics,
        UserNotification,
    )

    business_id = history_data.get('business_id')
    sms_parts = max(send_status.sms_parts - sms_deduction, 1)
    sms_cost = SMSCosts.sms_cost_calculate(sms_parts)
    history_data.update(
        sms_count=sms_parts,
        sms_cost=sms_cost,
    )
    from webapps.notification.tools import set_sms_notification_sender

    with transaction.atomic():
        # 48190
        # ACHTUNG! sender type may be overwritten here, based on task_type
        # or task_id
        set_sms_notification_sender(history_data, to)

        sms_type = history_data.get('type')
        sender = history_data.get('sender')
        if (
            business_id
            and sender == NotificationHistory.SENDER_BUSINESS
            and sms_type == UserNotification.SMS_NOTIFICATION
        ):
            NotificationSMSStatistics.upsert_for_business(business_id, sms_parts)


def generate_sms_registration_code(characters=None, length=None):
    if characters is None:
        characters = settings.SMS_REGISTRATION_CODE['characters']
    if length is None:
        length = settings.SMS_REGISTRATION_CODE['length']

    return ''.join(choice(characters) for _ in range(length))


def is_registration_code(history_data):
    return (
        history_data.get('task_id', '').split(':')[0] == 'sms_registration_code'
        if history_data
        else False
    )


def select_mobile_phone_number(phone_numbers):
    for phone_number in phone_numbers:
        parsed_phone_number = parse_phone_number(phone_number)
        if parsed_phone_number.is_mobile:
            return parsed_phone_number
    return None


def expand_sms_limit_history_do(sms_limit_history, dates):
    (now_year, now_month) = dates
    items = []
    for date, limit in list(sms_limit_history.items()):
        try:
            parts = date.split('-')
            items.append((int(parts[0]), int(parts[1]), int(limit)))
        except (ValueError, IndexError):
            pass

    items = sorted(items)

    while items and items[0][2] == 0:
        items.pop(0)

    if not items:
        return {}

    result = {}
    current_limit = 0
    month_from = items[0][0] * 12 + items[0][1] - 1
    month_till = now_year * 12 + now_month

    for i in range(month_from, month_till):
        # int division give float in python3
        i_year, i_month = i // 12, i % 12 + 1
        if items and items[0][:2] == (i_year, i_month):
            _, _, current_limit = items.pop(0)
        result['%.4d-%.2d' % (i_year, i_month)] = current_limit

    return result


def expand_sms_limit_postpaid_history(business):
    now = business.tznow
    return expand_sms_limit_history_do(business.sms_limit_history, (now.year, now.month))


def expand_sms_prepaid_history(business):
    now = business.tznow
    return expand_sms_limit_history_do(
        settings.SMS_PAID_ACCOUNT_PREPAID_HISTORY, (now.year, now.month)
    )


@functools.lru_cache(maxsize=1)
def _get_phone_blacklist() -> t.Set[PhoneNumber]:
    return set((parse_phone_number(phone) for phone in settings.SMS_REGISTRATION_BLACKLIST))


def cell_phone_blacklisted(cell_phone: PhoneNumber) -> bool:
    """
    Args:
        cell_phone: parsed cell phone

    Returns:
        True if cell phone is blacklisted, otherwise False
    """
    return cell_phone in _get_phone_blacklist()


def is_cell_phone_valid(cell_phone):
    """
    Args:
        cell_phone: cell phone

    Returns:
        True if cell phone is valid and not blacklisted, otherwise False
    """
    cell_phone = parse_phone_number(cell_phone)
    blacklisted = cell_phone_blacklisted(cell_phone)

    return cell_phone.is_valid and not blacklisted


def validate_cell_phone(cell_phone, field_name='cell_phone'):
    cell_phone = parse_phone_number(cell_phone)

    if settings.API_COUNTRY == Country.AR:
        error_message = _(
            "Write down your cell phone number with the city code "
            "(example for Buenos Aires 011) then 15 and your number"
        )
    else:
        error_message = _("We don't recognize this number. Add your country code and try again.")

    quick_assert(
        cell_phone.is_valid,
        ('not_valid', 'validation', field_name),
        error_message,
    )

    blacklisted = cell_phone_blacklisted(cell_phone)
    quick_assert(
        not blacklisted,
        ('not_valid', 'validation', field_name),
        _('This phone number is blocked.'),
    )

    return cell_phone


def get_reasonable_send_datetime(
    country: t.Optional[str] = None,
    timezone: str = 'UTC',
    send_at: t.Optional[datetime] = None,
    till_night_shift: t.Optional[int] = 0,
) -> datetime:
    """
    Calculates reasonable hour for sending sms.

    Cases:
      - At night, till midnight: get tommorow's night end time + max 120 mins
      - At night, after midnight: get today's night end time + max 120 mins
      - At daytime: get now + max 5 mins, but before night start

    :returns: reasonable sms send datetime
    :rtype: datetime.datetime
    """
    country = country or settings.API_COUNTRY
    timezone = timezone or DEFAULT_TIME_ZONES.get(country) or 'UTC'
    send_at = send_at or tznow().astimezone(tz.gettz(timezone))

    if settings.LIVE_DEPLOYMENT or settings.PYTEST or ForceSmsReasonableHoursFlag():
        # get night and day boundaries
        night_adjust = settings.SMS_SETTINGS_PER_COUNTRY.get(country, {}).get(
            'marketing_night_adjust',
            settings.SMS_SETTINGS_DEFAULT['marketing_night_adjust'],
        )

        night_start_split = night_adjust['night_start'].split(':')
        night_end_split = night_adjust['night_end'].split(':')

        night_start = time(
            hour=int(night_start_split[0]),
            minute=int(night_start_split[1]),
        )
        night_end = time(
            hour=int(night_end_split[0]),
            minute=int(night_end_split[1]),
        )

        after_night_spread = 2 * 60 * 60  # two hours
        during_the_day_spread = 5 * 60  # five minutes

        # handle night
        if night_start < send_at.time() or send_at.time() < night_end:
            add_day = 1 if night_start < send_at.time() else 0
            send_at = datetime.combine(send_at, night_end).replace(tzinfo=send_at.tzinfo)
            send_at = send_at + timedelta(days=add_day, seconds=randint(0, after_night_spread))

        # handle day
        else:
            till_night = (
                datetime.combine(send_at, night_start).replace(tzinfo=send_at.tzinfo)
            ) - send_at
            add_seconds = randint(0, min([during_the_day_spread, till_night.seconds]))
            send_at += timedelta(seconds=add_seconds)

        # handle weekends
        if weekend_adjust := settings.SMS_SETTINGS_PER_COUNTRY.get(country, {}).get(
            'marketing_days_exclude',
            settings.SMS_SETTINGS_DEFAULT['marketing_days_exclude'],
        ):
            if send_at.weekday() in weekend_adjust:
                send_at = get_reasonable_send_datetime(
                    country, timezone, send_at + timedelta(days=1)
                )

        # handle holidays
        if settings.SMS_SETTINGS_PER_COUNTRY.get(country, {}).get(
            'marketing_public_holidays_adjust',
            settings.SMS_SETTINGS_DEFAULT['marketing_public_holidays_adjust'],
        ):
            if send_at.date() in holidays.country_holidays(country.upper()):
                send_at = get_reasonable_send_datetime(
                    country, timezone, send_at + timedelta(days=1)
                )

        if till_night_shift:
            latest_hour_to_send = nptime.nptime.from_time(night_start) - timedelta(
                minutes=till_night_shift
            )
            if latest_hour_to_send <= send_at.time() <= night_start:
                send_at = get_reasonable_send_datetime(
                    country,
                    timezone,
                    send_at.combine(
                        send_at,
                        nptime.nptime.from_time(night_start) + timedelta(minutes=1),  # force night
                    ).replace(tzinfo=send_at.tzinfo),
                )

    return send_at.astimezone(tz.gettz('UTC'))


def get_reasonable_send_datetime_optim_invite(business):
    """
    Calculates reasonable hour for sending sms
     at invite_optimization experiment

    :returns: reasonable sms send datetime
    :rtype: datetime.datetime
    """
    random_seconds = 60 * 5
    send_at = business.tznow

    best_hours_by_weekdays = settings.SMS_INVITE_BEST_HOURS_BY_ISOWEEKDAYS
    sent_at_day_key = send_at.isoweekday()
    preferred_hours = best_hours_by_weekdays[sent_at_day_key]
    sent_at_hour = send_at.hour

    if sent_at_hour in preferred_hours:
        preferred_time = send_at
    elif sent_at_hour < max(preferred_hours):  # send later today
        possible_hours = [hour for hour in preferred_hours if hour > sent_at_hour]
        new_sent_hour = min(possible_hours)
        # correct hour
        preferred_time = send_at.replace(hour=new_sent_hour, minute=0)
    else:  # send next day
        sent_at_day_key = sent_at_day_key + 1 if sent_at_day_key < 7 else 1
        preferred_hours = best_hours_by_weekdays[sent_at_day_key]
        preferred_time = send_at.replace(
            day=send_at.day + 1,
            hour=min(preferred_hours),
            minute=0,
        )

    preferred_time = preferred_time + timedelta(seconds=randint(0, random_seconds * 2))
    return preferred_time.astimezone(tz.gettz('UTC'))


def get_reasonable_send_datetime_sms(
    country=None,
    timezone='UTC',
    business=None,
):
    return get_reasonable_send_datetime(
        country=country,
        timezone=timezone,
    )
