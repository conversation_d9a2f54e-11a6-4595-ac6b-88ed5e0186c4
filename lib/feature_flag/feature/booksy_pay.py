from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, DictFlag


class BooksyPayCashbackPromoFlag(BooleanFlag):
    flag_name = 'Feature_BooksyPayCashbackPromo'
    adapter = FeatureFlagAdapter.EPPO


class BooksyPayFlag(BooleanFlag):
    flag_name = 'Feature_BooksyPay'
    adapter = FeatureFlagAdapter.EPPO


class BusinessBooksyPayOnboardingFlag(BooleanFlag):
    flag_name = 'Feature_BusinessBooksyPayOnboarding'
    adapter = FeatureFlagAdapter.EPPO


class BusinessBooksyPayOnboardingXVersionCompatibilityFlag(DictFlag):
    """
    Expected format (see the BookingSources model):
    ```
    {
        "<BookingSources.app_type>": {
            "<BookingSources.name>": "<app_version>",
        }
    }
    ```

    E.g.:
    ```
    {
        "B": {
            "Android": "3.28.1_634",
            "iPhone": "3.28.3"
        }
    }
    ```

    IMPORTANT:
        Note that `BookingSources` are case-sensitive (both app types and source names).
        Make sure to check the expected value prior to configuring the flag.
    """

    flag_name = 'Feature_BusinessBooksyPayOnboardingXVersionCompatibility'
    adapter = FeatureFlagAdapter.EPPO


class ReadOnlyDBBooksyPayAttentionGetterViewFlag(BooleanFlag):
    flag_name = 'Feature_ReadOnlyDBBooksyPayAttentionGetterViewFlag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyPayAvailabilityOnBookingFlag(BooleanFlag):
    flag_name = 'Feature_BooksyPayAvailabilityOnBookingFlag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyPayAvailabilityV2Flag(BooleanFlag):
    flag_name = 'Feature_BooksyPayAvailabilityV2Flag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyPayRefundOnCxCancellationFlag(BooleanFlag):
    flag_name = 'Feature_BooksyPayRefundOnCxCancellation'
    adapter = FeatureFlagAdapter.EPPO


class BooksyPayRefundShortStatusFlag(BooleanFlag):
    flag_name = 'Feature_BooksyPayRefundShortStatusFlag'
    adapter = FeatureFlagAdapter.EPPO
